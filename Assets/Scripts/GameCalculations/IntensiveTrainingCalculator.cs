using Assets.Scripts.Enums;
using Assets.Scripts.Managers;
using Assets.Scripts.Scriptables;
using Assets.Scripts.Scriptables.Traits;
using Assets.Scripts.SerializeDataStruct;
using Assets.Scripts.SerializeDataStruct.Traits;
using UnityEngine;

using System.Collections.Generic;
using Assets.Scripts.DataStruct;

namespace Assets.Scripts.GameCalculations
{
    public class IntensiveTrainingCalculator : MonoBehaviour
    {
        private const float lifespanConsume = -10;//decrease by week instead

        private void Start()
        {
            TrainingActionManager.OnCalculationBegin += CalculateTrainingAction;
            TrainingActionManager.OnCalculationSimulated += CalculateTrainingActionSimulation;
        }

        private void OnDestroy()
        {
            TrainingActionManager.OnCalculationBegin -= CalculateTrainingAction;
            TrainingActionManager.OnCalculationSimulated -= CalculateTrainingActionSimulation;
        }

        private void CalculateTrainingAction(SerializeMonster monsterInfo, SerializeFarm farmInfo, BasicBattleParameterEnum trainingParameter, TrainingActionEnums trainingActionPerform, MonsterFoodScriptable selectedFood, bool isPremium, MapAreaScriptable trainingMap, int ticketRank1, int ticketRank2, int ticketRank3, int repeatTime)
        {
            if (trainingActionPerform != TrainingActionEnums.IntensiveTraining) return;

            if (monsterInfo == null || farmInfo == null)
            {
                Debug.LogError("Training monster or farm is null");
                return;
            }

            #region Debug
            SerializeDataStruct.Data.DebugDataCollection.InitDebug();
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"{monsterInfo.MonsterName} intensive training");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Terrain {trainingMap.AreaName}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Monster Personal {monsterInfo.MonsterPersonality.PersonalityName}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine("Monster traits");
            foreach (MonsterInnateTrait trait in monsterInfo.InnateTraits)
            {
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"{trait.TraitName}");
            }
            #endregion

            int mainTerrainStat = TrainingParameterValues.Get_MonsterTerrainCompability(monsterInfo.MonsterScriptableData.MonsterMainTerrainComp, monsterInfo.MonsterScriptableData.MonsterMainTerrainIncomp, trainingMap.TerrainType);
            int subTerrainStat = TrainingParameterValues.Get_MonsterTerrainCompability(monsterInfo.MonsterScriptableData.MonsterSubTerrainComp, monsterInfo.MonsterScriptableData.MonsterSubTerrainIncomp, trainingMap.TerrainType);

            SerializeGrowthParameters growthTrainingParaValue = new();
            SerializeGrowthParameters growthTrainingPara = new();
            var increaseBasicParameter = new SerializeBasicParameters();
            var trainingOutcome = new SerializeBasicParameters();
            InjuryTypesEnum finalInjury = InjuryTypesEnum.None;
            DiseasesTypesEnum finalDisease = DiseasesTypesEnum.None;
            InjuryTypesEnum trainingInjury = InjuryTypesEnum.None;
            DiseasesTypesEnum trainingDisease = DiseasesTypesEnum.None;
            TrainingResultTypesEnum trainingResultType = TrainingResultTypesEnum.Success;
            int passedTime = 0;
            List<SerializeDataStruct.Data.TrainingDataRecordings> trainingDataRecordings = new();
            for (int i = 0; i < 4; i++)
            {
                passedTime++;
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"\n---Training week {passedTime}---");
                if (SingleTraining(monsterInfo, mainTerrainStat, subTerrainStat, growthTrainingParaValue, out growthTrainingPara, out trainingInjury, out trainingDisease, out trainingResultType, selectedFood, isPremium))
                {
                    //only hospital can cure this
                    if (trainingDisease != DiseasesTypesEnum.None)
                    {
                        finalDisease = trainingDisease;
                        monsterInfo.SetDiseaseStatus(trainingDisease);
                    }

                    if (trainingInjury != InjuryTypesEnum.None)
                    {
                        finalInjury = trainingInjury;
                        monsterInfo.SetInjuryStatus(trainingInjury);
                    }
                    #region Add to template value
                    TrainingActionManager.BasicPClampValue(SerializeBasicParameters.CombineValue(monsterInfo.ComplexMonsterBasicParameter, increaseBasicParameter), trainingOutcome);
                    TrainingActionManager.TrainingPClampValue(SerializeGrowthParameters.CombineValues(monsterInfo.ComplexMonsterTrainingParameter, growthTrainingParaValue), growthTrainingPara);
                    // continue if monster not seriously sick or seriously injured
                    growthTrainingParaValue.AddStress(growthTrainingPara.Stress);
                    growthTrainingParaValue.AddFatigue(growthTrainingPara.Fatigue);
                    growthTrainingParaValue.AddFriendship(growthTrainingPara.Friendship);
                    growthTrainingParaValue.AddTrainingPolicy(growthTrainingPara.TrainingPolicy);
                    growthTrainingParaValue.AddLifespan(growthTrainingPara.Lifespan);
                    growthTrainingParaValue.AddBody(growthTrainingPara.Body);
                    growthTrainingParaValue.AddEnergy(growthTrainingPara.Energy);
                    growthTrainingParaValue.AddCondition(growthTrainingPara.Condition);
                    growthTrainingParaValue.AddPhysical(growthTrainingPara.Physical);
                    trainingOutcome = TrainingActionManager.CalculateParameterRise_IntensiveTraining(monsterInfo: monsterInfo,
                                                                                                     trainingGrowth: growthTrainingParaValue,
                                                                                                     mainTerrainStat: mainTerrainStat,
                                                                                                     subTerrainStat: subTerrainStat,
                                                                                                     injury: monsterInfo.MonsterInjuryCondition,
                                                                                                     disease: monsterInfo.MonsterDiseasesCondition,
                                                                                                     trainingResultType: trainingResultType,
                                                                                                     trainingMap: trainingMap);

                    trainingOutcome = TrainingActionManager.ClampTrainingParameter(monsterInfo.ComplexMonsterBasicParameter, increaseBasicParameter, trainingOutcome);
                    increaseBasicParameter.AddHealth(trainingOutcome.Health);
                    increaseBasicParameter.AddStrength(trainingOutcome.Strength);
                    increaseBasicParameter.AddIntelligent(trainingOutcome.Intelligent);
                    increaseBasicParameter.AddDexterity(trainingOutcome.Dexterity);
                    increaseBasicParameter.AddAgility(trainingOutcome.Agility);
                    increaseBasicParameter.AddVitality(trainingOutcome.Vitality);
                    #endregion
                    if (i < 3)
                    {
                        trainingDataRecordings.Add(new(monsterInfo.MonsterId, SerializeBasicParameters.CombineValue(monsterInfo.ComplexMonsterBasicParameter, increaseBasicParameter), trainingOutcome, SerializeGrowthParameters.CombineValues(monsterInfo.ComplexMonsterTrainingParameter, growthTrainingParaValue), growthTrainingPara, monsterInfo.MonsterInjuryCondition, monsterInfo.MonsterDiseasesCondition, trainingResultType, TrainingActionEnums.IntensiveTraining, 1, trainingMap.TrainingAnimations[i], trainingMap, null, null, 0, trainingInjury, trainingDisease));
                    }

                    continue;
                }

                TrainingActionManager.BasicPClampValue(SerializeBasicParameters.CombineValue(monsterInfo.ComplexMonsterBasicParameter, increaseBasicParameter), trainingOutcome);
                TrainingActionManager.TrainingPClampValue(SerializeGrowthParameters.CombineValues(monsterInfo.ComplexMonsterTrainingParameter, growthTrainingParaValue), growthTrainingPara);
                //only hospital can cure this
                if (trainingDisease != DiseasesTypesEnum.None)
                {
                    finalDisease = trainingDisease;
                    monsterInfo.SetDiseaseStatus(trainingDisease);
                }

                if (trainingInjury != InjuryTypesEnum.None)
                {
                    finalInjury = trainingInjury;
                    monsterInfo.SetInjuryStatus(trainingInjury);
                }

                //stop on seriously sick or seriously ill
                growthTrainingParaValue.AddStress(growthTrainingPara.Stress);
                growthTrainingParaValue.AddFatigue(growthTrainingPara.Fatigue);
                growthTrainingParaValue.AddFriendship(growthTrainingPara.Friendship);
                growthTrainingParaValue.AddTrainingPolicy(growthTrainingPara.TrainingPolicy);
                growthTrainingParaValue.AddLifespan(growthTrainingPara.Lifespan);
                growthTrainingParaValue.AddBody(growthTrainingPara.Body);
                growthTrainingParaValue.AddEnergy(growthTrainingPara.Energy);
                growthTrainingParaValue.AddCondition(growthTrainingPara.Condition);
                growthTrainingParaValue.AddPhysical(growthTrainingPara.Physical);
                trainingOutcome = TrainingActionManager.CalculateParameterRise_IntensiveTraining(monsterInfo: monsterInfo,
                                                                                                 trainingGrowth: growthTrainingParaValue,
                                                                                                 mainTerrainStat: mainTerrainStat,
                                                                                                 subTerrainStat: subTerrainStat,
                                                                                                 injury: monsterInfo.MonsterInjuryCondition,
                                                                                                 disease: monsterInfo.MonsterDiseasesCondition,
                                                                                                 trainingResultType: trainingResultType,
                                                                                                 trainingMap: trainingMap);

                increaseBasicParameter.AddHealth(trainingOutcome.Health);
                increaseBasicParameter.AddStrength(trainingOutcome.Strength);
                increaseBasicParameter.AddIntelligent(trainingOutcome.Intelligent);
                increaseBasicParameter.AddDexterity(trainingOutcome.Dexterity);
                increaseBasicParameter.AddAgility(trainingOutcome.Agility);
                increaseBasicParameter.AddVitality(trainingOutcome.Vitality);
                break;
            }

            #region Apply values
            TrainingActionManager.BasicPClampValue(monsterInfo, increaseBasicParameter);

            monsterInfo.MonsterAlteredBasicP.AddHealth(increaseBasicParameter.Health);
            monsterInfo.MonsterAlteredBasicP.AddStrength(increaseBasicParameter.Strength);
            monsterInfo.MonsterAlteredBasicP.AddIntelligent(increaseBasicParameter.Intelligent);
            monsterInfo.MonsterAlteredBasicP.AddDexterity(increaseBasicParameter.Dexterity);
            monsterInfo.MonsterAlteredBasicP.AddAgility(increaseBasicParameter.Agility);
            monsterInfo.MonsterAlteredBasicP.AddVitality(increaseBasicParameter.Vitality);

            TrainingActionManager.TrainingPClampValue(monsterInfo, growthTrainingParaValue);

            monsterInfo.MonsterAlteredTrainingP.AddFatigue(growthTrainingParaValue.Fatigue);
            monsterInfo.MonsterAlteredTrainingP.AddStress(growthTrainingParaValue.Stress);
            monsterInfo.MonsterAlteredTrainingP.AddPhysical(growthTrainingParaValue.Physical);
            monsterInfo.MonsterAlteredTrainingP.AddTrainingPolicy(growthTrainingParaValue.TrainingPolicy);
            monsterInfo.MonsterAlteredTrainingP.AddFriendship(growthTrainingParaValue.Friendship);
            monsterInfo.MonsterAlteredTrainingP.AddBody(growthTrainingParaValue.Body);
            monsterInfo.MonsterAlteredTrainingP.AddCondition(growthTrainingParaValue.Condition);
            monsterInfo.MonsterAlteredTrainingP.AddEnergy(growthTrainingParaValue.Energy);
            monsterInfo.MonsterAlteredTrainingP.AddLifespan(growthTrainingParaValue.Lifespan);

            //SerializeTrainingResult trainingSessionResult = new SerializeTrainingResult(increaseBasicParameter, growthTrainingParaValue, finalInjury, finalDisease, passedTime, trainingResultType);
            //monsterInfo.SetTrainingResult(trainingSessionResult);
            SerializeMonsterSkillLevel skillLevel = new();
            List<SerializeAcquireTraitLevel> traitLearn = new();
            if (passedTime == 4 && (trainingInjury != InjuryTypesEnum.SeriouslyInjured || trainingDisease != DiseasesTypesEnum.Illness))
            {
                traitLearn = AcquiredTraitCalculation(monsterInfo, trainingMap);
                skillLevel = SkillUpCalculation(monsterInfo, trainingMap);
            }
            #endregion
            SerializeDataStruct.Data.DebugDataCollection.PrintDebug();
            monsterInfo.MonsterAge += passedTime;
            GameProgressManager.Instance.AddWeekNoCallback(passedTime);
            trainingDataRecordings.Add(new(monsterInfo.MonsterId, monsterInfo.ComplexMonsterBasicParameter, trainingOutcome, monsterInfo.ComplexMonsterTrainingParameter, growthTrainingPara, trainingInjury, trainingDisease, trainingResultType, TrainingActionEnums.IntensiveTraining, 1, trainingMap.TrainingAnimations[3], trainingMap, traitLearn, skillLevel.SkillId, skillLevel.SkillLevel, finalInjury, finalDisease));
            TrainingActionManager.OnCalculationEnd?.Invoke(trainingDataRecordings.ToArray());
            GameProgressManager.Instance.AddIntensiveSession();
        }

        private void CalculateTrainingActionSimulation(SerializeMonster monsterInfo, SerializeFarm farmInfo, BasicBattleParameterEnum basicParameterTraining, TrainingActionEnums trainingAction, MonsterFoodScriptable selectedFood, MapAreaScriptable trainingMap, bool isPremium, int repeatTime)
        {
            if (trainingAction != TrainingActionEnums.IntensiveTraining || monsterInfo == null || farmInfo == null) return;
            DebugPopup.InTraining(false);
            SerializeMonster outputMonsterInfo = new SerializeMonster(monsterInfo);

            int mainTerrainStat = TrainingParameterValues.Get_MonsterTerrainCompability(monsterInfo.MonsterScriptableData.MonsterMainTerrainComp, monsterInfo.MonsterScriptableData.MonsterMainTerrainIncomp, trainingMap.TerrainType);
            int subTerrainStat = TrainingParameterValues.Get_MonsterTerrainCompability(monsterInfo.MonsterScriptableData.MonsterSubTerrainComp, monsterInfo.MonsterScriptableData.MonsterSubTerrainIncomp, trainingMap.TerrainType);

            SerializeGrowthParameters growthTrainingParaValue = new();
            InjuryTypesEnum trainingInjury = InjuryTypesEnum.None;
            DiseasesTypesEnum trainingDisease = DiseasesTypesEnum.None;
            TrainingResultTypesEnum trainingResultType = TrainingResultTypesEnum.Success;
            int passedTime = 0;
            var trainingParameter = new SerializeBasicParameters();
            for (int i = 0; i < 4; i++)
            {
                passedTime++;
                SingleTraining(monsterInfo, mainTerrainStat, subTerrainStat, growthTrainingParaValue, out SerializeGrowthParameters growthTrainingPara, out trainingInjury, out trainingDisease, out trainingResultType, selectedFood, false);

                growthTrainingParaValue.AddStress(growthTrainingPara.Stress);
                growthTrainingParaValue.AddFatigue(growthTrainingPara.Fatigue);
                growthTrainingParaValue.AddFriendship(growthTrainingPara.Friendship);
                growthTrainingParaValue.AddTrainingPolicy(growthTrainingPara.TrainingPolicy);
                growthTrainingParaValue.AddLifespan(growthTrainingPara.Lifespan);
                growthTrainingParaValue.AddBody(growthTrainingPara.Body);
                growthTrainingParaValue.AddEnergy(growthTrainingPara.Energy);
                growthTrainingParaValue.AddCondition(growthTrainingPara.Condition);
                var trainingOutcome = TrainingActionManager.CalculateParameterRise_IntensiveTraining(monsterInfo: monsterInfo,
                                                                                               trainingGrowth: growthTrainingParaValue,
                                                                                               mainTerrainStat: mainTerrainStat,
                                                                                               subTerrainStat: subTerrainStat,
                                                                                               injury: monsterInfo.MonsterInjuryCondition,
                                                                                               disease: monsterInfo.MonsterDiseasesCondition,
                                                                                               trainingResultType: trainingResultType,
                                                                                               trainingMap: trainingMap);
                trainingParameter.AddHealth(trainingOutcome.Health);
                trainingParameter.AddStrength(trainingOutcome.Strength);
                trainingParameter.AddIntelligent(trainingOutcome.Intelligent);
                trainingParameter.AddDexterity(trainingOutcome.Dexterity);
                trainingParameter.AddAgility(trainingOutcome.Agility);
                trainingParameter.AddVitality(trainingOutcome.Vitality);
            }

            TrainingActionManager.BasicPClampValue(monsterInfo, trainingParameter);
            outputMonsterInfo.MonsterAlteredBasicP.AddHealth(trainingParameter.Health);
            outputMonsterInfo.MonsterAlteredBasicP.AddStrength(trainingParameter.Strength);
            outputMonsterInfo.MonsterAlteredBasicP.AddIntelligent(trainingParameter.Intelligent);
            outputMonsterInfo.MonsterAlteredBasicP.AddDexterity(trainingParameter.Dexterity);
            outputMonsterInfo.MonsterAlteredBasicP.AddAgility(trainingParameter.Agility);
            outputMonsterInfo.MonsterAlteredBasicP.AddVitality(trainingParameter.Vitality);
            //outputMonsterInfo.LastTrainingResult.SetTrainingTime(4);
            //outputMonsterInfo.LastTrainingResult.BasicParameterChanges.SetHealth(trainingParameter.Health);
            //outputMonsterInfo.LastTrainingResult.BasicParameterChanges.SetStrength(trainingParameter.Strength);
            //outputMonsterInfo.LastTrainingResult.BasicParameterChanges.SetIntelligent(trainingParameter.Intelligent);
            //outputMonsterInfo.LastTrainingResult.BasicParameterChanges.SetDexterity(trainingParameter.Dexterity);
            //outputMonsterInfo.LastTrainingResult.BasicParameterChanges.SetAgility(trainingParameter.Agility);
            //outputMonsterInfo.LastTrainingResult.BasicParameterChanges.SetVitality(trainingParameter.Vitality);

            //outputMonsterInfo.LastTrainingResult.GrowthParameterChanges.SetFatigue(0);
            //outputMonsterInfo.LastTrainingResult.GrowthParameterChanges.SetStress(0);
            //outputMonsterInfo.LastTrainingResult.GrowthParameterChanges.SetPhysical(0);
            //outputMonsterInfo.LastTrainingResult.GrowthParameterChanges.SetTrainingPolicy(0);
            //outputMonsterInfo.LastTrainingResult.GrowthParameterChanges.SetFriendship(0);
            //outputMonsterInfo.LastTrainingResult.GrowthParameterChanges.SetBody(0);
            //outputMonsterInfo.LastTrainingResult.GrowthParameterChanges.SetCondition(0);
            //outputMonsterInfo.LastTrainingResult.GrowthParameterChanges.SetEnergy(0);
            //outputMonsterInfo.LastTrainingResult.GrowthParameterChanges.SetLifespan(0);
            //outputMonsterInfo.LastTrainingResult.SetTrainingResult(TrainingResultTypesEnum.Success);
            TrainingActionManager.OnSimulationCalculationFinish?.Invoke(outputMonsterInfo);
        }

        private bool SingleTraining(SerializeMonster monsterInfo, int mainTerrainStat, int subTerrainStat, SerializeGrowthParameters growthValue, out SerializeGrowthParameters weekResultTraining, out InjuryTypesEnum injury, out DiseasesTypesEnum disease, out TrainingResultTypesEnum trainingResultType, MonsterFoodScriptable selectedFood, bool isPremium)
        {
            weekResultTraining = new();

            #region Apply food value
            weekResultTraining.AddEnergy(-5 + (isPremium ? selectedFood.FoodPremiumEnergyValue : selectedFood.FoodEnergyValue));
            weekResultTraining.AddBody(-5 + (isPremium ? selectedFood.FoodPremiumBodyValue : selectedFood.FoodBodyValue));
            weekResultTraining.AddCondition(-5 + (isPremium ? selectedFood.FoodPremiumConditionValue : selectedFood.FoodConditionValue));

            weekResultTraining.AddFatigue((isPremium ? selectedFood.FatiguePremiumValue : selectedFood.FatigueValue));
            weekResultTraining.AddStress(isPremium ? selectedFood.StressPremiumValue : selectedFood.StressValue);
            weekResultTraining.AddFriendship(isPremium ? selectedFood.FriendshipPremiumValue : selectedFood.FriendshipValue);
            weekResultTraining.AddTrainingPolicy((isPremium ? selectedFood.PolicyPremiumValue : selectedFood.PolicyValue) - 1);
            weekResultTraining.AddPhysical(isPremium ? selectedFood.PhysicalPremiumValue : selectedFood.PhysicalValue);

            if (monsterInfo.LikeMeal == selectedFood)
            {
                weekResultTraining.AddFatigue(-1);
                weekResultTraining.AddStress(-1);
                weekResultTraining.AddTrainingPolicy(TrainingParameterValues.Get_TrainingPolicyConditionValue(0));
                weekResultTraining.AddFriendship(TrainingParameterValues.Get_FriendshipConditionValue(0));
            }
            else if (monsterInfo.DislikeMeal == selectedFood)
            {
                weekResultTraining.AddTrainingPolicy(TrainingParameterValues.Get_TrainingPolicyConditionValue(1));
                weekResultTraining.AddFriendship(TrainingParameterValues.Get_FriendshipConditionValue(1));
            }
            #endregion

            #region Debug
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Food selected = {selectedFood.FoodName}");
            if (isPremium)
            {
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Energy(E) {selectedFood.FoodPremiumEnergyValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Body(B) {selectedFood.FoodPremiumBodyValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Condition(C) {selectedFood.FoodPremiumConditionValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Physical {selectedFood.PhysicalPremiumValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Fatigue {selectedFood.FatiguePremiumValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Stress {selectedFood.StressPremiumValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Friendship {selectedFood.FriendshipPremiumValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Policy {selectedFood.PolicyPremiumValue}");
            }
            else
            {
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Energy(E) {selectedFood.FoodEnergyValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Body(B) {selectedFood.FoodBodyValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Condition(C) {selectedFood.FoodConditionValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Physical body {selectedFood.PhysicalValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Fatigue {selectedFood.FatigueValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Stress {selectedFood.StressValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Friendship {selectedFood.FriendshipValue}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Policy {selectedFood.PolicyValue}");
            }
            #endregion

            weekResultTraining.AddPhysical(TrainingParameterValues.Get_ActionPerformBasePhysicalBuild(1));

            #region Faituge
            float fatigueTraitCorrection = monsterInfo.InnateTraitCombineValues.TrainingFatigueReduction;
            float trainingFatigue = TrainingActionManager.Get_FatigueCaculatedIncrease(TrainingParameterValues.Get_ActionPerformBaseFatigue(0),
                                                                                       0,
                                                                                       mainTerrainStat,
                                                                                       subTerrainStat,
                                                                                       fatigueTraitCorrection,
                                                                                       weekResultTraining.Physical + monsterInfo.ComplexPhysical + growthValue.Physical,
                                                                                       weekResultTraining.TrainingPolicy + monsterInfo.ComplexTrainingPolicy + growthValue.TrainingPolicy);
            weekResultTraining.AddFatigue(trainingFatigue);
            FatigueTypesEnum fatigueType = TrainingParameterValues.FatigueTypeConvert(weekResultTraining.Fatigue + monsterInfo.ComplexFatigue + growthValue.Fatigue);
            #endregion

            #region Stress
            float stressTraitCorrection = monsterInfo.InnateTraitCombineValues.TrainingStressReduction;
            float trainingStress = TrainingActionManager.Get_StressCalculatedIncrease(TrainingParameterValues.Get_ActionPerformBaseStress(0),
                                                                                      0,
                                                                                      mainTerrainStat,
                                                                                      subTerrainStat,
                                                                                      stressTraitCorrection,
                                                                                      weekResultTraining.Physical + monsterInfo.ComplexPhysical + growthValue.Physical,
                                                                                      weekResultTraining.TrainingPolicy + monsterInfo.ComplexTrainingPolicy + growthValue.TrainingPolicy);
            weekResultTraining.AddStress(trainingStress);
            StressTypesEnum stressType = TrainingParameterValues.StressTypeConvert(weekResultTraining.Stress + monsterInfo.ComplexStress + growthValue.Stress);
            #endregion

            #region Injury
            float injuryChance = TrainingParameterValues.Get_InjuriesChanceFromTrainingResult(2);
            float injuyryTraitCorrection = monsterInfo.InnateTraitCombineValues.TrainingInjuryChance_Add;

            Vector2 injuryRate = TrainingActionManager.Get_InjuryProbabilityRate(injuryChance, injuyryTraitCorrection, fatigueType, 0);
            injury = TrainingActionManager.Get_CalculatedInjury(monsterInfo.MonsterInjuryCondition, injuryRate.x, injuryRate.y);
            #endregion

            #region Disease
            float numberOfSickmonster = 0;
            float diseaseTraitCorrection = monsterInfo.InnateTraitCombineValues.TrainingDiseaseChance_Add;

            Vector2 diseaseRate = TrainingActionManager.Get_DiseaseProbabilityRate(weekResultTraining.Energy + monsterInfo.ComplexEnergy + growthValue.Energy, weekResultTraining.Body + monsterInfo.ComplexBody + growthValue.Body, weekResultTraining.Condition + monsterInfo.ComplexCondition + growthValue.Condition, numberOfSickmonster, diseaseTraitCorrection, stressType);
            disease = TrainingActionManager.Get_RandomDisease(monsterInfo.MonsterDiseasesCondition, diseaseRate.x, diseaseRate.y);
            #endregion

            weekResultTraining.AddLifespan(TrainingActionManager.Get_LifeSpanConsumption(1, lifespanConsume, fatigueType, stressType));
            switch (injury)
            {
                case InjuryTypesEnum.Injured:
                    weekResultTraining.AddLifespan(TrainingParameterValues.Get_LifeSpanDecreaseValue(4));
                    break;
                case InjuryTypesEnum.SeriouslyInjured:
                    weekResultTraining.AddLifespan(TrainingParameterValues.Get_LifeSpanDecreaseValue(5));
                    break;
            }
            switch (disease)
            {
                case DiseasesTypesEnum.Sick:
                    weekResultTraining.AddLifespan(TrainingParameterValues.Get_LifeSpanDecreaseValue(6));
                    break;
                case DiseasesTypesEnum.Illness:
                    weekResultTraining.AddLifespan(TrainingParameterValues.Get_LifeSpanDecreaseValue(7));
                    break;
            }
            trainingResultType = TrainingActionManager.Get_TrainingResultChance(trainingPolicyValue: weekResultTraining.TrainingPolicy + monsterInfo.ComplexTrainingPolicy,
                                                                                friendshipValue: weekResultTraining.Friendship + monsterInfo.ComplexFriendship,
                                                                                traitHugeSuccessCorrection: monsterInfo.InnateTraitCombineValues.TrainingSuccessChance_Add,
                                                                                traitFailureCorrection: monsterInfo.InnateTraitCombineValues.TrainingFailChance_Add,
                                                                                injury: monsterInfo.MonsterInjuryCondition,
                                                                                diseases: monsterInfo.MonsterDiseasesCondition);
            return injury != InjuryTypesEnum.SeriouslyInjured && disease != DiseasesTypesEnum.Illness;
        }

        List<AcquireTraitScriptable> GetAcquireTraitLearnedList(SerializeMonster monsterInfo, MapAreaScriptable trainingArea, SerializeBasicParameters monsterComplexParameter, float traitCorrection)
        {
            List<AcquireTraitScriptable> learnList = new();

            foreach (AcquireTraitScriptable acquiredTrait in trainingArea.AccquireTraits)
            {
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Caculate Acquired Trait = {acquiredTrait.TraitName}");
                int level = 0;
                foreach (SerializeAcquireTraitLevel serializeAcquireTraitLevel in monsterInfo.MonsterAcquireTraitLevel)
                {
                    if (serializeAcquireTraitLevel.TraitScriptable.TraitId == acquiredTrait.TraitId)
                    {
                        level = serializeAcquireTraitLevel.Level;
                        break;
                    }
                }

                float rate = TrainingActionManager.Get_Intensive_TraitAcquisition_LevelUpRate(monsterComplexParameter, trainingArea, acquiredTrait.TraitInfo.RequireParamter, traitCorrection, level, monsterInfo.MonsterInjuryCondition, monsterInfo.MonsterDiseasesCondition);
                SerializeDataStruct.Data.DebugDataCollection.AppendLine("Acquired Trait up chance");
                float ranNum = UnityEngine.Random.Range(0, 100);
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Random chance = {ranNum / 100f}");
                if (ranNum / 100f <= rate && !learnList.Contains(acquiredTrait))
                    learnList.Add(acquiredTrait);
            }

            return learnList;
        }

        private List<SerializeAcquireTraitLevel> AcquiredTraitCalculation(SerializeMonster monsterInfo, MapAreaScriptable trainingArea)
        {
            float traitCorrection = monsterInfo.InnateTraitCombineValues.TrailtUpChance_Add;
            List<AcquireTraitScriptable> optainTraits = GetAcquireTraitLearnedList(monsterInfo, trainingArea, monsterInfo.ComplexMonsterBasicParameter, traitCorrection);
            List<SerializeAcquireTraitLevel> levelUpTrait = new();
            for (int i = 0; i < optainTraits.Count; i++)
            {
                var traitLevelUp = monsterInfo.AddTrait(optainTraits[i], 1);
                levelUpTrait.Add(traitLevelUp);
            }
            return levelUpTrait;
        }

        private SerializeMonsterSkillLevel SkillUpCalculation(SerializeMonster monsterInfo, MapAreaScriptable trainingArea)
        {
            SkillDataScriptable learningSkillScriptable = null;
            for (int i = 0; i < monsterInfo.MonsterScriptableData.SkillRegister.Count; i++)
            {
                if (monsterInfo.MonsterScriptableData.SkillRegister[i].LearningArea.Equals(trainingArea))
                {
                    learningSkillScriptable = monsterInfo.MonsterScriptableData.SkillRegister[i];
                    break;
                }
            }
            if (learningSkillScriptable == null)
            {
                Debug.LogWarning("No skill id found");
                return new();
            }
            int currentSkillLevel = 0;
            if (monsterInfo.MonsterSkillLevelDict.TryGetValue(learningSkillScriptable.SkillId, out var skillData))
            {
                currentSkillLevel = skillData.SkillLevel;
            }
            if (currentSkillLevel == learningSkillScriptable.SkillDetail.Count)
            {
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Skill = {learningSkillScriptable.SkillName}");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine("Skill level is maxed out");
                return new();
            }
            int nextLevel = Mathf.Clamp(currentSkillLevel + 1, 0, learningSkillScriptable.SkillDetail.Count);
            if (monsterInfo.MonsterRank < learningSkillScriptable.SkillDetail[currentSkillLevel].SkillRank)
            {
                Debug.Log("Can't level up skill due to rank not met");
                return new();
            }
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Skill = {learningSkillScriptable.SkillName}");

            float levelUpChance = TrainingActionManager.SkillLevelUpChanceCalculation(monsterInfo, learningSkillScriptable, monsterInfo.InnateTraitCombineValues.SkillUpChance_Add, nextLevel);

            float randomValue = UnityEngine.Random.Range(0, 100);

            if (randomValue <= levelUpChance * 100)
            {
                monsterInfo.LevelUpSkill(learningSkillScriptable.SkillId, nextLevel);
            }
            else
            {
                nextLevel = 0;
            }
            SerializeDataStruct.Data.DebugDataCollection.AppendLine("Skill Up Chance");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Level Up Chance = {levelUpChance * 100}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Radom Chance = {randomValue}");
            return new(learningSkillScriptable.SkillName, nextLevel);
        }
    }
}