using Assets.Scripts.Enums;
using Assets.Scripts.Managers;
using Assets.Scripts.Scriptables;
using Assets.Scripts.SerializeDataStruct;
using UnityEngine;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using Assets.Scripts.Scriptables.Traits;
using Assets.Scripts.DataStruct;
using I2.Loc;
using Assets.Scripts.Controllers;
using Assets.Scripts.Scriptables.Item;

namespace Assets.Scripts.GameCalculations
{
    public class ExplorationCalculator : MonoBehaviour
    {
        private const int actionTime = 4;
        private const float lifespanConsume = -15;

        private void Start()
        {
            TrainingActionManager.OnCalculationBegin += CalculateTrainingAction;
            TrainingActionManager.OnCalculationSimulated += CalculateTrainingActionSimulated;
        }

        private void OnDestroy()
        {
            TrainingActionManager.OnCalculationBegin -= CalculateTrainingAction;
            TrainingActionManager.OnCalculationSimulated -= CalculateTrainingActionSimulated;
        }

        private void CalculateTrainingAction(SerializeMonster monsterInfo, SerializeFarm farmInfo, BasicBattleParameterEnum trainingParameter, TrainingActionEnums trainingActionPerform, MonsterFoodScriptable selectedFood, bool isPremium, MapAreaScriptable trainingMap, int ticketRank1, int ticketRank2, int ticketRank3, int repeatTime)
        {
            if (trainingActionPerform != TrainingActionEnums.Exploration) return;

            if (monsterInfo == null || farmInfo == null)
            {
                Debug.LogError($"Training monster or farm must not be null!");
                return;
            }
            SerializeDataStruct.Data.DebugDataCollection.InitDebug();
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"{monsterInfo.MonsterName} exploration");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Terrain {trainingMap.AreaName}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Monster Personal {monsterInfo.MonsterPersonality.PersonalityName}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine("Monster traits");
            foreach (MonsterInnateTrait trait in monsterInfo.InnateTraits)
            {
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"{trait.TraitName}");
            }

            var loading = BackendLoadData.Instance.LoadingCanvas("CalculateTrainingAction");
            SerializeGrowthParameters growthTrainingParaValue = new();
            SerializeBasicParameters combineTrainingValues = new();
            SerializeTrainingResult trainingResult = new();
            SerializeGrowthParameters trainingGrowthPass = new();

            int mainTerrainStat = TrainingParameterValues.Get_MonsterTerrainCompability(monsterInfo.MonsterScriptableData.MonsterMainTerrainComp, monsterInfo.MonsterScriptableData.MonsterMainTerrainIncomp, trainingMap.TerrainType);
            int subTerrainStat = TrainingParameterValues.Get_MonsterTerrainCompability(monsterInfo.MonsterScriptableData.MonsterSubTerrainComp, monsterInfo.MonsterScriptableData.MonsterSubTerrainIncomp, trainingMap.TerrainType);

            bool isMonsterLost = false;
            TrainingResultTypesEnum trainingResultType = TrainingResultTypesEnum.Success;
            InjuryTypesEnum trainingInjury = InjuryTypesEnum.None;
            DiseasesTypesEnum trainingDisease = DiseasesTypesEnum.None;
            int passedTime = 0;
            int lostTime = 0;
            int performTime = actionTime;

            List<SerializeExplorationItem> explorationItems = new List<SerializeExplorationItem>();
            List<SerializeDataStruct.Data.TrainingDataRecordings> trainingDataRecordings = new();

            List<SerializeExplorationItem> itemList = new();
            for (int i = 0; i < performTime; i++)
            {
                passedTime++;

                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"\n---Explore week {passedTime}---");

                if (SingleTraining(monsterInfo, mainTerrainStat, subTerrainStat, growthTrainingParaValue, out trainingGrowthPass, out trainingInjury, out trainingDisease, out isMonsterLost, selectedFood, isPremium))
                {
                    TrainingActionManager.TrainingPClampValue(SerializeGrowthParameters.CombineValues(monsterInfo.ComplexMonsterTrainingParameter,growthTrainingParaValue),trainingGrowthPass);
                    growthTrainingParaValue.AddStress(trainingGrowthPass.Stress);
                    growthTrainingParaValue.AddFatigue(trainingGrowthPass.Fatigue);
                    growthTrainingParaValue.AddFriendship(trainingGrowthPass.Friendship);
                    growthTrainingParaValue.AddPhysical(trainingGrowthPass.Physical);
                    growthTrainingParaValue.AddTrainingPolicy(trainingGrowthPass.TrainingPolicy);
                    growthTrainingParaValue.AddLifespan(trainingGrowthPass.Lifespan);
                    growthTrainingParaValue.AddBody(trainingGrowthPass.Body);
                    growthTrainingParaValue.AddEnergy(trainingGrowthPass.Energy);
                    growthTrainingParaValue.AddCondition(trainingGrowthPass.Condition);

                    if (isMonsterLost)
                        lostTime++;
                    if (performTime >= actionTime && isMonsterLost)
                    {
                        performTime++;
                    }

                    // Caculate Item discovery
                    //only hospital can cure this
                    if (trainingDisease != DiseasesTypesEnum.None)
                        monsterInfo.SetDiseaseStatus(trainingDisease);
                    if (trainingInjury != InjuryTypesEnum.None)
                        monsterInfo.SetInjuryStatus(trainingInjury);
                    itemList.Clear();
                    itemList = GetExplorationItemFound(monsterInfo, monsterInfo.MonsterInjuryCondition, monsterInfo.MonsterDiseasesCondition, monsterInfo.ComplexMonsterBasicParameter, trainingMap, mainTerrainStat, subTerrainStat, ticketRank1, ticketRank2, ticketRank3);
                    explorationItems.AddRange(itemList);
                    trainingResultType = TrainingResultTypesEnum.Failure;
                    foreach (var item in itemList)
                    {
                        switch (item.discoveryRank)
                        {
                            case 1:
                                trainingResultType = TrainingResultTypesEnum.Success;
                                break;
                            case 2:
                            case 3:
                                trainingResultType = TrainingResultTypesEnum.HugeSuccess;
                                break;
                        }
                    }
                    if (i < performTime - 1)
                    {
                        trainingDataRecordings.Add(new(monsterInfo.MonsterId, SerializeBasicParameters.CombineValue(monsterInfo.ComplexMonsterBasicParameter, new()), new(), SerializeGrowthParameters.CombineValues(monsterInfo.ComplexMonsterTrainingParameter, growthTrainingParaValue), trainingGrowthPass, monsterInfo.MonsterInjuryCondition, monsterInfo.MonsterDiseasesCondition, trainingResultType, TrainingActionEnums.Exploration, 1, BasicBattleParameterEnum.HP, trainingMap, null, null, 0, trainingInjury, trainingDisease));
                    }
                    continue;
                }
                TrainingActionManager.TrainingPClampValue(SerializeGrowthParameters.CombineValues(monsterInfo.ComplexMonsterTrainingParameter, growthTrainingParaValue), trainingGrowthPass);
                #region On fail for injury and sickness
                growthTrainingParaValue.AddStress(trainingGrowthPass.Stress);
                growthTrainingParaValue.AddFatigue(trainingGrowthPass.Fatigue);
                growthTrainingParaValue.AddFriendship(trainingGrowthPass.Friendship);
                growthTrainingParaValue.AddPhysical(trainingGrowthPass.Physical);
                growthTrainingParaValue.AddTrainingPolicy(trainingGrowthPass.TrainingPolicy);
                growthTrainingParaValue.AddLifespan(trainingGrowthPass.Lifespan);
                growthTrainingParaValue.AddBody(trainingGrowthPass.Body);
                growthTrainingParaValue.AddEnergy(trainingGrowthPass.Energy);
                growthTrainingParaValue.AddCondition(trainingGrowthPass.Condition);
                //only hospital can cure this
                if (trainingDisease != DiseasesTypesEnum.None)
                    monsterInfo.SetDiseaseStatus(trainingDisease);
                if (trainingInjury != InjuryTypesEnum.None)
                    monsterInfo.SetInjuryStatus(trainingInjury);
                // Caculate Item discovery
                itemList.Clear();
                itemList = GetExplorationItemFound(monsterInfo, monsterInfo.MonsterInjuryCondition, monsterInfo.MonsterDiseasesCondition, monsterInfo.ComplexMonsterBasicParameter, trainingMap, mainTerrainStat, subTerrainStat, ticketRank1, ticketRank2, ticketRank3);
                explorationItems.AddRange(itemList);
                trainingResultType = TrainingResultTypesEnum.Failure;
                foreach (var item in itemList)
                {
                    switch (item.discoveryRank)
                    {
                        case 1:
                            trainingResultType = TrainingResultTypesEnum.Success;
                            break;
                        case 2:
                        case 3:
                            trainingResultType = TrainingResultTypesEnum.HugeSuccess;
                            break;
                    }
                }
                break;
                #endregion
            }

            #region Apply values
            SerializeBasicParameters increaseExploreParameter = new(trainingMap.ExploreIncreaseParameter);
            if(passedTime >= performTime)
            {
                TrainingActionManager.BasicPClampValue(monsterInfo, increaseExploreParameter);

                monsterInfo.MonsterAlteredBasicP.AddHealth(increaseExploreParameter.Health);
                monsterInfo.MonsterAlteredBasicP.AddStrength(increaseExploreParameter.Strength);
                monsterInfo.MonsterAlteredBasicP.AddIntelligent(increaseExploreParameter.Intelligent);
                monsterInfo.MonsterAlteredBasicP.AddDexterity(increaseExploreParameter.Dexterity);
                monsterInfo.MonsterAlteredBasicP.AddAgility(increaseExploreParameter.Agility);
                monsterInfo.MonsterAlteredBasicP.AddVitality(increaseExploreParameter.Vitality);
            }
            TrainingActionManager.TrainingPClampValue(monsterInfo, growthTrainingParaValue);

            monsterInfo.MonsterAlteredTrainingP.AddFatigue(growthTrainingParaValue.Fatigue);
            monsterInfo.MonsterAlteredTrainingP.AddStress(growthTrainingParaValue.Stress);
            monsterInfo.MonsterAlteredTrainingP.AddPhysical(growthTrainingParaValue.Physical);
            monsterInfo.MonsterAlteredTrainingP.AddTrainingPolicy(growthTrainingParaValue.TrainingPolicy);
            monsterInfo.MonsterAlteredTrainingP.AddFriendship(growthTrainingParaValue.Friendship);
            monsterInfo.MonsterAlteredTrainingP.AddBody(growthTrainingParaValue.Body);
            monsterInfo.MonsterAlteredTrainingP.AddCondition(growthTrainingParaValue.Condition);
            monsterInfo.MonsterAlteredTrainingP.AddEnergy(growthTrainingParaValue.Energy);
            monsterInfo.MonsterAlteredTrainingP.AddLifespan(growthTrainingParaValue.Lifespan);

            // Item Discovery
            var itemFoundList = AddItems(explorationItems, trainingMap);
            float score = ScoreController.CalculateExplorationScore(trainingMap, itemFoundList);
            Dictionary<string, int> itemCount = new();
            for(int i = 0; i < itemFoundList.Count; i++)
            {
                if (itemCount.ContainsKey(itemFoundList[i].ItemId))
                    itemCount[itemFoundList[i].ItemId]++;
                else
                    itemCount.Add(itemFoundList[i].ItemId, 1);
            }

            //SerializeTrainingResult trainingSessionResult = new SerializeTrainingResult(increaseExploreParameter, growthTrainingParaValue, trainingInjury, trainingDisease, passedTime, trainingResultType);
            //monsterInfo.SetTrainingResult(trainingSessionResult);
            #endregion
            Destroy(loading);
            GameProgressManager.Instance.AddExploreSession();
            SerializeBasicParameters increaseBasicP = new();
            monsterInfo.MonsterAge += performTime;
            if (passedTime >= performTime)
            {
                increaseBasicP = new SerializeBasicParameters(increaseExploreParameter);
                TrainingActionManager.BasicPClampValue(monsterInfo, increaseBasicP);
            }
            trainingDataRecordings.Add(new(monsterInfo.MonsterId, monsterInfo.ComplexMonsterBasicParameter, increaseBasicP, monsterInfo.ComplexMonsterTrainingParameter, trainingGrowthPass, monsterInfo.MonsterInjuryCondition, monsterInfo.MonsterDiseasesCondition, trainingResultType, TrainingActionEnums.Exploration, 1, BasicBattleParameterEnum.HP, trainingMap, null, null, 0, trainingInjury, trainingDisease));
            //TrainingActionManager.OnCalculationFinish?.Invoke(monsterInfo);
            TrainingActionManager.OnExploreItemFound(itemFoundList);
            GameProgressManager.Instance.AddWeekNoCallback(passedTime);
            SerializeDataStruct.Data.DebugDataCollection.PrintDebug();
            ScoreManager.AddUserScoreRecord?.Invoke(new DataStruct.Data.SerializeUserScoreRecord(string.Empty, GameDataManager.Instance.LoadedPlayerData.PlayerWallet, score, 0, System.DateTime.UtcNow.Date, true, 1));
            TrainingActionManager.OnCalculationEnd?.Invoke(trainingDataRecordings.ToArray());
        }

        private List<ScriptableItem> AddItems(List<SerializeExplorationItem> explorationItems, MapAreaScriptable trainingMap)
        {
            SerializeDataStruct.Data.DebugDataCollection.AppendLine("Map Items");
            List<ScriptableItem> aTypeItem, bTypeItem, cTypeItem, foundItems;
            GetItemTypeCount(trainingMap, out aTypeItem, out bTypeItem, out cTypeItem, out foundItems);

            SerializeDataStruct.Data.DebugDataCollection.AppendLine("Get true items found");
            List<SerializeDataStruct.ItemData.SerializeItem> mintItemList = new();
            foreach (SerializeExplorationItem item in explorationItems)
            {
                if (item.itemType == SerializeExplorationItem.ItemType.a && aTypeItem.Count > 0)
                {
                    mintItemList.Add(new(aTypeItem[item.discoveryRank - 1], 1));
                    foundItems.Add(aTypeItem[item.discoveryRank - 1]);

                    SerializeDataStruct.Data.DebugDataCollection.AppendLine($"True item found = {aTypeItem[item.discoveryRank - 1].name}");
                }
                else if (item.itemType == SerializeExplorationItem.ItemType.b && bTypeItem.Count > 0)
                {
                    mintItemList.Add(new(bTypeItem[item.discoveryRank - 1], 1));
                    foundItems.Add(bTypeItem[item.discoveryRank - 1]);

                    SerializeDataStruct.Data.DebugDataCollection.AppendLine($"True item found = {bTypeItem[item.discoveryRank - 1].name}");
                }
                else if (item.itemType == SerializeExplorationItem.ItemType.c && cTypeItem.Count > 0)
                {
                    mintItemList.Add(new(cTypeItem[item.discoveryRank - 1], 1));
                    foundItems.Add(cTypeItem[item.discoveryRank - 1]);

                    SerializeDataStruct.Data.DebugDataCollection.AppendLine($"True item found = {cTypeItem[item.discoveryRank - 1].name}");
                }
            }

            GameProgressManager.Instance.UpdateTotalItemFounds(foundItems.Count);
            if (mintItemList.Count > 0)
            {
                try
                {
                    Dictionary<Scriptables.Item.ScriptableItem, int> itemDict = new();
                    foreach (var item in mintItemList)
                    {
                        if (itemDict.ContainsKey(item.Item))
                        {
                            itemDict[item.Item]++;
                        }
                        else
                        {
                            itemDict[item.Item] = 1;
                        }
                    }
                    mintItemList = new();
                    foreach (var key in itemDict.Keys)
                    {
                        mintItemList.Add(new(key, 1));
                    }
                    Controllers.GameDataController.AddItemBalance(mintItemList, itemDict.Values.ToList(),"exploration");
                }
                catch (System.Exception ex)
                {
                    UI.Utilities.UINotifyManager.AddNotifyResponse("explore item error", () =>
                    {
                        var msg = LocalizationManager.CurrentLanguageCode == "ja" ? "アイテムをミントできません。" : "Can't mint item";
                        UI.UIPopupNotify.Instance.SetNotify("Error", $"{msg}\n{ex.Message}", UI.Utilities.UINotifyManager.ProcessNotify);
                    }, UI.Utilities.UINotifyManager.NotifyType.System);
                }
            }
            return foundItems;
        }

        private static void GetItemTypeCount(MapAreaScriptable trainingMap, out List<ScriptableItem> aTypeItem, out List<ScriptableItem> bTypeItem, out List<ScriptableItem> cTypeItem, out List<ScriptableItem> foundItems)
        {
            aTypeItem = new();
            bTypeItem = new();
            cTypeItem = new();
            foundItems = new();
            for (int i = 0; i < trainingMap.ExploreItem.Count; i++)
            {
                Scriptables.Item.ScriptableItem item = trainingMap.ExploreItem[i];
                int itemIndex = i;
                SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Item = {item.name}");

                if (i < 3)
                {
                    aTypeItem.Add(item);
                    continue;
                }
                else if (i < 6)
                {
                    bTypeItem.Add(item);
                    continue;
                }
                else
                {
                    cTypeItem.Add(item);
                    continue;
                }
            }
        }

        private void CalculateTrainingActionSimulated(SerializeMonster monsterInfo, SerializeFarm farmInfo, BasicBattleParameterEnum trainingParameter, TrainingActionEnums trainingActionPerform, MonsterFoodScriptable selectedFood, MapAreaScriptable trainingMap, bool isPremium, int repeatTime)
        {
            if (trainingActionPerform != TrainingActionEnums.Exploration || monsterInfo == null || farmInfo == null) return;
            DebugPopup.InTraining(false);

            SerializeMonster outputMonsterInfo = new SerializeMonster(monsterInfo);

            //outputMonsterInfo.LastTrainingResult.SetTrainingTime(4);
            SerializeBasicParameters increaseExploreParameter = new(trainingMap.ExploreIncreaseParameter);
            TrainingActionManager.BasicPClampValue(monsterInfo, increaseExploreParameter);
            outputMonsterInfo.MonsterAlteredBasicP.AddHealth(increaseExploreParameter.Health);
            outputMonsterInfo.MonsterAlteredBasicP.AddStrength(increaseExploreParameter.Strength);
            outputMonsterInfo.MonsterAlteredBasicP.AddIntelligent(increaseExploreParameter.Intelligent);
            outputMonsterInfo.MonsterAlteredBasicP.AddDexterity(increaseExploreParameter.Dexterity);
            outputMonsterInfo.MonsterAlteredBasicP.AddAgility(increaseExploreParameter.Agility);
            outputMonsterInfo.MonsterAlteredBasicP.AddVitality(increaseExploreParameter.Vitality);
            TrainingActionManager.OnSimulationCalculationFinish?.Invoke(outputMonsterInfo);
        }

        private bool SingleTraining(SerializeMonster monsterInfo, int mainTerrainStat, int subTerrainStat, SerializeGrowthParameters growthValue, out SerializeGrowthParameters weekResultTraining, out InjuryTypesEnum injury, out DiseasesTypesEnum disease, out bool isLost, MonsterFoodScriptable selectedFood, bool isPremium)
        {
            weekResultTraining = new();

            #region Apply food value
            weekResultTraining.AddEnergy(-5 + (isPremium ? selectedFood.FoodPremiumEnergyValue : selectedFood.FoodEnergyValue));
            weekResultTraining.AddBody(-5 + (isPremium ? selectedFood.FoodPremiumBodyValue : selectedFood.FoodBodyValue));
            weekResultTraining.AddCondition(-5 + (isPremium ? selectedFood.FoodPremiumConditionValue : selectedFood.FoodConditionValue));

            weekResultTraining.AddFatigue(isPremium ? selectedFood.FatiguePremiumValue : selectedFood.FatigueValue);
            weekResultTraining.AddStress(isPremium ? selectedFood.StressPremiumValue : selectedFood.StressValue);
            weekResultTraining.AddFriendship(isPremium ? selectedFood.FriendshipPremiumValue : selectedFood.FriendshipValue);
            weekResultTraining.AddTrainingPolicy((isPremium ? selectedFood.PolicyPremiumValue : selectedFood.PolicyValue) - 1);
            weekResultTraining.AddPhysical(isPremium ? selectedFood.PhysicalPremiumValue : selectedFood.PhysicalValue);

            if (monsterInfo.LikeMeal == selectedFood)
            {
                weekResultTraining.AddFatigue(-1);
                weekResultTraining.AddStress(-1);
                weekResultTraining.AddTrainingPolicy(TrainingParameterValues.Get_TrainingPolicyConditionValue(0));
                weekResultTraining.AddFriendship(TrainingParameterValues.Get_FriendshipConditionValue(0));
            }
            else if (monsterInfo.DislikeMeal == selectedFood)
            {
                weekResultTraining.AddTrainingPolicy(TrainingParameterValues.Get_TrainingPolicyConditionValue(1));
                weekResultTraining.AddFriendship(TrainingParameterValues.Get_FriendshipConditionValue(1));
            }
            #endregion

            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"---Food selected = {selectedFood.FoodName}---");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Energy: E = {(isPremium ? selectedFood.FoodPremiumEnergyValue : selectedFood.FoodEnergyValue)}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Body: B = {(isPremium ? selectedFood.FoodPremiumBodyValue : selectedFood.FoodBodyValue)}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Condition: C = {(isPremium ? selectedFood.FoodPremiumConditionValue : selectedFood.FoodConditionValue)}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Physical body value = {(isPremium ? selectedFood.PhysicalPremiumValue : selectedFood.PhysicalValue)}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Fatigue = {(isPremium ? selectedFood.FatiguePremiumValue : selectedFood.FatigueValue)}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Stress = {(isPremium ? selectedFood.StressPremiumValue : selectedFood.StressValue)}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Friendship = {(isPremium ? selectedFood.FriendshipPremiumValue : selectedFood.FriendshipValue)}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Policy value = {(isPremium ? selectedFood.PolicyPremiumValue : selectedFood.PolicyValue)}");

            weekResultTraining.AddPhysical(TrainingParameterValues.Get_ActionPerformBasePhysicalBuild(1));

            #region Faituge
            float fatigueTraitCorrection = monsterInfo.InnateTraitCombineValues.TrainingFatigueReduction;
            float trainingFatigue = TrainingActionManager.Get_FatigueCaculatedIncrease(TrainingParameterValues.Get_ActionPerformBaseFatigue(0),
                                                                                       0f,
                                                                                       mainTerrainStat,
                                                                                       subTerrainStat,
                                                                                       fatigueTraitCorrection,
                                                                                       weekResultTraining.Physical + monsterInfo.ComplexPhysical + growthValue.Physical,
                                                                                       weekResultTraining.TrainingPolicy + monsterInfo.ComplexTrainingPolicy + growthValue.TrainingPolicy);
            weekResultTraining.AddFatigue(trainingFatigue);
            FatigueTypesEnum fatigueType = TrainingParameterValues.FatigueTypeConvert(weekResultTraining.Fatigue + monsterInfo.ComplexFatigue + growthValue.Fatigue);
            #endregion

            #region Stress
            float stressTraitCorrection = monsterInfo.InnateTraitCombineValues.TrainingStressReduction;
            float trainingStress = TrainingActionManager.Get_StressCalculatedIncrease(TrainingParameterValues.Get_ActionPerformBaseStress(0),
                                                                                      0f,
                                                                                      mainTerrainStat,
                                                                                      subTerrainStat,
                                                                                      stressTraitCorrection,
                                                                                      weekResultTraining.Physical + monsterInfo.ComplexPhysical + growthValue.Physical,
                                                                                      weekResultTraining.TrainingPolicy + monsterInfo.ComplexTrainingPolicy + growthValue.TrainingPolicy);
            weekResultTraining.AddStress(trainingStress);
            StressTypesEnum stressType = TrainingParameterValues.StressTypeConvert(weekResultTraining.Stress + monsterInfo.ComplexStress + growthValue.Stress);
            #endregion

            #region Degree of friendship
            switch (fatigueType)
            {
                case FatigueTypesEnum.Tired:
                    weekResultTraining.AddFriendship(TrainingParameterValues.Get_FriendshipConditionValue(6));
                    break;
                case FatigueTypesEnum.Overwork:
                    weekResultTraining.AddFriendship(TrainingParameterValues.Get_FriendshipConditionValue(8));
                    break;
            }

            switch (stressType)
            {
                case StressTypesEnum.Highstress:
                    weekResultTraining.AddFriendship(TrainingParameterValues.Get_FriendshipConditionValue(9));
                    break;
                case StressTypesEnum.Overstress:
                    weekResultTraining.AddFriendship(TrainingParameterValues.Get_FriendshipConditionValue(11));
                    break;
            }
            #endregion

            #region Injury
            float injuryChance = TrainingParameterValues.Get_InjuriesChanceFromTrainingResult(3);
            float injuyryTraitCorrection = monsterInfo.InnateTraitCombineValues.TrainingInjuryChance_Add;

            Vector2 injuryRate = TrainingActionManager.Get_InjuryProbabilityRate(injuryChance, injuyryTraitCorrection, fatigueType, 0);
            injury = TrainingActionManager.Get_CalculatedInjury(monsterInfo.MonsterInjuryCondition, injuryRate.x, injuryRate.y);
            #endregion

            #region Disease
            float numberOfSickmonster = 0;
            float diseaseTraitCorrection = monsterInfo.InnateTraitCombineValues.TrainingDiseaseChance_Add;

            Vector2 diseaseRate = TrainingActionManager.Get_DiseaseProbabilityRate(weekResultTraining.Energy + monsterInfo.ComplexEnergy + growthValue.Energy, weekResultTraining.Body + monsterInfo.ComplexBody + growthValue.Body, weekResultTraining.Condition + monsterInfo.ComplexCondition + growthValue.Condition, numberOfSickmonster, diseaseTraitCorrection, stressType);
            disease = TrainingActionManager.Get_RandomDisease(monsterInfo.MonsterDiseasesCondition, diseaseRate.x, diseaseRate.y);
            #endregion

            weekResultTraining.AddLifespan(TrainingActionManager.Get_LifeSpanConsumption(1, lifespanConsume, fatigueType, stressType));
            //isLost = TrainingActionManager.Get_ExploreIsLost(TrainingActionManager.Get_ExploreTotalParameterCorrection(monsterInfo.ComplexMonsterBasicParameter, exploreAreaScriptable.ExploreCorrection, exploreAreaScriptable.ExploreRecommendation, 0, mainTerrainStat, subTerrainStat, injury, disease), 0, 1, injury, disease);
            switch (injury)
            {
                case InjuryTypesEnum.Injured:
                    weekResultTraining.AddLifespan(TrainingParameterValues.Get_LifeSpanDecreaseValue(4));
                    break;
                case InjuryTypesEnum.SeriouslyInjured:
                    weekResultTraining.AddLifespan(TrainingParameterValues.Get_LifeSpanDecreaseValue(5));
                    break;
            }
            switch (disease)
            {
                case DiseasesTypesEnum.Sick:
                    weekResultTraining.AddLifespan(TrainingParameterValues.Get_LifeSpanDecreaseValue(6));
                    break;
                case DiseasesTypesEnum.Illness:
                    weekResultTraining.AddLifespan(TrainingParameterValues.Get_LifeSpanDecreaseValue(7));
                    break;
            }
            isLost = false;
            
            return injury != InjuryTypesEnum.SeriouslyInjured && disease != DiseasesTypesEnum.Illness;
        }

        public static List<SerializeExplorationItem> GetExplorationItemFound(SerializeMonster playerMonsterInfo, InjuryTypesEnum currentInjuryCondition, DiseasesTypesEnum currentDiseasesCondition, SerializeBasicParameters monsterComplexParameters, MapAreaScriptable basicTrainingAreaDataScriptObj, int mainRaceStat, int subRaceStat, float totalTicketConsumed, float rank2TicketConsumed, float rank3TicketConsumed)
        {
            SerializeDataStruct.Data.DebugDataCollection.AppendLine("Find Item");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Discovery Ticket Consumed = {totalTicketConsumed}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Rank2 Ticket Consumed = {rank2TicketConsumed}");
            SerializeDataStruct.Data.DebugDataCollection.AppendLine($"Rank3 Ticket Consumed = {rank3TicketConsumed}");

            float itemAcquisitionTraitCorrection = playerMonsterInfo.InnateTraitCombineValues.ItemFoundChance_Add;
            float itemRankUpTraitCorrection = playerMonsterInfo.InnateTraitCombineValues.RankUpChance_Add;
            float totalInjuryCorrection = 0f;
            /// If you are injured, the parameters at the time of judgment are reduced by 10%.
            if (currentInjuryCondition != InjuryTypesEnum.None)
            {
                totalInjuryCorrection = -0.1f;
            }

            float totalDiseaseCorrection = 0f;
            // If you are sick, the parameters at the time of judgment are reduced by 10%.
            if (currentDiseasesCondition != DiseasesTypesEnum.None)
            {
                totalDiseaseCorrection = -0.1f;
            }

            List<SerializeExplorationItem> foundList = new List<SerializeExplorationItem>();

            SerializeDataStruct.Data.DebugDataCollection.AppendLine("Find Item A");
            ///Find item A
            float itemA_DiscoveryProbability = TrainingActionManager.Get_Exploration_Item_Discovery_Probability(monsterParameters: monsterComplexParameters,
                                                                                                                explorationParameterCorrections: basicTrainingAreaDataScriptObj.ExploreCorrection,
                                                                                                                recommendedTotal: basicTrainingAreaDataScriptObj.ExploreRecommendation,
                                                                                                                injuryCorrection: totalInjuryCorrection,
                                                                                                                diseaseCorrection: totalDiseaseCorrection,
                                                                                                                mainStat: mainRaceStat,
                                                                                                                subStat: subRaceStat,
                                                                                                                numberOfDiscoveryTicketsConsumed: totalTicketConsumed,
                                                                                                                itemAcquisitionTraitCorrection: itemAcquisitionTraitCorrection,
                                                                                                                itemAcquisitionType: SerializeExplorationItem.ItemType.a);

            Vector2 rank2rank3Rate = TrainingActionManager.Get_Exploration_Item_Rank_Increase_Rate(rank2TicketConsumed, rank3TicketConsumed, itemRankUpTraitCorrection);
            rank2rank3Rate += new Vector2(itemRankUpTraitCorrection, itemRankUpTraitCorrection);

            SerializeExplorationItem itemA = TrainingActionManager.Get_Exploration_Item_Found(itemA_DiscoveryProbability, rank2rank3Rate.x, rank2rank3Rate.y);

            if (itemA != null)
            {
                itemA.itemType = SerializeExplorationItem.ItemType.a;
                foundList.Add(itemA);

                SerializeDataStruct.Data.DebugDataCollection.AppendLine("Item A found");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine("Item rank = " + itemA.discoveryRank);
            }

            SerializeDataStruct.Data.DebugDataCollection.AppendLine("Find Item B");
            ///Find item B
            float itemB_DiscoveryProbability = TrainingActionManager.Get_Exploration_Item_Discovery_Probability(monsterParameters: monsterComplexParameters,
                                                                                                                explorationParameterCorrections: basicTrainingAreaDataScriptObj.ExploreCorrection,
                                                                                                                recommendedTotal: basicTrainingAreaDataScriptObj.ExploreRecommendation,
                                                                                                                injuryCorrection: totalInjuryCorrection,
                                                                                                                diseaseCorrection: totalDiseaseCorrection,
                                                                                                                mainStat: mainRaceStat,
                                                                                                                subStat: subRaceStat,
                                                                                                                numberOfDiscoveryTicketsConsumed: totalTicketConsumed,
                                                                                                                itemAcquisitionTraitCorrection: itemAcquisitionTraitCorrection,
                                                                                                                itemAcquisitionType: SerializeExplorationItem.ItemType.b);

            rank2rank3Rate = TrainingActionManager.Get_Exploration_Item_Rank_Increase_Rate(rank2TicketConsumed, rank3TicketConsumed, itemRankUpTraitCorrection);
            rank2rank3Rate += new Vector2(itemRankUpTraitCorrection, itemRankUpTraitCorrection);

            SerializeExplorationItem itemB = TrainingActionManager.Get_Exploration_Item_Found(itemB_DiscoveryProbability, rank2rank3Rate.x, rank2rank3Rate.y);

            if (itemB != null)
            {
                itemB.itemType = SerializeExplorationItem.ItemType.b;
                foundList.Add(itemB);

                SerializeDataStruct.Data.DebugDataCollection.AppendLine("Item B found");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine("Item rank = " + itemB.discoveryRank);
            }

            SerializeDataStruct.Data.DebugDataCollection.AppendLine("Find Item C");
            ///Find item C
            float itemC_DiscoveryProbability = TrainingActionManager.Get_Exploration_Item_Discovery_Probability(monsterParameters: monsterComplexParameters,
                                                                                                                explorationParameterCorrections: basicTrainingAreaDataScriptObj.ExploreCorrection,
                                                                                                                recommendedTotal: basicTrainingAreaDataScriptObj.ExploreRecommendation,
                                                                                                                injuryCorrection: totalInjuryCorrection,
                                                                                                                diseaseCorrection: totalDiseaseCorrection,
                                                                                                                mainStat: mainRaceStat,
                                                                                                                subStat: subRaceStat,
                                                                                                                numberOfDiscoveryTicketsConsumed: totalTicketConsumed,
                                                                                                                itemAcquisitionTraitCorrection: itemAcquisitionTraitCorrection,
                                                                                                                itemAcquisitionType: SerializeExplorationItem.ItemType.c);

            rank2rank3Rate = TrainingActionManager.Get_Exploration_Item_Rank_Increase_Rate(rank2TicketConsumed, rank3TicketConsumed, itemRankUpTraitCorrection);
            rank2rank3Rate += new Vector2(itemRankUpTraitCorrection, itemRankUpTraitCorrection);

            SerializeExplorationItem itemC = TrainingActionManager.Get_Exploration_Item_Found(itemC_DiscoveryProbability, rank2rank3Rate.x, rank2rank3Rate.y);

            if (itemC != null)
            {
                itemC.itemType = SerializeExplorationItem.ItemType.c;
                foundList.Add(itemC);

                SerializeDataStruct.Data.DebugDataCollection.AppendLine("Item C found");
                SerializeDataStruct.Data.DebugDataCollection.AppendLine("Item rank = " + itemC.discoveryRank);
            }

            SerializeDataStruct.Data.DebugDataCollection.AppendLine("Item found list");
            foreach (SerializeExplorationItem item in foundList.AsSpan())
            {
                SerializeDataStruct.Data.DebugDataCollection.AppendLine("Item found = " + item.itemType.ToString());
                SerializeDataStruct.Data.DebugDataCollection.AppendLine("Item rank = " + item.discoveryRank.ToString());
            }
            return foundList;
        }

        #region Custom find item
        public List<SerializeDataStruct.SerializeDiscoverItem> GetItemDiscover(float AA, float AB, float AC)
        {
            float randomItemA = Random.Range(0, 1);
            float randomItemB = Random.Range(0, 1);
            float randomItemC = Random.Range(0, 1);
            List<SerializeDiscoverItem> discoveredItem = new();
            if (randomItemA <= AA)
            {
                SerializeDiscoverItem AAItem = new SerializeDiscoverItem();
                AAItem.ItemType = 0;
                AAItem.ItemRarity = ItemsRarityEnum.C;
                discoveredItem.Add(AAItem);
            }
            if (randomItemB <= AB)
            {
                SerializeDiscoverItem ABItem = new SerializeDiscoverItem();
                ABItem.ItemType = 1;
                ABItem.ItemRarity = ItemsRarityEnum.C;
                discoveredItem.Add(ABItem);
            }
            if (randomItemC <= AC)
            {
                SerializeDiscoverItem ACItem = new SerializeDiscoverItem();
                ACItem.ItemType = 2;
                ACItem.ItemRarity = ItemsRarityEnum.C;
                discoveredItem.Add(ACItem);
            }

            return discoveredItem;
        }

        public List<SerializeDiscoverItem> RankUpItem(List<SerializeDiscoverItem> discoveredItems, float rank2UpRate, float rank3UpRate)
        {
            for (int i = 0; i < discoveredItems.Count; i++)
            {
                float randomRank2 = Random.Range(0, 1);
                if (randomRank2 <= rank2UpRate)
                {
                    discoveredItems[i].ItemRarity = ItemsRarityEnum.UC;
                }
                float randomRank3 = Random.Range(0, 1);
                if (randomRank3 <= rank3UpRate)
                {
                    discoveredItems[i].ItemRarity = ItemsRarityEnum.R;
                }
            }
            return discoveredItems;
        }

        private List<SerializeDiscoverItem> GetWeeklyItem(SerializeMonster monsterInfo, float totalParameter, Enums.DiseasesTypesEnum trainingDisease, Enums.InjuryTypesEnum trainingInjury, int mainTerrainStat, int subTerrainStat, Scriptables.MapAreaScriptable exploreArea, int discoveryTicketConsume, int rank2TicketConsume, int rank3TicketConsume)
        {
            float injuryCorrection = 0;
            if (monsterInfo.MonsterInjuryCondition != Enums.InjuryTypesEnum.None)
                injuryCorrection = 0.1f;

            float diseaseCorrection = 0;
            if (monsterInfo.MonsterDiseasesCondition != Enums.DiseasesTypesEnum.None)
                diseaseCorrection = 0.1f;

            float PC = totalParameter * (1 + injuryCorrection + diseaseCorrection) / exploreArea.ExploreRecommendation - 1;

            float terrainCorrection = Managers.TrainingParameterValues.Get_TerrainCompatibilityStatCorrection(mainTerrainStat, subTerrainStat);

            float AP = Mathf.Clamp(PC + terrainCorrection, -0.4f, 0.4f);

            float AA = 0.3f * (1 + AP + 0.2f * discoveryTicketConsume + monsterInfo.InnateTraitCombineValues.ItemFoundChance_Add);
            float AB = 0.1f * (1 + AP + 0.2f * discoveryTicketConsume + monsterInfo.InnateTraitCombineValues.ItemFoundChance_Add);
            float AC = 0.05f * (1 + AP + 0.2f * discoveryTicketConsume + monsterInfo.InnateTraitCombineValues.ItemFoundChance_Add);

            float Rank2 = 0.2f + monsterInfo.InnateTraitCombineValues.RankUpChance_Add + 0.2f * rank2TicketConsume;
            float Rank3 = 0.2f + monsterInfo.InnateTraitCombineValues.RankUpChance_Add + 0.2f * rank3TicketConsume;

            StringBuilder logText = new StringBuilder();
            logText.AppendLine($"Total parameter: {totalParameter}");
            logText.AppendLine($"IC: {injuryCorrection}");
            logText.AppendLine($"SC: {diseaseCorrection}");
            logText.AppendLine($"DT: {discoveryTicketConsume}");
            logText.AppendLine($"IA: {monsterInfo.InnateTraitCombineValues.ItemFoundChance_Add}");
            logText.AppendLine($" RP: {exploreArea.ExploreRecommendation}");
            logText.AppendLine($"PC = {PC}");
            logText.AppendLine($"C = {terrainCorrection}");
            logText.AppendLine($"AP = {AP}");
            logText.AppendLine("\n");
            logText.AppendLine($"AA: {AA}");
            logText.AppendLine($"AB: {AB}");
            logText.AppendLine($"AC: {AC}");
            logText.AppendLine($"Rank2: {Rank2}");
            logText.AppendLine($"Rank3: {Rank3}");
            //add item and random here
            List<SerializeDiscoverItem> discoverItem = GetItemDiscover(AA, AB, AC);
            discoverItem = RankUpItem(discoverItem, Rank2, Rank3);
            return discoverItem;
        }
        #endregion
    }
}