using System.Collections.Generic;
using TMPro;
using UnityEngine;
using Assets.Scripts.Managers;
using Assets.Scripts.DataStruct;
using System.Linq;
using Assets.Scripts.SerializeDataStruct;
using Assets.Scripts.UI.FarmMenu.RaiseUI;
using Network;
using UI.BattleUI.UIBattleAbility;
using UnityEngine.UI;
using Assets.Scripts.UI.BattleUI.PvP;

namespace Assets.Scripts.UI.MonsterDetailsInfo
{
    public partial class UIMonsterDetail
    {
        public GameObject LoadingCanvas { get; private set; }

        [System.Serializable]
        private class UIMonsterBattleDetailB
        {
            [SerializeField] private List<UISelectedSkillDetail> skillList = new();
            [SerializeField] private List<UIInnateTrait> innateTraitList = new();
            [SerializeField] private List<UIAcquiredTraits> acquireTraitList = new();
            [SerializeField] private UIFarmTrait farmTraitcomponent;
            [SerializeField] private TextMeshProUGUI skillCostText;
            [SerializeField] private Color disabledSkillTextColor;
            [SerializeField] private Color disabledSkillOutlineColor;
            [SerializeField] private Color activeSkillOutlineColor;
            [SerializeField] private List<Button> skillInfoList = new();
            [SerializeField] private UIMonsterSkillDetail skillDetail;
            [SerializeField] private GameObject skillCostOverflow;

            internal void FillData(SerializeMonster monsterInfo)
            {
                for (var i = 0; i < skillInfoList.Count; i++)
                {
                    var index = i;
                    skillInfoList[i].onClick.AddListener(() =>
                    {
                        skillDetail.SetSkill_Preview(index, monsterInfo);
                    });
                }
                
                SetInnateTraitData(monsterInfo);
                SetAcquiredTraitData(monsterInfo);
                SetFarmTraitData(monsterInfo);
                SetSkillData(monsterInfo);
            }

            private void SetSkillData(SerializeMonster monsterInfo)
            {
                var skillIndex = 0;
                var totalCost = 0;
                foreach (var skillData in monsterInfo.MonsterSkillLevel.Where(skillData => skillIndex < skillList.Count && skillData.IsSelected))
                {
                    skillList[skillIndex].SetSkill(monsterInfo,monsterInfo.MonsterSkillDetailDict[skillData.SkillId], skillData.SelectLevel);
                    totalCost += monsterInfo.MonsterSkillDetailDict[skillData.SkillId].SkillDetail[skillData.SelectLevel - 1].SkillCost;
                    skillIndex++;
                }

                for (var i = skillIndex; i < skillList.Count; i++)
                {
                    skillList[i].SetBlank();
                }
                
                ValidateSkillCost(monsterInfo,totalCost);
            }

            private void ValidateSkillCost(SerializeMonster monster, int totalCost)
            {
                var battleRank = monster.MonsterRank.ToString();
                if (CustomNetworkManager.Instance.GetRoom() != null)
                {
                    battleRank = CustomNetworkManager.Instance.GetRoom().State.metadata.monsterRank;
                }
                else
                {
                    if (!string.IsNullOrEmpty(CustomPvpModeAI.Instance.BattleRank))
                    {
                        battleRank = CustomPvpModeAI.Instance.BattleRank;
                    }
                }
                var costToCompare = battleRank.ToLower() switch
                {
                    "f" => 30,
                    "e" => 35,
                    "d" => 40,
                    "c" => 45,
                    "b" => 50,
                    "a" => 55,
                    "s" => 60,
                    _ => 30
                };
          
                skillCostText.text = $"{totalCost}/{costToCompare}";
                if (totalCost > costToCompare)
                {
                    skillCostText.color = Color.red;
                    skillCostOverflow.SetActive(true);
                }
                else
                {
                    skillCostText.color = Color.white;
                    skillCostOverflow.SetActive(false);
                }
            }

            private void SetFarmTraitData(SerializeMonster monsterInfo)
            {
                var isFarmSet = false;
                var farmSetup = GameDataManager.Instance.LoadedPlayerData.PlayerFarmSetupList.FirstOrDefault(x => x.IsSelected);
                if (farmSetup != null)
                    foreach (var farm in farmSetup.FarmSet.Where(setup => setup.MonsterId == monsterInfo.MonsterId).Select(setup1 => GameDataManager.Instance.LoadedPlayerData.PlayerFarmList.FirstOrDefault(x =>
                                 x.FarmId == setup1.FarmId)).Where(farm => farm != null))
                    {
                        farmTraitcomponent.SetData(farm.FarmTrait.FarmTraitName,
                            (string.IsNullOrEmpty(farm.FarmTrait.Description))
                                ? farm.FarmTrait.Description
                                : farm.FarmTrait.Description.Trim());
                        isFarmSet = true;
                    }

                if (!isFarmSet)
                    farmTraitcomponent.SetData("None", "");
            }

            private void SetAcquiredTraitData(SerializeMonster monsterInfo)
            {
                var acquiredTraitIndex = 0;
                foreach (var t in acquireTraitList)
                {
                    if (acquiredTraitIndex + 1 > monsterInfo.MonsterAcquireTraitLevel.Count && acquiredTraitIndex == 0)
                    {
                        t.SetTraitNone();
                        acquiredTraitIndex++;
                        continue;
                    }
                    if (acquiredTraitIndex != 0 && acquiredTraitIndex + 1 > monsterInfo.MonsterAcquireTraitLevel.Count)
                    {
                        t.SetTraitNone();
                        t.gameObject.SetActive(false);
                        acquiredTraitIndex++;
                        continue;
                    }
                    if (monsterInfo.MonsterAcquireTraitLevel[acquiredTraitIndex].TraitScriptable.TraitId.Equals("None") && acquiredTraitIndex == 0)
                    {
                        t.SetTraitNone();
                        acquiredTraitIndex++;
                        continue;
                    }
                    //trait from count 2 == None
                    if (monsterInfo.MonsterAcquireTraitLevel[acquiredTraitIndex].TraitScriptable.TraitId.Equals("None") && acquiredTraitIndex != 0)
                    {
                        t.SetTraitNone();
                        t.gameObject.SetActive(false);
                        acquiredTraitIndex++;
                        continue;
                    }
                    //trait from count 2 != None
                    if (acquiredTraitIndex != 0 && monsterInfo.MonsterAcquireTraitLevel[0].TraitScriptable.TraitId.Equals("None"))
                        acquireTraitList[0].gameObject.SetActive(false);
                    var trait = monsterInfo.MonsterAcquireTraitLevel[acquiredTraitIndex];
                    t.SetData(trait.TraitScriptable.TraitName, trait.Level, trait.TraitScriptable.Description.Trim());
                    acquiredTraitIndex++;
                }
            }

            private void SetInnateTraitData(SerializeMonster monsterInfo)
            {
                var innateTraitIndex = 0;
                for (var i = 0; i < innateTraitList.Count; i++)
                {
                    if (innateTraitIndex >= monsterInfo.InnateTraits.Count && innateTraitIndex == 0)
                    {
                        innateTraitList[i].SetTraitNone();
                        innateTraitIndex++;
                        continue;
                    }
                    if (innateTraitIndex >= monsterInfo.InnateTraits.Count && innateTraitIndex != 0)
                    {
                        innateTraitList[i].SetTraitNone();
                        innateTraitList[i].gameObject.SetActive(false);
                        innateTraitIndex++;
                        continue;
                    }
                    if (monsterInfo.InnateTraits[innateTraitIndex].TraitId.Equals("None") && innateTraitIndex == 0)
                    {
                        innateTraitList[i].SetTraitNone();
                        innateTraitIndex++;
                        continue;
                    }
                    if (monsterInfo.InnateTraits[innateTraitIndex].TraitId.Equals("None") && innateTraitIndex != 0)
                    {
                        innateTraitList[i].SetTraitNone();
                        innateTraitList[i].gameObject.SetActive(false);
                        innateTraitIndex++;
                        continue;
                    }
                    if (innateTraitIndex != 0 && monsterInfo.InnateTraits[innateTraitIndex - 1].TraitId.Equals("None"))
                        innateTraitList[i - 1].gameObject.SetActive(false);
                    innateTraitList[i].SetTrait(monsterInfo.InnateTraits[innateTraitIndex].TraitName, monsterInfo.InnateTraits[innateTraitIndex].Description);
                    innateTraitIndex++;
                }
            }

            internal void InitSkillConfigControl(UIMonsterDetail parent, UIAbilitySetMenu abilitySetMenu)
            {
                foreach (var skillButton in skillList)
                {
                    var button = skillButton;
                    skillButton.BlankSlotBtn.onClick.AddListener(() =>
                    {
                        abilitySetMenu.SetSkillEdit(parent.MonsterInfo, button.SkillData);
                    });
                    skillButton.RemoveSlotBtn.onClick.AddListener(() =>
                    {
                        var loading = BackendLoadData.Instance.LoadingCanvas();
                        parent.LoadingCanvas = loading;
                        skillButton.RemoveSkill();
                    });
                    skillButton.ChangeSlotBtn.onClick.AddListener(() =>
                    {
                        abilitySetMenu.SetSkillEdit(parent.MonsterInfo, skillButton.SkillData);
                    });
                }
            }
        }
    }
}