using Assets.Scripts.DataStruct;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.MonsterDetailsInfo
{
    public partial class UIMonsterDetail
    {
        [System.Serializable]
        private class UIMonsterDetailGeneral
        {
            [Header("General data")]
            [SerializeField] private TextMeshProUGUI mainSeedText;
            [SerializeField] private Image mainSeedIcon;
            [SerializeField] private TextMeshP<PERSON>UGUI subSeedText;
            [SerializeField] private Image subSeedIcon;
            [SerializeField] private System.Collections.Generic.List<FarmMenu.UIFarmMoodle> monsterMoodControl = new();
            [SerializeField] private TextMeshProUGUI lifespanValuteText;
            [SerializeField] private TextMeshProUGUI ageValueText;
            [SerializeField] private TextMeshProUGUI weekText;
            [SerializeField] private TextMeshProUGUI growthValueText;
            [SerializeField] private TextMeshProUG<PERSON> fatigueValueText;
            [SerializeField] private TextMeshProU<PERSON><PERSON> stressValueText;
            [SerializeField] private TextMeshP<PERSON><PERSON><PERSON><PERSON> physicalValueText;
            [SerializeField] private TextMeshProUGUI trainingPolicyValueText;
            [SerializeField] private TextMeshProUGUI friendshipValueText;
            [SerializeField] private TextMeshProUGUI personalityValueText;
            [Header("Personality correction")]
            [SerializeField] private TextMeshProUGUI healthCorrectionText;
            [SerializeField] private TextMeshProUGUI strengthCorrectionText;
            [SerializeField] private TextMeshProUGUI intelligentCorrectionText;
            [SerializeField] private TextMeshProUGUI dexterityCorrectionText;
            [SerializeField] private TextMeshProUGUI agilityCorrectionText;
            [SerializeField] private TextMeshProUGUI vitalityCorrectionText;
            [Header("Monster meal")]
            [SerializeField] private Image likeFoodGraphic;
            [SerializeField] private Image dislikeFoodGraphic;
            [SerializeField] private TextMeshProUGUI monsterEngeryText;
            [SerializeField] private TextMeshProUGUI monsterBodyText;
            [SerializeField] private TextMeshProUGUI monsterConditionText;
            [Header("Monster terrain")]
            [SerializeField] private Image mainTerrainPositiveGraphic;
            [SerializeField] private TextMeshProUGUI mainTerrainPositiveText;
            [SerializeField] private Image subTerrainPositiveGraphic;
            [SerializeField] private TextMeshProUGUI subTerrainPositiveText;
            [SerializeField] private Image mainTerrainNegativeGraphic;
            [SerializeField] private TextMeshProUGUI mainTerrainNegativeText;
            [SerializeField] private Image subTerrainNegativeGraphic;
            [SerializeField] private TextMeshProUGUI subTerrainNegativeText;

            internal void FillData(SerializeMonster monsterInfo)
            {
                mainSeedText.text = monsterInfo.MonsterScriptableData.MonsterMainSeed.ToString();
                mainSeedIcon.sprite = Managers.GameComponentsSettingManager.Instance.SeedIconDict[monsterInfo.MonsterScriptableData.MonsterMainSeed];
                subSeedText.text = monsterInfo.MonsterScriptableData.MonsterSubSeed.ToString();
                subSeedIcon.sprite = Managers.GameComponentsSettingManager.Instance.SeedIconDict[monsterInfo.MonsterScriptableData.MonsterSubSeed];

                monsterMoodControl[0].SetStress(monsterInfo.ComplexStress);
                monsterMoodControl[1].SetFatigue(monsterInfo.ComplexFatigue);
                monsterMoodControl[2].SetDisease(monsterInfo.MonsterDiseasesCondition);
                monsterMoodControl[3].SetInjury(monsterInfo.MonsterInjuryCondition);

                lifespanValuteText.text = $"{monsterInfo.ComplexLifespan:0}/{monsterInfo.MonsterScriptableData.MonsterGrowthParameters.Lifespan + monsterInfo.InnateTraitCombineValues.LifeSpan_Add}";
                ageValueText.text = $"{monsterInfo.MonsterAge}";
                weekText.text = I2.Loc.LocalizationManager.GetTranslation(monsterInfo.MonsterAge > 1 ? "Weeks" : "Week");
                growthValueText.text = Managers.GameComponentsSettingManager.Instance.GetGrowthType(monsterInfo.MonsterScriptableData.GrowthType);
                fatigueValueText.text = $"{monsterInfo.ComplexFatigue:0}/100 [{Managers.GameComponentsSettingManager.Instance.GetFatigueType(Managers.TrainingParameterValues.FatigueTypeConvert(monsterInfo.ComplexFatigue))}]";
                stressValueText.text = $"{monsterInfo.ComplexStress:0}/100 [{Managers.GameComponentsSettingManager.Instance.GetStressType(Managers.TrainingParameterValues.StressTypeConvert(monsterInfo.ComplexStress))}]";
                physicalValueText.text = $"{monsterInfo.ComplexPhysical:0}/100 [{Managers.GameComponentsSettingManager.Instance.GetPhysicalText(Managers.TrainingParameterValues.PhysicalBuildTypeConvert(monsterInfo.ComplexPhysical))}]";
                trainingPolicyValueText.text = $"{monsterInfo.ComplexTrainingPolicy:0}/100 [{Managers.GameComponentsSettingManager.Instance.GetPolicyType(Managers.TrainingParameterValues.PolicyTypeConvert(monsterInfo.ComplexTrainingPolicy))}]";
                friendshipValueText.text = $"{monsterInfo.ComplexFriendship:0}/100";
                personalityValueText.text = $"{monsterInfo.MonsterPersonality.PersonalityName}";

                healthCorrectionText.text = $"{monsterInfo.MonsterPersonality.TrainingCorrection.Health}%";
                strengthCorrectionText.text = $"{monsterInfo.MonsterPersonality.TrainingCorrection.Strength}%";
                intelligentCorrectionText.text = $"{monsterInfo.MonsterPersonality.TrainingCorrection.Intelligent}%";
                dexterityCorrectionText.text = $"{monsterInfo.MonsterPersonality.TrainingCorrection.Dexterity}%";
                agilityCorrectionText.text = $"{monsterInfo.MonsterPersonality.TrainingCorrection.Agility}%";
                vitalityCorrectionText.text = $"{monsterInfo.MonsterPersonality.TrainingCorrection.Vitality}%";

                likeFoodGraphic.sprite = monsterInfo.LikeMeal.FoodSprite;
                dislikeFoodGraphic.sprite = monsterInfo.DislikeMeal.FoodSprite;

                monsterEngeryText.text = $"{monsterInfo.ComplexEnergy:0}/100";
                monsterBodyText.text = $"{monsterInfo.ComplexBody:0}/100";
                monsterConditionText.text = $"{monsterInfo.ComplexCondition:0}/100";

                if (Managers.FarmAreaManager.Instance.EnviromentDict.TryGetValue(monsterInfo.MonsterScriptableData.MonsterMainTerrainComp, out Scriptables.UIMonsterEnviromentIcon positiveMainTerrain))
                {
                    mainTerrainPositiveGraphic.sprite = positiveMainTerrain.EnviromentIcon;
                    switch (I2.Loc.LocalizationManager.CurrentLanguageCode)
                    {
                        default:
                        case "en":
                            mainTerrainPositiveText.text = positiveMainTerrain.EnviromentNameEN;
                            break;
                        case "ja":
                            mainTerrainPositiveText.text = positiveMainTerrain.EnviromentNameJP;
                            break;
                    }
                }

                if (Managers.FarmAreaManager.Instance.EnviromentDict.TryGetValue(monsterInfo.MonsterScriptableData.MonsterSubTerrainComp, out Scriptables.UIMonsterEnviromentIcon positiveSubTerrain))
                {
                    subTerrainPositiveGraphic.sprite = positiveSubTerrain.EnviromentIcon;
                    switch (I2.Loc.LocalizationManager.CurrentLanguageCode)
                    {
                        default:
                        case "en":
                            subTerrainPositiveText.text = positiveSubTerrain.EnviromentNameEN;
                            break;
                        case "ja":
                            subTerrainPositiveText.text = positiveSubTerrain.EnviromentNameJP;
                            break;
                    }
                }

                if (Managers.FarmAreaManager.Instance.EnviromentDict.TryGetValue(monsterInfo.MonsterScriptableData.MonsterMainTerrainIncomp, out Scriptables.UIMonsterEnviromentIcon negativeMainTerrain))
                {
                    mainTerrainNegativeGraphic.sprite = negativeMainTerrain.EnviromentIcon;
                    switch (I2.Loc.LocalizationManager.CurrentLanguageCode)
                    {
                        default:
                        case "en":
                            mainTerrainNegativeText.text = negativeMainTerrain.EnviromentNameEN;
                            break;
                        case "ja":
                            mainTerrainNegativeText.text = negativeMainTerrain.EnviromentNameJP;
                            break;
                    }
                }

                if (Managers.FarmAreaManager.Instance.EnviromentDict.TryGetValue(monsterInfo.MonsterScriptableData.MonsterSubTerrainIncomp, out Scriptables.UIMonsterEnviromentIcon negativeSubTerrain))
                {
                    subTerrainNegativeGraphic.sprite = negativeSubTerrain.EnviromentIcon;
                    switch (I2.Loc.LocalizationManager.CurrentLanguageCode)
                    {
                        default:
                        case "en":
                            subTerrainNegativeText.text = negativeSubTerrain.EnviromentNameEN;
                            break;
                        case "ja":
                            subTerrainNegativeText.text = negativeSubTerrain.EnviromentNameJP;
                            break;
                    }
                }
            }
        }
    }
}