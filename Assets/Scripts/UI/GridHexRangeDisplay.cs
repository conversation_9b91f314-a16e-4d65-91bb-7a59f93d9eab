using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.Enums;
using Assets.Scripts.Scriptables;
using MPewsey.HexagonalUI;
using UnityEngine;
using UnityEngine.UI;

public class GridHexRangeDisplay : MonoBehaviour
{
    private List<Image> listHexRange;
    private List<Image> listHexSoe;
    private int maxGridCount;

    [SerializeField] private HexLayoutGroup hexLayoutGroupRange;
    [SerializeField] private HexLayoutGroup hexLayoutGroupSoe;

    [SerializeField] private Sprite normalHexSprite;
    [SerializeField] private Sprite skillHexSprite;
    [SerializeField] private Sprite highlightHexSprite;

    [SerializeField] private SkillDataScriptable skillData;


    private List<int> range1 = new() { 2, 8 };
    private List<int> range2 = new() { 0, 4, 5, 15, 20, 24 };
    private List<int> range2x = new() { 0, 2, 4, 5, 6, 9, 15, 16, 19, 20, 22, 24 };
    private List<int> range3 = new() { 0, 5, 6, 7, 13, 20, 34, 35, 41, 42, 47, 48 };

    private List<int> range3x = new()
    {
        0, 2, 3, 5, 6, 7, 8, 10, 12, 13, 14, 15, 18, 19, 20, 28, 29, 32, 33, 36, 34, 35, 38, 40, 41, 42, 44, 45, 47, 48
    };
    private List<int> range4x = new()
    {
        0,1,3,4,5,7,8,9,10,11,13,14,16,17,18,19,20,22,24,25,26,27,28,29,30,33,34,35,45,46,47,48,51,52,53,54,55,56,58,60,61,62,63,64,65,67,68,70,71,72,73,75,76,77,79,80
    };
    private List<int> range5 = new() { 0, 1, 8, 9, 10, 11, 12, 20, 21, 22, 31, 32, 33, 43, 54, 76, 77, 87, 88, 97, 98, 99, 100, 108, 109, 110, 111, 118, 119, 120 };

    private List<int> battleHex = new()
    {
        0, 1, 8, 9, 10, 11, 12, 20, 21, 22, 31, 32, 33, 34, 42, 43, 44, 53, 54, 55, 56, 64, 65, 66, 67, 74, 75, 76, 77, 78, 79, 80
    };
    
    private int[] scopeTypeX5range3 = { 19, 20, 27, 33, 34 };
    private int[] scopeTypeX5range2 = { 8, 9, 13, 18, 19 };
    private int[] scopeTypeCircle7 = { 18, 19, 25, 26, 27, 32, 33 };
    private int[] scopeTypeStraight3 = { 25, 26, 27 };
    private int[] scopeOther6 = { 9, 10, 11, 12, 13, 14, 19 };
    private int[] scopeCircle4 = { 1, 3, 4, 5, 7 };
    private int[] scopeDiffusion7 = { 8, 9, 10, 11, 12, 13, 18, 19 };
    
    public void SetData(SkillDataScriptable skillData)
    {
        this.skillData = skillData;
        hexLayoutGroupRange.transform.DestroyChildrenHelper();
        hexLayoutGroupSoe.transform.DestroyChildrenHelper();
        
        GridSetup(skillData.SkillRange, hexLayoutGroupRange);
        GenerateHex(skillData.SkillRange, hexLayoutGroupRange, skillData.SkillId);

        if (skillData.SkillId is "SignWitch_Sagittarius_Skill_6" or "SignWitch_GeminiB_Skill_6" or "Hiyori_1_Skill_6" or "SignWitch_Virgilia_Skill_6" or "SignWitch_Scorpius_Skill_4" or "SignWitch_Libra_Skill_5"or "SignWitch_Aquarius_Skill_6"or"ODENPETS_Laby_Skill_5")
        {
            GridSetup(skillData.SkillRange + 1, hexLayoutGroupSoe);
            GenerateHex(skillData.SkillRange + 1, hexLayoutGroupSoe, skillData.SkillId);
        }
        else if (skillData.SkillId is "CCT_Coni_Skill_6" or "CCT_Conic_Skill_6" or "CCT_Conic_Skill_2")
        {
            GridSetup(skillData.SkillRange + 4, hexLayoutGroupSoe, true);
            GenerateHex(skillData.SkillRange + 4, hexLayoutGroupSoe, skillData.SkillId);
        }
        else
        {
            GridSetup(skillData.SkillRange, hexLayoutGroupSoe);
            GenerateHex(skillData.SkillRange, hexLayoutGroupSoe, skillData.SkillId);
        }
    }

    private void GenerateHex(int range, HexLayoutGroup hexParent, string skillDataScriptable)
    {
        for (int i = 0; i <= maxGridCount; i++)
        {
            var hex = new GameObject
            {
                transform =
                {
                    parent = hexParent.gameObject.transform,
                    localScale = Vector3.one,
                    localPosition = Vector3.zero,
                    name = $"hex_{i}"
                }
            };


            var image = hex.AddComponent<Image>();

            if (skillData.ExecutionType is SkillExecutionTypesEnum.Designation or SkillExecutionTypesEnum.Self_Activation or SkillExecutionTypesEnum.Sink_Designation &&
                skillData.SkillScopeType is ScopeOfEffectEnums.Point or ScopeOfEffectEnums.Circle)
            {
                if (skillDataScriptable is "SignWitch_Aquarius_Skill_6" or "SignWitch_Virgilia_Skill_6" or "SignWitch_Scorpius_Skill_4" or "SignWitch_Libra_Skill_5" or "Hiyori_1_Skill_6"or"ODENPETS_Laby_Skill_5")
                {
                    if (range2x.Contains(i))
                        image.enabled = false;
                    else
                        image.sprite = hexParent == hexLayoutGroupSoe ? normalHexSprite : skillHexSprite;
                }
                else {
                    switch (range)
                    {
                        case 1 when range1.Contains(i):
                        case 2 when range2.Contains(i):
                        case 3 when range3.Contains(i):
                        case 5 when range5.Contains(i):
                            image.enabled = false;
                            break;
                        default:
                            image.sprite = hexParent == hexLayoutGroupSoe ? normalHexSprite : skillHexSprite;
                            break;
                    }
                }
            }
            else
            {
                if (skillDataScriptable is "CCT_Coni_Skill_6"|| skillDataScriptable is "CCT_Conic_Skill_6")
                {
                    image.sprite = hexParent == hexLayoutGroupSoe ? normalHexSprite : skillHexSprite;
                }
                else if (skillDataScriptable is "SignWitch_Aquarius_Skill_3")
                {
                    if (range == 2 && range2.Contains(i))
                        image.enabled = false;
                    else
                        image.sprite = hexParent == hexLayoutGroupSoe ? normalHexSprite : skillHexSprite;
                }
                else
                {
                    switch (range)
                    {
                        case 1 when range1.Contains(i):
                        case 2 when range2x.Contains(i):
                        case 3 when range3x.Contains(i):
                        case 4 when range4x.Contains(i):
                            image.enabled = false;
                            break;
                        default:
                            image.sprite = hexParent == hexLayoutGroupSoe ? normalHexSprite : skillHexSprite;
                            break;
                    }   
                }
            }

            if (i == maxGridCount / 2)
            {
                image.sprite = highlightHexSprite;
            }

            if (maxGridCount == 0 && hexParent == hexLayoutGroupSoe)
            {
                image.sprite = skillHexSprite;
            }

            if (hexParent == hexLayoutGroupSoe)
            {
                switch (skillData.SkillScopeType)
                {
                    case ScopeOfEffectEnums.X when skillData.SkillScopeRange == 5:
                    {
                        if (skillData.SkillRange == 3)
                        {
                            if (scopeTypeX5range3.Contains(i))
                            {
                                image.enabled = true;
                                image.sprite = skillHexSprite;
                            }
                        }
                        if (skillData.SkillRange == 2)
                        {
                            if (scopeTypeX5range2.Contains(i))
                            {
                                image.enabled = true;
                                image.sprite = skillHexSprite;
                            }
                        }
                        break;
                    }
                    
                    case ScopeOfEffectEnums.Point when skillData.SkillScopeRange == 1 && skillData.SkillRange == 5:
                    {
                        if (i == maxGridCount / 2 + skillData.SkillRange - 1)
                            image.sprite = highlightHexSprite;
                        if (i == maxGridCount / 2 + skillData.SkillRange)
                            image.sprite = skillHexSprite;
                        break;
                    }
                    case ScopeOfEffectEnums.Point when skillData.SkillScopeRange == 1:
                    {
                        if (i == maxGridCount / 2 + skillData.SkillRange)
                            image.sprite = skillHexSprite;
                        break;
                    }
                    
                    case ScopeOfEffectEnums.Line when skillData.SkillScopeRange == 1:
                    {
                        if (i == maxGridCount / 2 + skillData.SkillRange)
                            image.sprite = skillHexSprite;
                        switch (skillData.SkillId)
                        {
                            case "SignWitch_Aries_Skill_4" when i == maxGridCount / 2 + 2:
                            case "SignWitch_GeminiA_Skill_4" when i == maxGridCount / 2 + 3:
                            case "SignWitch_GeminiB_Skill_4" when i == maxGridCount / 2 + 3:
                                image.sprite = highlightHexSprite;
                                break;
                        }
                        break;
                    }
                    case ScopeOfEffectEnums.Line when skillData.SkillScopeRange == 2 && skillData.SkillRange == 2:
                    {
                        if (i == maxGridCount / 2 + 1)
                            image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2 + 2)
                            image.sprite = skillHexSprite;
                        break;
                    }
                    case ScopeOfEffectEnums.Line when skillData.SkillScopeRange == 3 && skillData.SkillRange == 3:
                    {
                        if (i == maxGridCount / 2 + 1)
                            image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2 + 2)
                            image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2 + 3)
                            image.sprite = skillHexSprite;
                        break;
                    }
                    case ScopeOfEffectEnums.Line when skillData.SkillScopeRange == 4 && skillData.SkillRange == 4:
                    {
                        if (i == maxGridCount / 2 + 1)
                            image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2 + 2)
                            image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2 + 3)
                            image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2 + 4)
                            image.sprite = skillHexSprite;
                        break;
                    }
                    
                    case ScopeOfEffectEnums.Circle when skillData.SkillScopeRange == 3 && skillData.SkillRange == 1:
                    {
                        image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2)
                            image.sprite = highlightHexSprite;
                        break;
                    }
                    case ScopeOfEffectEnums.Circle when skillData.SkillScopeRange == 7 && skillData.SkillRange == 3:
                    {
                        if (scopeTypeCircle7.Contains(i))
                        {
                            image.enabled = true;
                            image.sprite = skillHexSprite;
                        }
                        break;
                    }
                    case ScopeOfEffectEnums.Circle when skillData.SkillScopeRange == 7 && skillData.SkillRange == 1 || skillData.SkillScopeRange == 19 && skillData.SkillRange == 2:
                    {
                        image.sprite = skillHexSprite;
                        break;
                    }
                    case ScopeOfEffectEnums.Circle when skillData.SkillScopeRange == 6 && skillData.SkillRange == 1:
                    {
                        image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2)
                            image.sprite = highlightHexSprite;
                        break;
                    }
                    case ScopeOfEffectEnums.Circle when skillData.SkillScopeRange == 4 && skillData.SkillRange == 1:
                    {
                        image.enabled = false;
                        if (scopeCircle4.Contains(i))
                        {
                            image.enabled = true;
                            image.sprite = skillHexSprite;
                        }
                        if (i == 3)
                            image.sprite = highlightHexSprite;
                        break;
                    }
                    case ScopeOfEffectEnums.Circle when skillData.SkillScopeRange == 4 && skillData.SkillRange == 2:
                    {
                        if (skillData.ExecutionType is SkillExecutionTypesEnum.Designation)
                        {
                            image.enabled = false;
                            int[] enable = { 0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 13, 14, 15, 18, 19, 20, 21, 28, 29, 32, 33, 36, 34, 35, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48 };
                            if (!enable.Contains(i))
                            {
                                image.enabled = true;
                                image.sprite = normalHexSprite;
                            }
                            if (i == maxGridCount / 2)
                            {
                                image.sprite = highlightHexSprite;
                            }
                        }
                        else
                        {
                            int[] disable = { 1, 4, 21, 43, 46 };
                            if (disable.Contains(i))
                            {
                                image.enabled = false;
                            }
                        }
                        
                        
                        int[] highlight = { 19, 26, 27, 33 };
                        if (highlight.Contains(i))
                        {
                            image.enabled = true;
                            image.sprite = skillHexSprite;
                        }
                        break;
                    }
                    case ScopeOfEffectEnums.Circle when skillData.SkillScopeRange == 7 && skillData.SkillRange == 2:
                    {
                        if (i == maxGridCount / 2 - 4) 
                            image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2 - 3) 
                            image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2 + 1)
                            image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2 + 2)
                            image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2 + 6) 
                            image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2 + 7) 
                            image.sprite = skillHexSprite;
                        break;
                    }
                    case ScopeOfEffectEnums.Circle when skillData.SkillScopeRange == 18:
                    {
                        image.sprite = skillHexSprite;
                        if(i == maxGridCount / 2)
                            image.sprite = highlightHexSprite;
                        break;
                    }
                    
                    case ScopeOfEffectEnums.Straight when skillData.SkillScopeRange == 1:
                    {
                        if (i == maxGridCount / 2 + skillData.SkillRange)
                            image.sprite = skillHexSprite;
                        break;
                    }
                    case ScopeOfEffectEnums.Straight when skillData.SkillScopeRange == 3:
                    {
                        if (scopeTypeStraight3.Contains(i))
                        {
                            image.enabled = true;
                            image.sprite = skillHexSprite;
                        }
                        break;
                    }

                    #region Others
                    case ScopeOfEffectEnums.Others when skillData.SkillScopeRange == 6:
                    {
                        image.enabled = false;
                        if (scopeOther6.Contains(i))
                        {
                            image.enabled = true;
                            image.sprite = skillHexSprite;
                        }
                        if (i == 10)
                            image.sprite = highlightHexSprite;
                        break;
                    }
                    case ScopeOfEffectEnums.Others when skillData.SkillScopeRange == 3:
                    {
                        switch (i)
                        {
                            case 1:
                            case 5:
                            case 7:
                                image.sprite = skillHexSprite;
                                break;
                        }
                        break;
                    }
                    case ScopeOfEffectEnums.Others when skillData.SkillScopeRange == 2:
                    {
                        if (i == maxGridCount / 2 - 1)
                            image.sprite = skillHexSprite;
                        if (i == maxGridCount / 2 + 1)
                            image.sprite = skillHexSprite;
                        break;
                    }
                    case ScopeOfEffectEnums.Others when skillData.SkillScopeRange == 49:
                    {
                        if (battleHex.Contains(i))
                        {
                            image.enabled = false;
                        }
                        image.sprite = skillHexSprite;
                        break;
                    }
                    #endregion
                    
                    case ScopeOfEffectEnums.Diffusion when skillData.SkillScopeRange == 7:
                    {
                        image.enabled = false;
                        if (scopeDiffusion7.Contains(i))
                        {
                            image.enabled = true;
                            image.sprite = skillHexSprite;
                        }
                        if (i == 10)
                            image.sprite = highlightHexSprite;
                        break;
                    }
                    
                }
                
                switch (skillDataScriptable)
                {
                    case "Tofu_1_Skill_2":
                    case "Unicorn_1_Skill_1":
                    case "Unicorn_1_Skill_6":
                    case "Valius_1_Skill_5":
                    case "UPA_1_Skill_6":
                    case "Dekausa_1_Skill_2":
                    case "Dekausa_1_Skill_4":
                    case "Dekausa_1_Skill_6":
                    case "Kidsfight_KidsHero_5_Skill_6":
                    case "Kidsfight_KidsVillian_3_Skill_6":
                    {
                        if (i == maxGridCount / 2 + skillData.SkillRange - 1)
                            image.sprite = highlightHexSprite;
                        break;
                    }
                    case "CCT_Conic_Skill_2":
                    {
                        image.sprite = normalHexSprite;
                        if (battleHex.Contains(i))
                        {
                            image.enabled = false;
                        }
                        if (i == maxGridCount / 2)
                            image.sprite = highlightHexSprite;
                        if (i == maxGridCount / 2 + 1)
                            image.sprite = skillHexSprite;
                        break;
                    }
                }
            }

        }
    }

    private void GridSetup(int range, HexLayoutGroup hexLayout, bool isBattleScene = false)
    {
        switch (range)
        {
            case 0:
                maxGridCount = 0;
                hexLayout.padding.right = 32;
                hexLayout.padding.left = 0;
                hexLayout.padding.top = 23;
                hexLayout.padding.bottom = 0;
                hexLayout.Circumradius = 40f;
                break;
            case 1:
                maxGridCount = 8;
                hexLayout.padding.right = 17;
                hexLayout.padding.left = 42;
                hexLayout.padding.top = 30;
                hexLayout.padding.bottom = 0;
                hexLayout.Circumradius = 34.08f;
                break;
            case 2:
                maxGridCount = 24;
                hexLayout.padding.left = 29;
                hexLayout.padding.right = 44;
                hexLayout.padding.top = 18;
                hexLayout.padding.bottom = 0;
                hexLayout.Circumradius = 22.35f;
                break;
            case 3:
                maxGridCount = 48;
                hexLayout.padding.left = 19;
                hexLayout.padding.right = 0;
                hexLayout.padding.top = 41;
                hexLayout.padding.bottom = 0;
                hexLayout.Circumradius = 20f;
                break;
            case 4:
                maxGridCount = 80;
                hexLayout.padding.left = isBattleScene? -1 : 19;
                hexLayout.padding.right = isBattleScene? -13 : 26;
                hexLayout.padding.top = isBattleScene? 63 : 27;
                hexLayout.padding.bottom = 0;
                hexLayout.Circumradius = isBattleScene? 16.13f : 15.4f;
                break;
            case 5:
                maxGridCount = 120;
                hexLayout.padding.left = 34;
                hexLayout.padding.right = 26;
                hexLayout.padding.top = 4;
                hexLayout.padding.bottom = -27;
                hexLayout.Circumradius = 13.22f;
                break;
        }

        hexLayout.childAlignment = TextAnchor.MiddleCenter;
        hexLayout.Spacing = new Vector2(-6.32f, -3.84f);
        hexLayout.StartAxis = HexLayoutGroup.Axis.Horizontal;
        hexLayout.CellOrientation = HexLayoutGroup.Axis.Vertical;
        hexLayout.Constraint = HexLayoutGroup.ConstraintType.Flexible;
    }
}
