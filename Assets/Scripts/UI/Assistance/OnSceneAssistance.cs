using Assets.Scripts.Managers;
using Assets.Scripts.UI.Utilities;
using Febucci.UI.Core;
using UI.Chat;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.Assistance
{
    public class OnSceneAssistance : InitInstance<OnSceneAssistance>
    {
        [SerializeField] private Image m_assistant_view;
        [SerializeField] private Image m_sub_assistant_view;
        [SerializeField] private GameObject m_assistant_obj;
        [SerializeField] private GameObject m_sub_assistant_obj;
        [SerializeField] private Typewriter<PERSON>ore m_assistant_dialog;
        [SerializeField] private GameObject m_optional_select_obj;
        [SerializeField] private Button m_accecpt_btn;
        [SerializeField] private Button m_deny_btn;
        [SerializeField] private Button m_continue_dialog_btn;
        [SerializeField] private Button m_sceneBlockerBtn;
        private bool m_isSetupReady = false;
        private bool m_canBlockMenu = false;

        public bool IsSetupReady => m_isSetupReady;
        public bool IsShowingText => m_assistant_dialog.isShowingText;

        public override void Init()
        {
            gameObject.SetActive(false);
            m_accecpt_btn.onClick.AddListener(AccpectRequest);
            m_deny_btn.onClick.AddListener(DenyRequest);
            m_continue_dialog_btn.onClick.AddListener(DialogInteraction);
            m_sceneBlockerBtn.onClick.AddListener(DialogInteraction);
            m_isSetupReady = true;
            base.Init();
        }

        private void DialogInteraction()
        {
            if (!m_assistant_dialog.isShowingText)
            {
                AssistantManager.Instance.OnDialogComplete?.Invoke();
                m_sceneBlockerBtn.gameObject.SetActive(m_canBlockMenu);
                if (AssistantManager.Instance.OnDialogComplete == null && !m_optional_select_obj.activeSelf)
                {
                    gameObject.SetActive(false);
                    UINotifyManager.ProcessNotify();
                }
            }
            else
            {
                m_assistant_dialog.SkipTypewriter();
            }
        }

        private void AccpectRequest()
        {
            gameObject.SetActive(false);
            AssistantManager.Instance.OnRequestSelected?.Invoke(true);
        }

        private void DenyRequest()
        {
            gameObject.SetActive(false);
            AssistantManager.Instance.OnRequestSelected?.Invoke(false);
        }

        public void Set_MainAssistant(string dialog, string emotionKey, bool optional_dialog = false, bool m_canBlockMenu = false,bool m_canSkipDialog = false)
        {
            ChatMenuManager.Instance.CloseChatMenu();
            gameObject.SetActive(true);
            if(m_canSkipDialog)
            {
                m_assistant_dialog.ShowText(dialog);
                m_assistant_dialog.SkipTypewriter();
            }
            else
            {
                m_assistant_dialog.SkipTypewriter();
                m_assistant_dialog.ShowText(dialog);
            }
            m_sub_assistant_obj.SetActive(false);
            m_assistant_obj.SetActive(true);
            this.m_canBlockMenu = m_canBlockMenu;
            m_assistant_view.sprite = AssistantManager.Instance.AssistantSprite[emotionKey];
            m_sceneBlockerBtn.gameObject.SetActive(true);
            m_optional_select_obj.SetActive(false);
            if (optional_dialog)
            {
                m_assistant_dialog.onTextShowed.AddListener(() =>
                {
                    m_optional_select_obj.SetActive(optional_dialog);
                });
            }
            else
            {
                m_assistant_dialog.onTextShowed.RemoveAllListeners();
                m_optional_select_obj.SetActive(optional_dialog);
            }
        }

        public void BlockOtherMenuInteraction()
        {
            m_sceneBlockerBtn.gameObject.SetActive(true);
        }

        public void Set_SubAssistant(Sprite subAssistant, string dialog)
        {
            m_assistant_dialog.ShowText(dialog);
            m_sub_assistant_view.sprite = subAssistant;
            m_sub_assistant_obj.SetActive(true);
            m_assistant_obj.SetActive(false);
            m_optional_select_obj.SetActive(false);
        }
    }
}
