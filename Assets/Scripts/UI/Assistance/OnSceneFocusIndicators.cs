using System.Collections.Generic;
using UnityEngine;

namespace Assets.Scripts.UI.Assistance
{
    public class OnSceneFocusIndicators : MonoBehaviour
    {
        [SerializeField] private List<GameObject> m_hideObjects;

        public void SetFocusOnObject()
        {
            gameObject.SetActive(true);
            foreach (var item in m_hideObjects)
            {
                item.SetActive(false);
            }
        }

        public void ClearFocus()
        {
            foreach (var item in m_hideObjects)
            {
                item.SetActive(true);
            }
            gameObject.SetActive(false);
        }
    }
}
