using Assets.Scripts.UI.Utilities;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.Assistance
{
    public class UISubMenuPopup : UIMenuStruct
    {
        [SerializeField] private List<Button> m_tutorialButton;
        private int m_farmToUnlock = 1;
        private int m_returnTutorial;

        private void Awake()
        {
            OnMenuClose.AddListener(() =>
            {
                Managers.AssistantManager.Instance.ContinueCurrentTutorial();
            });
            for (int i = 0; i < m_tutorialButton.Count; i++)
            {
                int index = i;
                m_tutorialButton[i].onClick.AddListener(() =>
                {
                    base.CloseMenu();
                    switch (index)
                    {
                        case 0:
                            // rank match
                            OnSceneFocusAssist.Instance?.RequestRankedBattleMenuOpen();
                            Managers.AssistantManager.Instance.PlayTutorial(9);
                            break;
                        case 1:
                            // fusion
                            Managers.GameProgressManager.Instance.GameProgress.FirstFusion = System.DateTime.UtcNow;
                            Managers.GameProgressManager.OnPlayerProgressUpdated?.Invoke();
                            OnSceneFocusAssist.Instance?.RequestFusionMenuOpen();
                            Managers.AssistantManager.Instance.PlayTutorial(8);
                            break;
                        case 2:
                            // coach
                            Managers.GameProgressManager.Instance.GameProgress.FirstCoach = System.DateTime.UtcNow;
                            Managers.GameProgressManager.OnPlayerProgressUpdated?.Invoke();
                            OnSceneFocusAssist.Instance?.RequestCoachMenuOpen();
                            Managers.AssistantManager.Instance.PlayTutorial(12);
                            break;
                        case 3:
                            // guild list
                            OnSceneFocusAssist.Instance?.RequestGuildListMenuOpen();
                            Managers.AssistantManager.Instance.PlayTutorial(11);
                            break;
                        case 4:
                            // farm unlock
                            if (Managers.GameProgressManager.Instance.GameProgress.IsFarmSlot2Unlocked) break;
                            Managers.AssistantManager.Instance.ClearCurrentTutorial();
                            UINotifyManager.AddNotifyResponse("Farm unlock", () =>
                            {
                                string msg = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Unlock Second Farm");
                                FarmUnlockSetup(msg, 1, 10);
                            }, UINotifyManager.NotifyType.DefaultNotify);
                            UINotifyManager.ProcessNotify();
                            break;
                        case 5:
                            // crystalize
                            Managers.GameProgressManager.Instance.GameProgress.FirstCrystallize = System.DateTime.UtcNow;
                            Managers.GameProgressManager.OnPlayerProgressUpdated?.Invoke();
                            OnSceneFocusAssist.Instance?.RequestCrystalizeMenuOpen();
                            Managers.AssistantManager.Instance.PlayTutorial(14);
                            break;
                        case 6:
                            // prize match
                            OnSceneFocusAssist.Instance?.RequestPrizeBattleMenuOpen();
                            Managers.AssistantManager.Instance.PlayTutorial(15);
                            break;
                        case 8:
                            // 3rd farm
                            if (Managers.GameProgressManager.Instance.GameProgress.IsFarmSlot3Unlocked) break;
                            Managers.AssistantManager.Instance.ClearCurrentTutorial();
                            UINotifyManager.AddNotifyResponse("Farm unlock", () =>
                            {
                                string msg = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Unlock Third Farm");
                                FarmUnlockSetup(msg, 2, 16);
                            }, UINotifyManager.NotifyType.DefaultNotify);
                            UINotifyManager.ProcessNotify();
                            break;
                    }
                });
            }
        }

        private void FarmUnlockSetup(string msg, int farmToUnlock, int returnTutorial)
        {
            Managers.AssistantManager.Instance.SetDialog(msg, "normal", true, true, true);
            Managers.AssistantManager.Instance.OnRequestSelected += SetFarmUnlock;
            m_farmToUnlock = farmToUnlock;
            m_returnTutorial = returnTutorial;
        }

        public void SetTutorialList(List<int> tutorialTriggers)
        {
            for (int i = 0; i < m_tutorialButton.Count; i++)
            {
                int buttonIndex = i;
                m_tutorialButton[i].gameObject.SetActive(tutorialTriggers.Contains(buttonIndex));
            }
            OpenMenu();
        }

        private void SetFarmUnlock(bool accept)
        {
            if (accept)
            {
                GameObject loading = BackendLoadData.Instance.LoadingCanvas();
                Managers.GameProgressManager.Instance.GameProgress.UnlockFarmSlot(m_farmToUnlock, () =>
                {
                    string msg = string.Empty;
                    if (m_farmToUnlock == 1)
                    {
                        msg = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg First/Second Farm Unlocked");
                    }
                    else if (m_farmToUnlock == 2)
                    {
                        msg = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg First/Third Farm Unlocked");
                    }
                    UINotifyManager.AddNotifyResponse("Farm unlock success", () =>
                    {
                        UINotifyManager.AddNotifyResponse("Return to tutorial", () =>
                        {
                            if (m_farmToUnlock == 1)
                                Managers.AssistantManager.Instance.PlayTutorial(10);
                            else if (m_farmToUnlock == 2)
                                Managers.AssistantManager.Instance.PlayTutorial(16);
                        }, UINotifyManager.NotifyType.Tutorial, 1);
                        Managers.AssistantManager.Instance.SetDialog(msg, "smile", false, true, true);
                    }, UINotifyManager.NotifyType.Tutorial);
                    UINotifyManager.ProcessNotify();
                    Destroy(loading);
                }, (message) =>
                {
                    UINotifyManager.AddNotifyResponse("Farm unlock fail", () =>
                    {
                        string msg = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg First/Second Farm Unlocked");
                        Managers.AssistantManager.Instance.SetDialog(msg, "confusion", false, true, true);
                        UINotifyManager.AddNotifyResponse("Return to tutorial", () =>
                        {
                            if (m_farmToUnlock == 1)
                                Managers.AssistantManager.Instance.PlayTutorial(10);
                            else if (m_farmToUnlock == 2)
                                Managers.AssistantManager.Instance.PlayTutorial(16);
                        }, UINotifyManager.NotifyType.Tutorial, 1);
                    }, UINotifyManager.NotifyType.DefaultNotify);
                    Destroy(loading);
                });
            }
            else
            {
                Managers.AssistantManager.Instance.OnRequestSelected -= SetFarmUnlock;
                Managers.AssistantManager.Instance.PlayTutorial(m_returnTutorial);
            }
        }
    }
}
