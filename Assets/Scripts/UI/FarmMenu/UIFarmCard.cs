using Assets.Scripts.DataStruct;
using Assets.Scripts.Managers;
using Assets.Scripts.Scriptables;
using Assets.Scripts.SerializeDataStruct;
using Assets.Scripts.UI.FarmMenu.RaiseUI;
using Assets.Scripts.UI.Monsterbag;
using Assets.Scripts.UI.MonsterDetailsInfo;
using Assets.Scripts.UI.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
namespace Assets.Scripts.UI.FarmMenu
{
    public class UIFarmCard : MonoBehaviour
    {
        [SerializeField] private EmptyCardComponents emptyCard;
        [SerializeField] private EmptyFarmComponents emptyFarm;
        [SerializeField] private FarmMonsterComponents monsterFarm;
        [SerializeField] private Button unlockSlot;
        [SerializeField] private TextMeshProUGUI lockRankText;
        private SerializeFarm farmInfo;
        private SerializeMonster monsterInfo;
        private UIFarmListManagerMenu farmListMenu;
        private UIUpdateRaise farmDetail;
        private UIMonsterDetail monsterDetailMenu;
        private UIUpdateMonsterBagMenu monsterFarmBagUpdate;
        private int m_cardNumber = 0;
        private UIFarm m_farmMenu;

        public SerializeFarm FarmInfo => farmInfo;
        public SerializeMonster MonsterInfo => monsterInfo;
        public int CardNumber => m_cardNumber;

        private void SetFarmUnlock(bool accept)
        {
            AssistantManager.Instance.OnRequestSelected -= SetFarmUnlock;
            if (accept)
            {
                GameObject loading = BackendLoadData.Instance.LoadingCanvas();
                GameProgressManager.Instance.GameProgress.UnlockFarmSlot(m_cardNumber, () =>
                {
                    string msg = string.Empty;
                    if (m_cardNumber == 1)
                    {
                        msg = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg First/Second Farm Unlocked");
                    }
                    else if (m_cardNumber == 2)
                    {
                        msg = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg First/Third Farm Unlocked");
                    }
                    UINotifyManager.AddNotifyResponse("Farm unlock success", () =>
                    {
                        AssistantManager.Instance.SetDialog(msg, "smile", false);
                    }, UINotifyManager.NotifyType.DefaultNotify);
                    UINotifyManager.ProcessNotify();
                    Destroy(loading);
                }, (message) =>
                {
                    UINotifyManager.AddNotifyResponse("Farm unlock fail", () =>
                    {
                        string msg = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Bit invalid");
                        AssistantManager.Instance.SetDialog(msg, "confusion", false);
                    }, UINotifyManager.NotifyType.DefaultNotify);
                    UINotifyManager.ProcessNotify();
                    Destroy(loading);
                });
            }
        }

        private void OpenFarmListMenu()
        {
            farmListMenu.SetReplaceMode(true, m_cardNumber);
            farmListMenu.SetTargetFarmInfo(farmInfo);
            farmListMenu.OpenMenu();
        }

        public void SetFarmInfo()
        {
            farmInfo = null;
            emptyCard.MainComponent.SetActive(true);
            emptyFarm.MainComponent.SetActive(false);
            monsterFarm.MainComponent.SetActive(false);
        }

        public void SetLockRank(bool isLocked, bool canUnlock, string rankRequire)
        {
            unlockSlot.gameObject.SetActive(isLocked);
            unlockSlot.interactable = canUnlock;
            lockRankText.text = rankRequire;
        }

        public void SetFarmInfo(SerializeFarm farmInfo)
        {
            this.farmInfo = farmInfo;
            emptyCard.MainComponent.SetActive(false);
            emptyFarm.MainComponent.SetActive(true);
            monsterFarm.MainComponent.SetActive(false);

            emptyFarm.EnviromentImage.sprite = FarmAreaManager.Instance.GetFullscreenSprite(farmInfo.FarmTerrain);
            emptyFarm.FarmNameText.text = farmInfo.FarmName;
            if (EnviromentIconDataManager.Instance.EnviromentIconDict.TryGetValue(farmInfo.FarmTerrain, out UIMonsterEnviromentIcon enviromentIcon))
            {
                emptyFarm.TerrainImage.sprite = enviromentIcon.EnviromentIcon;
                emptyFarm.TerrainImage.color = enviromentIcon.EnviromentColor;
            }
            var farmSetup = GameDataManager.Instance.LoadedPlayerData.PlayerFarmSetupList.FirstOrDefault(x => x.IsSelected);
            var setup = farmSetup.FarmSet.FirstOrDefault(x => x.FarmPosition == m_cardNumber);
            setup = new(m_cardNumber, string.Empty, farmInfo.FarmId);
        }

        public void SetFarmInfo(SerializeFarm farmInfo, SerializeMonster monsterInfo, RenderTexture renderTexture)
        {
            this.farmInfo = farmInfo;
            this.monsterInfo = monsterInfo;
            emptyCard.MainComponent.SetActive(false);
            emptyFarm.MainComponent.SetActive(false);
            monsterFarm.MainComponent.SetActive(true);
            monsterFarm.MonsterRender.texture = renderTexture;
            bool isSetted = false;
            var farmSetup = GameDataManager.Instance.LoadedPlayerData.PlayerFarmSetupList.FirstOrDefault(x => x.IsSelected);
            var setup = farmSetup.FarmSet.FirstOrDefault(x => x.FarmPosition == m_cardNumber);
            isSetted = setup.MonsterId == monsterInfo.MonsterId;
            bool isCompatible = true;
            bool unCompatible = false;

            if (isSetted)
            {
                Helpers.SetMonsterCompability(out isCompatible, out unCompatible, farmInfo, monsterInfo);
            }
            monsterFarm.FarmOn.SetActive(isCompatible);
            monsterFarm.FarmOff.SetActive(unCompatible);
            monsterFarm.EnviromentImage.sprite = FarmAreaManager.Instance.GetFullscreenSprite(farmInfo.FarmTerrain);
            monsterFarm.FarmNameText.text = farmInfo.FarmName;
            if (EnviromentIconDataManager.Instance.EnviromentIconDict.TryGetValue(farmInfo.FarmTerrain, out UIMonsterEnviromentIcon enviromentIcon))
            {
                monsterFarm.TerrainImage.sprite = enviromentIcon.EnviromentIcon;
                monsterFarm.TerrainImage.color = enviromentIcon.EnviromentColor;
            }

            monsterFarm.MonsterNameText.text = monsterInfo.MonsterName;
            RankTypeEnums monsterRank = monsterInfo.MonsterRank;
            monsterFarm.MonsterRankImage.sprite = Managers.GameComponentsSettingManager.Instance.GetRank(monsterRank);
            monsterFarm.RaiseMonsterBtn.onClick.RemoveListener(RaiseMonster);
            monsterFarm.RaiseMonsterBtn.onClick.AddListener(RaiseMonster);

            monsterFarm.FatigueSlider.maxValue = 100;
            monsterFarm.FatigueSlider.value = monsterInfo.ComplexFatigue;

            monsterFarm.StressSlider.maxValue = 100;
            monsterFarm.StressSlider.value = monsterInfo.ComplexStress;

            monsterFarm.LifespanSlider.maxValue = monsterInfo.MonsterScriptableData.MonsterGrowthParameters.Lifespan;
            monsterFarm.LifespanSlider.value = monsterInfo.ComplexLifespan;

            monsterFarm.HealthValueText.text = Mathf.RoundToInt(monsterInfo.ComplexHealth).ToString();
            monsterFarm.StrengthValueText.text = Mathf.RoundToInt(monsterInfo.ComplexStrength).ToString();
            monsterFarm.IntelligentValueText.text = Mathf.RoundToInt(monsterInfo.ComplexIntelligent).ToString();
            monsterFarm.DexterityValueText.text = Mathf.RoundToInt(monsterInfo.ComplexDexterity).ToString();
            monsterFarm.AgilityValueText.text = Mathf.RoundToInt(monsterInfo.ComplexAgility).ToString();
            monsterFarm.VitalityValueText.text = Mathf.RoundToInt(monsterInfo.ComplexVitality).ToString();

            RankTypeEnums healthRank = TrainingParameterValues.MonsterParameterToRank(monsterInfo.ComplexHealth);
            monsterFarm.HealthRankValueText.text = healthRank.ToString();
            monsterFarm.HealthRankValueText.color = GameComponentsSettingManager.Instance.GetRankColor(healthRank);

            RankTypeEnums strenthRank = TrainingParameterValues.MonsterParameterToRank(monsterInfo.ComplexStrength);
            monsterFarm.StrengthRankValueText.text = strenthRank.ToString();
            monsterFarm.StrengthRankValueText.color = GameComponentsSettingManager.Instance.GetRankColor(strenthRank);

            RankTypeEnums intelligentRank = TrainingParameterValues.MonsterParameterToRank(monsterInfo.ComplexIntelligent);
            monsterFarm.IntelligentRankValueText.text = intelligentRank.ToString();
            monsterFarm.IntelligentRankValueText.color = GameComponentsSettingManager.Instance.GetRankColor(intelligentRank);

            RankTypeEnums dexterityRank = TrainingParameterValues.MonsterParameterToRank(monsterInfo.ComplexDexterity);
            monsterFarm.DexterityRankValueText.text = dexterityRank.ToString();
            monsterFarm.DexterityRankValueText.color = GameComponentsSettingManager.Instance.GetRankColor(dexterityRank);

            RankTypeEnums agilityRank = TrainingParameterValues.MonsterParameterToRank(monsterInfo.ComplexAgility);
            monsterFarm.AgilityRankValueText.text = agilityRank.ToString();
            monsterFarm.AgilityRankValueText.color = GameComponentsSettingManager.Instance.GetRankColor(agilityRank);


            RankTypeEnums vitalityRank = TrainingParameterValues.MonsterParameterToRank(monsterInfo.ComplexVitality);
            monsterFarm.VitalityRankValueText.text = vitalityRank.ToString();
            monsterFarm.VitalityRankValueText.color = GameComponentsSettingManager.Instance.GetRankColor(vitalityRank);

            monsterFarm.MoodleList[0].SetStress(monsterInfo.ComplexStress);
            monsterFarm.MoodleList[1].SetFatigue(monsterInfo.ComplexFatigue);
            monsterFarm.MoodleList[2].SetDisease(monsterInfo.MonsterDiseasesCondition);
            monsterFarm.MoodleList[3].SetInjury(monsterInfo.MonsterInjuryCondition);

            int skillIndex = 0;
            for (int i = 0; i < monsterInfo.MonsterSkillLevel.Count; i++)
            {
                if (monsterInfo.MonsterSkillLevel[i].IsSelected)
                {
                    if (skillIndex >= monsterFarm.SkillList.Count) continue;
                    monsterFarm.SkillList[skillIndex].SetSkill(monsterInfo.MonsterSkillDetailDict[monsterInfo.MonsterSkillLevel[i].SkillId], monsterInfo.MonsterSkillLevel[i].SelectLevel);
                    skillIndex++;
                }
            }

            for (int i = skillIndex; i < monsterFarm.SkillList.Count; i++)
            {
                monsterFarm.SkillList[i].SetSkill(null, 0);
            }
            if (setup.FarmPosition == m_cardNumber)
            {
                var farm = GameDataManager.Instance.LoadedPlayerData.PlayerFarmList.FirstOrDefault(x => x.FarmId == farmInfo.FarmId);
                setup = new(m_cardNumber, monsterInfo.MonsterId, farm.FarmId);
            }
        }

        public void SetCardNumber(int cardNumber, UIFarm farmMenu)
        {
            this.m_cardNumber = cardNumber;
            unlockSlot.onClick.AddListener(() =>
            {
                UINotifyManager.AddNotifyResponse("Farm unlock", () =>
                {
                    string msg = string.Empty;
                    if (cardNumber == 1)
                        msg = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Unlock Second Farm");
                    if (cardNumber == 2)
                        msg = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Unlock Third Farm");
                    AssistantManager.Instance.SetDialog(msg, "normal", true);
                    AssistantManager.Instance.OnRequestSelected += SetFarmUnlock;
                }, UINotifyManager.NotifyType.System);
                UINotifyManager.ProcessNotify();
            });
            this.m_farmMenu = farmMenu;
            monsterFarm.RemoveMonsterBtn.onClick.AddListener(RemoveMonster);
            monsterFarm.ReplaceMonsterBtn.onClick.AddListener(ChangeMonster);
            monsterFarm.FarmReplaceBtn.onClick.AddListener(OpenFarmListMenu);
            emptyFarm.AddFarmBtn.onClick.AddListener(OpenFarmListMenu);
            emptyFarm.AddMonsterBtn.onClick.AddListener(OpenBagMenu);
            emptyCard.AddFarmBtn.onClick.AddListener(OpenFarmListMenu);
            monsterFarm.GeneralDetailBtn.onClick.AddListener(OpenGeneralDetail);
            monsterFarm.Battle1Btn.onClick.AddListener(OpenBattle1);
        }

        public void SetupMenu(UIFarmListManagerMenu farmListMenu, UIUpdateMonsterBagMenu monsterFarmBagUpdate, UIUpdateRaise farmDetail)
        {
            this.farmListMenu = farmListMenu;
            this.monsterFarmBagUpdate = monsterFarmBagUpdate;
            this.farmDetail = farmDetail;
        }

        internal void SetMonsterDetailMenu(UIMonsterDetail monsterDetailMenu)
        {
            this.monsterDetailMenu = monsterDetailMenu;
            for (int i = 0; i < monsterFarm.SkillList.Count; i++)
            {
                UIFarmCardSkill skillButton = monsterFarm.SkillList[i];
                skillButton.InitButtonFunction(monsterDetailMenu, monsterInfo, farmInfo);
            }
        }

        #region Private fuctions
        private void OpenBagMenu()
        {
            monsterFarmBagUpdate.SetTargetFarm(farmInfo.FarmId);
            monsterFarmBagUpdate.OpenAsMonsterBag(true);
        }

        private void OpenGeneralDetail()
        {
            monsterDetailMenu.SetMonsterData(monsterInfo, farmInfo);
            monsterDetailMenu.SetTargetPage(0);
        }

        private void OpenBattle1()
        {
            monsterDetailMenu.SetMonsterData(monsterInfo, farmInfo);
            monsterDetailMenu.SetTargetPage(1);
        }

        private void RemoveMonster()
        {
            var farmSetup = Managers.GameDataManager.Instance.LoadedPlayerData.PlayerFarmSetupList.Where(x => x.IsSelected).FirstOrDefault();
            if (farmSetup != null)
            {
                for (int i = 0; i < farmSetup.FarmSet.Count; i++)
                {
                    SerializeDataStruct.Data.SerializeFarmSet setup = farmSetup.FarmSet[i];
                    if (setup.FarmId == farmInfo.FarmId)
                    {
                        farmSetup.FarmSet[i] = new SerializeDataStruct.Data.SerializeFarmSet(setup.FarmPosition, string.Empty, setup.FarmId);
                        break;
                    }
                }
                GameDataManager.OnFarmDataChanged?.Invoke();
                GameDataManager.OnFarmSetupDataChanged?.Invoke();
            }
            m_farmMenu.DisplayController.RemoveFromDisplay(m_cardNumber);
        }

        private void ChangeMonster()
        {
            var farmSetup = GameDataManager.Instance.LoadedPlayerData.PlayerFarmSetupList.FirstOrDefault(x => x.IsSelected);
            var setup = farmSetup.FarmSet.FirstOrDefault(x => x.FarmId == farmInfo.FarmId);
            monsterFarmBagUpdate.SetTargetFarm(farmInfo.FarmId, setup.MonsterId);
            monsterFarmBagUpdate.OpenAsMonsterBag(true);
        }

        private void RaiseMonster()
        {
            farmDetail.SetInfo(monsterInfo, farmInfo, m_cardNumber);
            farmDetail.OpenMenu();
        }
        #endregion

        #region Serializeable
        [Serializable]
        private class EmptyCardComponents
        {
            [SerializeField] private Button addFarmBtn;
            [SerializeField] private GameObject mainComponent;

            public Button AddFarmBtn => addFarmBtn;
            public GameObject MainComponent => mainComponent;
        }

        [Serializable]
        private class EmptyFarmComponents
        {
            [SerializeField] private Button addMonsterBtn;
            [SerializeField] private Button addFarmBtn;
            [SerializeField] private Image terrainImage;
            [SerializeField] private Image enviromentImage;
            [SerializeField] private TextMeshProUGUI farmNameText;
            [SerializeField] private GameObject mainComponent;

            public Button AddMonsterBtn => addMonsterBtn;
            public Button AddFarmBtn => addFarmBtn;
            public Image TerrainImage => terrainImage;
            public Image EnviromentImage => enviromentImage;
            public GameObject MainComponent => mainComponent;
            public TextMeshProUGUI FarmNameText => farmNameText;

        }

        [Serializable]
        private class FarmMonsterComponents
        {
            [Header("General buttons")]
            [SerializeField] private Button raiseMonsterBtn;
            [SerializeField] private Button removeMonsterBtn;
            [SerializeField] private Button replaceMonsterBtn;
            [SerializeField] private Button farmReplaceBtn;
            [SerializeField] private Button generalDetailBtn;
            [SerializeField] private Button battle1Btn;
            [Space(5)]
            [SerializeField] private List<UIFarmMoodle> moodleList;
            [SerializeField] private Image enviromentImage;
            [SerializeField] private GameObject farmOn;
            [SerializeField] private GameObject farmOff;
            [SerializeField] private Image terrainImage;
            [SerializeField] private Image monsterRankImage;
            [SerializeField] private TextMeshProUGUI farmNameText;
            [SerializeField] private TextMeshProUGUI monsterNameText;
            [SerializeField] private GameObject mainComponent;
            [Header("Skill Component Data")]
            [SerializeField] private List<UIFarmCardSkill> skillList;
            [Header("Parameter components data")]
            [SerializeField] private Slider fatigueSlider;
            [SerializeField] private Slider stressSlider;
            [SerializeField] private Slider lifespanSlider;
            [Header("Health")]
            [SerializeField] private TextMeshProUGUI healthValueText;
            [SerializeField] private TextMeshProUGUI healthRankValueText;
            [Header("Strength")]
            [SerializeField] private TextMeshProUGUI strengthValueText;
            [SerializeField] private TextMeshProUGUI strengthRankValueText;
            [Header("Intelligent")]
            [SerializeField] private TextMeshProUGUI intelligentValueText;
            [SerializeField] private TextMeshProUGUI intelligentRankValueText;
            [Header("Dexterity")]
            [SerializeField] private TextMeshProUGUI dexterityValueText;
            [SerializeField] private TextMeshProUGUI dexterityRankValueText;
            [Header("Agility")]
            [SerializeField] private TextMeshProUGUI agilityValueText;
            [SerializeField] private TextMeshProUGUI agilityRankValueText;
            [Header("Vitality")]
            [SerializeField] private TextMeshProUGUI vitalityValueText;
            [SerializeField] private TextMeshProUGUI vitalityRankValueText;
            [SerializeField] private RawImage m_monsterRender;

            public Button RaiseMonsterBtn => raiseMonsterBtn;
            public Button RemoveMonsterBtn => removeMonsterBtn;
            public List<UIFarmMoodle> MoodleList => moodleList;
            public Image EnviromentImage => enviromentImage;
            public GameObject FarmOn => farmOn;
            public GameObject FarmOff => farmOff;

            public Image TerrainImage => terrainImage;
            public Image MonsterRankImage => monsterRankImage;
            public GameObject MainComponent => mainComponent;
            public TextMeshProUGUI FarmNameText => farmNameText;
            public TextMeshProUGUI MonsterNameText => monsterNameText;
            public Slider FatigueSlider => fatigueSlider;
            public Slider StressSlider => stressSlider;
            public Slider LifespanSlider => lifespanSlider;
            public TextMeshProUGUI HealthValueText => healthValueText;
            public TextMeshProUGUI HealthRankValueText => healthRankValueText;
            public TextMeshProUGUI StrengthValueText => strengthValueText;
            public TextMeshProUGUI StrengthRankValueText => strengthRankValueText;
            public TextMeshProUGUI IntelligentValueText => intelligentValueText;
            public TextMeshProUGUI IntelligentRankValueText => intelligentRankValueText;
            public TextMeshProUGUI DexterityValueText => dexterityValueText;
            public TextMeshProUGUI DexterityRankValueText => dexterityRankValueText;
            public TextMeshProUGUI AgilityValueText => agilityValueText;
            public TextMeshProUGUI AgilityRankValueText => agilityRankValueText;
            public TextMeshProUGUI VitalityValueText => vitalityValueText;
            public TextMeshProUGUI VitalityRankValueText => vitalityRankValueText;
            public List<UIFarmCardSkill> SkillList => skillList;
            public Button ReplaceMonsterBtn => replaceMonsterBtn;
            public Button FarmReplaceBtn => farmReplaceBtn;
            public Button GeneralDetailBtn => generalDetailBtn;
            public Button Battle1Btn => battle1Btn;
            public RawImage MonsterRender => m_monsterRender;
        }
        #endregion
    }
}