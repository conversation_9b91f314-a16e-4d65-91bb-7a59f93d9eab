using Assets.Scripts.Managers;
using Assets.Scripts.SerializeDataStruct.ItemData;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.FarmMenu
{
    public class UIFarmEnhanceSlotBtn : MonoBehaviour
    {
        [SerializeField] private Button controlButton;
        [SerializeField] private GameObject itemView;
        [SerializeField] private Image itemBackground;
        [SerializeField] private Image itemIconRank;
        [SerializeField] private Image itemImage;
        private SerializeItem enhanceItem;

        public SerializeItem EnhanceItem => enhanceItem;
        public Button ControlButton => controlButton;

        public void SetItem(SerializeItem enhanceItem)
        {
            this.enhanceItem = enhanceItem;
            controlButton.interactable = true;
            itemView.SetActive(true);
            itemIconRank.sprite = GameComponentsSettingManager.Instance.GetItemQualityIcon(enhanceItem.Item.ItemRarity);

            if (enhanceItem.CoachItemData != null && !string.IsNullOrEmpty(enhanceItem.CoachItemData.MonsterCoachFrom))
            {
                itemBackground.sprite = ItemDataManager.Instance.CoachSettingDict[enhanceItem.CoachItemData.CoachParameter];
                if (GameDataManager.Instance.LoadedPlayerData.PlayerMonsterDict.TryGetValue(enhanceItem.CoachItemData.MonsterCoachFrom, out var playerMonster))
                {
                    this.itemImage.sprite = playerMonster.MonsterScriptableData.MonsterScreenshot;
                }
                else
                {
                    for (int monsterIndex = 0; monsterIndex < MonsterDataManager.Instance.MonstersData.Count; monsterIndex++)
                    {
                        var monster = MonsterDataManager.Instance.MonstersData[monsterIndex];
                        //var monsterBasicName = Regex.Replace(monster.MonsterName, @"\[\w*\]", "").Trim();
                        if (enhanceItem.CoachItemData.MonsterPublicId.Equals(monster.MonsterPublicNumber))
                        {
                            this.itemImage.sprite = monster.MonsterScreenshot;
                            break;
                        }
                    }
                }
            }
            else
            {
                itemBackground.sprite = GameComponentsSettingManager.Instance.GetItemQualityBackground(enhanceItem.Item.ItemRarity);
                itemImage.sprite = enhanceItem.Item.ItemGraphic;
            }
        }

        public void RemoveItem()
        {
            enhanceItem = null;
            controlButton.interactable = false;
            itemView.SetActive(false);
        }

    }
}