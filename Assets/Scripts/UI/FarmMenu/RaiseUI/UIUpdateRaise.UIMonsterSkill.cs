using Assets.Scripts.DataStruct;
using UnityEngine;

namespace Assets.Scripts.UI.FarmMenu.RaiseUI
{
    public partial class UIUpdateRaise
    {
        [System.Serializable]
        private class UIMonsterSkill
        {
            [SerializeField] private System.Collections.Generic.List<UIRaiseSkillComponent> skillComponents;
            [SerializeField] private UIMonsterSkillDetail skillDetailMenu;
            [SerializeField] private UI.TrainningMenu.UIIntensiveTrainningMenu intensiveMenu;

            internal void SetMonster(SerializeMonster monsterInfo)
            {
                for (int i = 0; i < monsterInfo.MonsterScriptableData.SkillRegister.Count; i++)
                {
                    Scriptables.SkillDataScriptable skillData = monsterInfo.MonsterScriptableData.SkillRegister[i];
                    skillComponents[i].SetSkill(skillData, monsterInfo.MonsterSkillLevelDict[skillData.SkillId].SkillLevel, monsterInfo);
                    skillComponents[i].SkillButton.onClick.RemoveAllListeners();
                    skillComponents[i].SkillButton.onClick.AddListener(() => OpenSkillMenu(skillData, monsterInfo.MonsterSkillLevelDict[skillData.SkillId].SkillLevel));
                    skillComponents[i].SkillNotifyPopup.onClick.RemoveAllListeners();
                    skillComponents[i].SkillNotifyPopup.onClick.AddListener(() => intensiveMenu.SetMonsterData(monsterInfo, skillData));
                }
            }

            private void OpenSkillMenu(Scriptables.SkillDataScriptable skillData, int skillLevel)
            {
                skillDetailMenu.SetSkill(skillData, skillLevel);
                skillDetailMenu.OpenMenu();
            }
        }
    }
}