using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.FarmMenu.RaiseUI
{
    public class UIItemExploreObject : MonoBehaviour
    {
        [SerializeField] private Image itemBackground;
        [SerializeField] private Image itemImage;
        [SerializeField] private Image itemRank;
        [SerializeField] private TextMeshProUGUI itemCount;

        public void SetItem(Scriptables.Item.ScriptableItem itemData, int count)
        {
            itemBackground.sprite = Managers.GameComponentsSettingManager.Instance.GetItemQualityBackground(itemData.ItemRarity);
            itemImage.sprite = itemData.ItemGraphic;
            itemRank.sprite = Managers.GameComponentsSettingManager.Instance.GetItemQualityIcon(itemData.ItemRarity);
            itemCount.text = $"x{count}";
            gameObject.SetActive(true);
        }
    }
}