using System;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.Guild
{
    public class UIGuildEmblemToggle : MonoBehaviour
    {
        [SerializeField] private Toggle m_controlToggle;
        [SerializeField] private Image m_guildImage;
        private string m_emblemId;

        public void SetToggleData(string emblemId, Sprite guildImage, Action callback, ToggleGroup m_toggleGroup)
        {
            m_emblemId = emblemId;
            m_guildImage.sprite = guildImage;
            m_controlToggle.group = m_toggleGroup;
            m_controlToggle.onValueChanged.AddListener((bool val) =>
            {
                callback?.Invoke();
            });
        }
    }
}
