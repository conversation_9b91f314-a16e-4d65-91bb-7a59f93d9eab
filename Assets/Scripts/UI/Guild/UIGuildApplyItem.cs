using Assets.Scripts.Controllers;
using Assets.Scripts.DataStruct.Data.Guild;
using Assets.Scripts.Managers;
using System;
using System.Linq;
using System.Threading.Tasks;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.Guild
{
    public class UIGuildApplyItem : MonoBehaviour
    {
        [SerializeField] private Image m_guildImage;
        [SerializeField] private GameObject m_loadingOverrideGuildImage;
        [SerializeField] private TextMeshProUGUI m_guildName;
        [SerializeField] private TextMeshProUGUI m_guildLevel;
        [SerializeField] private TextMeshProUGUI m_guildRank;
        [SerializeField] private TextMeshProUGUI m_guildTotalScore;
        [SerializeField] private TextMeshProUGUI m_guildCycleScore;
        [SerializeField] private TextMeshProUGUI m_guildMonthScore;
        [SerializeField] private Image m_guildMasterImage;
        [SerializeField] private GameObject m_loadingOverrideGuildMasterImage;
        [SerializeField] private TextMeshProUGUI m_guildMasterName;
        [SerializeField] private TextMeshProUGUI m_guildMemberCount;
        [SerializeField] private Button m_removeApplication;
        [SerializeField] private Button m_guildInfoButton;
        private UIUserGuildSectorMenu m_infoMenu;
        private SerializeGuildData cachedGuild;

        private void Start()
        {
            m_removeApplication.onClick.AddListener(RemoveApplication);
            m_guildInfoButton.onClick.AddListener(SetGuildInfo);
        }

        public void SetInfoMenu(UIUserGuildSectorMenu infoMenu)
        {
            m_infoMenu = infoMenu;
        }

        private void RemoveApplication()
        {
            GuildDataController.RemoveApplies?.Invoke(new()
            {
                new()
                {
                    _applicationId = string.Empty,
                    _userWallet = GameDataManager.Instance.LoadedPlayerData.PlayerWallet,
                    _guildId = cachedGuild._guildId
                }
            });
        }

        private void SetGuildInfo()
        {
            m_infoMenu.SetCachedGuild(cachedGuild);
        }

        public async Task SetData(SerializeGuildData guild)
        {
            cachedGuild = guild;
            gameObject.SetActive(true);
            m_guildName.text = cachedGuild._guildName;
            m_guildLevel.text = cachedGuild._guildLevel.ToString();
            m_guildRank.text = "0";
            m_guildTotalScore.text = "0";
            m_guildCycleScore.text = "0";
            m_guildMonthScore.text = "0";
            m_guildMasterImage.gameObject.SetActive(false);
            m_loadingOverrideGuildMasterImage.SetActive(true);
            m_loadingOverrideGuildImage.SetActive(true);
            await SetGuildMasterInfoAsync();
        }

        private async Task SetGuildMasterInfoAsync()
        {
            m_guildMasterImage.gameObject.SetActive(false);
            var guildMasterInfo = await WalletUserDataManager.Instance.GetUser(cachedGuild._guildMemberList.Where(x => x._memberRole == Enums.GuildMemberRoleEnum.GuildMaster).FirstOrDefault()._memberWallet);
            m_guildMasterName.text = guildMasterInfo.UserName;
            m_guildMasterImage.gameObject.SetActive(true);
            m_guildMasterImage.sprite = guildMasterInfo.SpriteAvatar;
            m_guildMemberCount.text = $"{cachedGuild._guildMemberList.Count}/{GuildDataManager.Instance.GuildLevelLimit[cachedGuild._guildLevel - 1].MaxMemberLevel}";
            if (!string.IsNullOrEmpty(cachedGuild._guildImageString))
            {
                if (GuildDataManager.Instance.GuildEmblemId.Exists(x => x.Equals(cachedGuild._guildImageString)))
                {
                    m_guildImage.sprite = GuildDataManager.Instance.GuildEmblemSprite[int.Parse(cachedGuild._guildImageString)];
                }
                else
                {
                    m_guildImage.sprite = cachedGuild._guildImage;
                }
            }
            var startDate = Helpers.GetFirstDateOfMonth(DateTime.UtcNow.Year, DateTime.UtcNow.Month);
            var endDate = Helpers.GetLastDateOfMonth(DateTime.UtcNow.Year, DateTime.UtcNow.Month);
            var guildScoreRecord = GuildDataManager.Instance.GuildCycleScore;
            var cycle = CycleManager.Instance.GameCycles.FirstOrDefault(x => x.CycleStartTime <= startDate && x.CycleEndTime <= endDate);
            SerializeGuildCycleScore guildMonthCycle = GuildDataController.GetGuildCycleData(ref cachedGuild, in guildScoreRecord, startDate, endDate, cycle.CycleId);
            if (!string.IsNullOrEmpty(guildMonthCycle._id))
            {
                guildMonthCycle = new()
                {
                    _id = string.Empty,
                    _cycle = CycleManager.Instance.CurrentCycle.CycleId,
                    _startTime = startDate,
                    _endTime = endDate,
                    _oasEarned = "0",
                    _rankedScore = 0,
                    _exploreScore = 0,
                    _isFinallized = false,
                    _guildPlacement = 0
                };
            }
            m_guildRank.text = $"{guildMonthCycle._guildPlacement}";
            m_guildTotalScore.text = guildScoreRecord.Where(x=>x._guildId == cachedGuild._guildId).Sum(x => x._exploreScore + x._rankedScore).ToString("N0");
            m_guildCycleScore.text = guildScoreRecord.Where(x => x._guildId == cachedGuild._guildId && x._cycle == CycleManager.Instance.CurrentCycle.CycleId).Sum(x => x._exploreScore + x._rankedScore).ToString("N0");
            m_guildMonthScore.text = guildScoreRecord.Where(x => x._guildId == cachedGuild._guildId && x._startTime.Month == DateTime.UtcNow.Month && x._startTime.Year == DateTime.UtcNow.Year).Sum(x => x._exploreScore + x._rankedScore).ToString("N0");
            m_loadingOverrideGuildImage.SetActive(false);
            m_loadingOverrideGuildMasterImage.SetActive(false);
        }
    }
}
