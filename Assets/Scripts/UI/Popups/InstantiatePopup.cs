using Assets.Scripts.UI.Utilities;
using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.Popups
{
    public class InstantiatePopup : UIMenuStruct
    {
        [Header("Components")]
        [SerializeField] private TextMeshProUGUI popupLabel;
        [SerializeField] private TextMeshProUGUI popupContent;
        [SerializeField] private Button accecptBtn;
        [SerializeField] private Button denyBtn;
        private Action onAccecpt;
        private Action onDeny;

        private void Awake()
        {
            OpenMenu();
            accecptBtn.onClick.AddListener(() => { onAccecpt?.Invoke(); CloseMenu(); });
            denyBtn.onClick.AddListener(() => { onDeny?.Invoke(); CloseMenu(); });
            OnMenuClose.AddListener(() => Destroy(gameObject));
        }

        public void Setup(string popupLabel, string popupContent, Action onAccecpt, Action onDeny)
        {
            this.popupLabel.text = popupLabel;
            this.popupContent.text = popupContent;

            //apply new action
            this.onAccecpt = onAccecpt;
            this.onDeny = onDeny;
        }
    }
}
