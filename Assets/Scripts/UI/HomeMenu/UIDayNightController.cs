using System;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

namespace UI.HomeMenu
{
    public class UIDayNightController : MonoBehaviour
    {
        [SerializeField] private float changeAfterTime = 10f;
        [SerializeField] private float shiftTime = 3f;
        [SerializeField] private List<GameObject> dayItems;
        [SerializeField] private List<GameObject> sunsetItems;
        [SerializeField] private List<GameObject> nightItems;
        private readonly Dictionary<DayTimeCycle, List<GameObject>> _timeCycleItems = new();
        private float _shiftCountDown;
        private bool _canChangeNextDay;
        private DayTimeCycle _currentDaytime;

        private void Awake()
        {
            InitializeTimeCycleItems();
            _shiftCountDown = changeAfterTime;

            _currentDaytime = DayTimeCycle.Day;
            UpdateTimeCycleItems(_canChangeNextDay);
        }

        private void InitializeTimeCycleItems()
        {
            _timeCycleItems[DayTimeCycle.Day] = dayItems;
            _timeCycleItems[DayTimeCycle.Sunset] = sunsetItems;
            _timeCycleItems[DayTimeCycle.Night] = nightItems;
        }

        private void LateUpdate()
        {
            DayShiftTimer();
        }

        private void DayShiftTimer()
        {
            _shiftCountDown -= Time.deltaTime;
            if (_shiftCountDown > 0) { return; }
            _canChangeNextDay = true;
            _currentDaytime = GetNextDayTimeCycle(_currentDaytime);
            _shiftCountDown = changeAfterTime;

            // Start the DOTween sequence
            DOTween.Sequence()
                .AppendCallback(() => UpdateTimeCycleItems(true))
                .AppendInterval(shiftTime)
                .AppendCallback(() => UpdateTimeCycleItems(false))
                .Play();
        }

        private static DayTimeCycle GetNextDayTimeCycle(DayTimeCycle current)
        {
            return current switch
            {
                DayTimeCycle.Day => DayTimeCycle.Sunset,
                DayTimeCycle.Sunset => DayTimeCycle.Night,
                DayTimeCycle.Night => DayTimeCycle.Day,
                _ => throw new ArgumentOutOfRangeException(nameof(current), current, null)
            };
        }

        private void UpdateTimeCycleItems(bool isTransitioning)
        {
            foreach (var cycle in Enum.GetValues(typeof(DayTimeCycle)))
            {
                var cycleItems = _timeCycleItems[(DayTimeCycle)cycle];
                foreach (var item in cycleItems)
                {
                    var canvasGroup = item.GetComponent<CanvasGroup>();
                    if (!canvasGroup)
                    {
                        canvasGroup = item.AddComponent<CanvasGroup>();
                    }

                    if (isTransitioning)
                    {
                        // Use DOTween to fade in/out the items
                        canvasGroup.DOFade(cycle.Equals(_currentDaytime) ? 1 : 0, shiftTime).SetEase(Ease.InOutQuad);
                    }
                    else
                    {
                        canvasGroup.alpha = cycle.Equals(_currentDaytime) ? 1 : 0;
                    }
                }
            }
        }

        private enum DayTimeCycle
        {
            Day,
            Sunset,
            Night
        }
    }
}