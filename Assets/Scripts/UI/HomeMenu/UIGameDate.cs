using Assets.Scripts.Managers;
using TMPro;
using UnityEngine;

namespace Assets.Scripts.UI.HomeMenu
{
    public class UIGameDate : MonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI weekTimeText;
        [SerializeField] private TextMeshP<PERSON>UGUI monthTimeText;
        [SerializeField] private TextMeshProUGUI yearTimeText;

        private void Start()
        {
            GameDataManager.OnGameInfoUpdated += UpdateTimeText;
            UpdateTimeText();
        }

        private void OnDestroy()
        {
            GameDataManager.OnGameInfoUpdated -= UpdateTimeText;
        }

        private void UpdateTimeText()
        {
            weekTimeText.text = $" {GameProgressManager.Instance.CurrentGameWeek}";
            monthTimeText.text = $" {GameProgressManager.Instance.CurrentGameMonth}";
            yearTimeText.text = $" {GameProgressManager.Instance.CurrentGameYear}";
        }
    }
}