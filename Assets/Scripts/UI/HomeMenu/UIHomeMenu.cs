using System.Collections.Generic;
using System.Linq;
using System.Text;
using Assets.Scripts.Managers;
using Assets.Scripts.Models;
using Assets.Scripts.UI.BattleUI.PvP;
using Assets.Scripts.UI.FarmMenu;
using Assets.Scripts.UI.Utilities;
using DG.Tweening;
using I2.Loc;
using Network;
using TMPro;
using UI.Chat;
using UI.HomeMenu;
using UI.UIFriend;
using UI.Utilities;
using UnityEngine;

namespace Assets.Scripts.UI.HomeMenu
{
    public class UIHomeMenu : MonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI playerName;
        [SerializeField] private TextMeshProUGUI playerWallet;
        [SerializeField] private RectTransform topMenu;
        [SerializeField] private RectTransform bottomMenu;
        [SerializeField] private RectTransform sideMenuLeft;
        [SerializeField] private RectTransform floatingMenu;
        [SerializeField] private float showTime;
        [SerializeField] private float hideTime;
        [SerializeField] private Ease showEase;
        [SerializeField] private Ease hideEase;
        [SerializeField] private UIFarm farmMenu;
        [SerializeField] private List<UIMenuStruct> menuRegisterList;
        [SerializeField] private GameObject friendApprovalNotice1;

        private readonly Vector2 _hideTop = new(0, Screen.height);
        private readonly Vector2 _hideBottom = new(0, -Screen.height);
        private readonly Vector2 _hideLeft = new(-Screen.width, 0);
        private Vector2 _hideRight = new(Screen.width, 0);
        private List<UIMenuStruct> _currentOpenMenus = new();

        public bool isHomeOpen;

        private void OnEnable()
        {
            GameDataManager.OnPlayerInfoUpdated += SetPlayerData;
            FriendDataManager.Instance.OnFriendListChanged += OnApprovalListChanged;
            if (CustomPvpModeAI.Instance.IsTournamentMode)
            {
                CustomPvpModeAI.Instance.IsTournamentMode = false;
                farmMenu.OpenMenu();
            }
            if (CustomPvpModeAI.Instance.IsBattleMenuActive)
            {
                transform.root.GetComponentInChildren<UIPVPBattleMenu>(true).OpenMenu();
            }
        }
        private void OnDisable()
        {
            GameDataManager.OnPlayerInfoUpdated -= SetPlayerData;

            if (FriendDataManager.Instance)
            {
                FriendDataManager.Instance.OnFriendListChanged -= OnApprovalListChanged;
            }
        }

        private async void Start()
        {
            GameDataManager.OnPlayerInfoUpdated += SetPlayerData;
            FriendDataManager.Instance.GetAllData();
            OnApprovalListChanged();
            SetPlayerData();

            for (int i = 0; i < menuRegisterList.Count; i++)
            {
                var currentIndex = i;
                menuRegisterList[currentIndex].OnMenuOpen.AddListener(() =>
                {
                    _currentOpenMenus.Add(menuRegisterList[currentIndex]);
                    HideMainmenuComponents();
                });
            }

            for (int i = 0; i < menuRegisterList.Count; i++)
            {
                var currentIndex = i;
                menuRegisterList[currentIndex].OnMenuClose.AddListener(() =>
                {
                    _currentOpenMenus.Remove(menuRegisterList[currentIndex]);
                    if (_currentOpenMenus.Count == 0)
                    {
                        ShowMainmenuComponents();
                    }
                });
            }
            Time.timeScale = 1;
            if (TournamentDataManager.Instance && TournamentDataManager.Instance.IsReturnFromTournament)
            {
                TournamentDataManager.Instance.IsReturnFromTournament = false;
                Models.GameDataModel.LoadItemData();
                farmMenu.OpenMenu();
                HideMainmenuComponents();
                if (AssistantManager.Instance.CurrentTutorial == null || string.IsNullOrEmpty(AssistantManager.Instance.CurrentTutorial.TutorialKey))
                    MonsterInjuryAfterTournamentPvE();
            }
            var hasReconnection = await CustomNetworkManager.Instance.CheckIfMatch();

            if (hasReconnection == false) return;

            var defaultMessage = LocalizationManager.CurrentLanguageCode == "ja"
                ? "PvP中に切断されました。再接続しますか？"
                : "Disconnected during PvP. Would you like to reconnect?";

            UIPopupPanel.Instance.SetupReconnect("Reconnect", defaultMessage,
                () => { CustomNetworkManager.Instance.ReconnectNormal().GetAwaiter(); },
                () => { CustomNetworkManager.Instance.OnDenyReconnect().GetAwaiter(); });
            UIPopupPanel.Instance.OpenMenu();
        }

        private void SetPlayerData()
        {
            playerName.text = GameDataManager.Instance.LoadedPlayerData.PlayerName;
            playerWallet.text = GameDataManager.Instance.LoadedPlayerData.PlayerWallet.WalletShorten();
        }

        private void OnApprovalListChanged()
        {
            friendApprovalNotice1.SetActive(FriendDataManager.Instance.GetFriendApprovalList().Count > 0);
        }

        private void HideMainmenuComponents()
        {
            isHomeOpen = false;
            topMenu.DOKill();
            bottomMenu.DOKill();
            sideMenuLeft.DOKill();
            floatingMenu.gameObject.SetActive(false);
            topMenu.DOAnchorPos(_hideTop, hideTime).SetEase(hideEase)
                .OnComplete(() => topMenu.gameObject.SetActive(false));
            bottomMenu.DOAnchorPos(_hideBottom, hideTime).SetEase(hideEase)
                .OnComplete(() => bottomMenu.gameObject.SetActive(false));
            sideMenuLeft.DOAnchorPos(_hideLeft, hideTime).SetEase(hideEase)
                .OnComplete(() => sideMenuLeft.gameObject.SetActive(false));
        }

        private void ShowMainmenuComponents()
        {
            isHomeOpen = true;
            topMenu.DOKill();
            bottomMenu.DOKill();
            sideMenuLeft.DOKill();
            topMenu.gameObject.SetActive(true);
            bottomMenu.gameObject.SetActive(true);
            sideMenuLeft.gameObject.SetActive(true);
            topMenu.DOAnchorPos(Vector2.zero, showTime).SetEase(showEase);
            bottomMenu.DOAnchorPos(Vector2.zero, showTime).SetEase(showEase);
            sideMenuLeft.DOAnchorPos(Vector2.zero, showTime).SetEase(showEase)
                .OnComplete(() => floatingMenu.gameObject.SetActive(true));
            ChatMenuManager.Instance.OpenChatMenu();
            GameDataModel.LoadItemData();
        }

        private void MonsterInjuryAfterTournamentPvE()
        {
            SerializeDataStruct.SerializeTournament joinedTournament = GameDataManager.Instance.LoadedPlayerData
                .PlayerTournamentList.FirstOrDefault(x => x.TournamentYear == GameProgressManager.Instance.CurrentGameYear && x.TournamentDetail == TournamentDataManager.Instance.SelectedTournament);
            if (joinedTournament == null) return;
            TournamentDataManager.Instance.ResetTournament();
            #region Monster Condition Notice
            for (int i = 0; i < GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList.Count; i++)
            {
                DataStruct.SerializeMonster monster = GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList[i];
                if (!joinedTournament.TournamentParticipateList.Exists(x => x == monster.MonsterId)) continue;
                StringBuilder noticeString = new();
                #region Condition
                var statusString = string.Empty;
                if (monster.MonsterDiseasesCondition == Enums.DiseasesTypesEnum.Illness && monster.MonsterInjuryCondition == Enums.InjuryTypesEnum.SeriouslyInjured)
                    statusString = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Seriously Sick And Seriously Injured Notify");
                if (monster.MonsterDiseasesCondition == Enums.DiseasesTypesEnum.Illness && monster.MonsterInjuryCondition == Enums.InjuryTypesEnum.Injured)
                    statusString = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Injured And Seriously Sick Notify");
                if (monster.MonsterDiseasesCondition == Enums.DiseasesTypesEnum.Sick && monster.MonsterInjuryCondition == Enums.InjuryTypesEnum.SeriouslyInjured)
                    statusString = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Seriously injured and Sick Notify");
                if (monster.MonsterDiseasesCondition == Enums.DiseasesTypesEnum.Sick && monster.MonsterInjuryCondition == Enums.InjuryTypesEnum.Injured)
                    statusString = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Injured And Sick Notify");
                if (monster.MonsterDiseasesCondition == Enums.DiseasesTypesEnum.Illness && monster.MonsterInjuryCondition == Enums.InjuryTypesEnum.None)
                    statusString = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Seriously Sick Notify");
                if (monster.MonsterDiseasesCondition == Enums.DiseasesTypesEnum.Sick && monster.MonsterInjuryCondition == Enums.InjuryTypesEnum.None)// Todo: Fix sick reminder
                    statusString = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Sick Reminder");
                if (monster.MonsterDiseasesCondition == Enums.DiseasesTypesEnum.Sick && monster.MonsterInjuryCondition == Enums.InjuryTypesEnum.None)
                    statusString = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Sick Notify");
                if (monster.MonsterDiseasesCondition == Enums.DiseasesTypesEnum.None && monster.MonsterInjuryCondition == Enums.InjuryTypesEnum.SeriouslyInjured)
                    statusString = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Seriously Injured Notify");
                if (monster.MonsterDiseasesCondition == Enums.DiseasesTypesEnum.None && monster.MonsterInjuryCondition == Enums.InjuryTypesEnum.Injured)// Todo: Fix injury reminder
                    statusString = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Injured Reminder");
                if (monster.MonsterDiseasesCondition == Enums.DiseasesTypesEnum.None && monster.MonsterInjuryCondition == Enums.InjuryTypesEnum.Injured)
                    statusString = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Injured Notify");
                statusString = statusString.Replace("{monster_name}", monster.MonsterName);
                #endregion

                if (!string.IsNullOrEmpty(statusString.ToString()))
                {
                    UINotifyManager.AddNotifyResponse("Monster State", () =>
                    {
                        AssistantManager.Instance.SetDialog(statusString.ToString(), "confusion", false);
                    }, UINotifyManager.NotifyType.RaiseNotice, i);
                }
            }
            #endregion
            UINotifyManager.ProcessNotify();
        }
    }
}