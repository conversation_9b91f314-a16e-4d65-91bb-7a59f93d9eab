using System;
using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.Controllers;
using Assets.Scripts.DataStruct.Data;
using Assets.Scripts.DataStruct.Data.HashData;
using Assets.Scripts.Interfaces;
using Assets.Scripts.Managers;
using Assets.Scripts.Models;
using Assets.Scripts.Scriptables.Regeneration;
using Assets.Scripts.UI.Utilities;
using Cysharp.Threading.Tasks;
using I2.Loc;
using TMPro;
using UI.Chat;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.Association.Regeneration
{
    public class UIMonsterRegenerationMenu : UIMenuStruct
    {
        [SerializeField] private UIRegenerationTable regenerationTableSelection;
        [SerializeField] private Button fushionOptionBtn;
        [SerializeField] private Button selectNftRegenerateBtn;
        [SerializeField] private Button currentNftRegenerateBtn;
        [SerializeField] private Button regenerateBtn;
        [SerializeField] private Image nftImage;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private GameObject questionMarkIcon;
        [SerializeField] private Toggle freeRegenerateToggle;
        [SerializeField] private Toggle ticketRegenerateToggle;
        [SerializeField] private TextMeshProUGUI regenerateCost;
        [SerializeField] private GameObject costTypeIcon;
        [SerializeField] private GameObject ticketIcon;

        [SerializeField] private GameObject regenerateNameCard;
        [SerializeField] private TextMeshProUGUI regenerateObjectName;
        [SerializeField] private UIMonsterCreationMenu regenerateCompleteMenu;
        [SerializeField] private TextMeshProUGUI regenerationTicketCountText;
        [SerializeField] private ServerInfoSO serverInfo;
        [SerializeField] private RegenerationProbabilitySO regenerationProbabilitySO;
        [SerializeField] private List<RegenerationProbabilitySO> regenerationProbabilitySOs;
        [SerializeField] private RegenerationProbabilityCollabSO regenerationProbabilityCollabSOs;
        [SerializeField] private UIRegenerationAnimation regenerationAnimationUI;

        private IHashTypeObjects _regenerationTarget;

        private void Awake()
        {
            currentNftRegenerateBtn.gameObject.SetActive(false);
            selectNftRegenerateBtn.onClick.AddListener(() => regenerationTableSelection.SetMenu(this));
            currentNftRegenerateBtn.onClick.AddListener(() => regenerationTableSelection.SetMenu(this));
            OnMenuClose.AddListener(ResetView);
            regenerateBtn.onClick.AddListener(() =>
            {
                //float.TryParse(GameDataManager.Instance.LoadedPlayerData.PlayerOas, out var oasFloat);
                //if (.1f > oasFloat && !freeRegenerateToggle.isOn && !ticketRegenerateToggle.isOn)
                //{
                //    UINotifyManager.AddNotifyResponse("Mint perform", () =>
                //    {
                //        UIPopupNotify.Instance.SetNotify("Notice", I2.Loc.LocalizationManager.GetTranslation("General/Transaction Fee Warning"));
                //    }, UINotifyManager.NotifyType.System, 0);
                //    UINotifyManager.ProcessNotify();
                //}
                //else
                    GenerateMonster();
            });
            regenerateCompleteMenu.OnMenuClose.AddListener(ResetView);
            ResetView();
            freeRegenerateToggle.onValueChanged.AddListener((value) =>
            {
                if (value)
                {
                    regenerateCost.text = "Free";
                    float.TryParse(GameDataManager.Instance.LoadedPlayerData.PlayerOas, out var oasFloat);
                    regenerateBtn.interactable = true;
                }
                else SetRegenerateTarget(_regenerationTarget);
            });
            ticketRegenerateToggle.onValueChanged.AddListener((value) =>
            {
                if (value)
                {
                    float.TryParse(GameDataManager.Instance.LoadedPlayerData.PlayerOas, out var oasFloat);
                    regenerateBtn.interactable = true;
                    //if (oasFloat < 1)
                    //{
                    //    UINotifyManager.AddNotifyResponse("Not enough oas", () =>
                    //    {
                    //        UIPopupNotify.Instance.SetNotify("Notice", "Not enough oas for network transfer fee");
                    //    }, UINotifyManager.NotifyType.DefaultNotify);
                    //    UINotifyManager.ProcessNotify();
                    //}
                    regenerateCost.text = "x1";
                    costTypeIcon.SetActive(false);
                    ticketIcon.SetActive(true);
                }
                else
                {
                    costTypeIcon.SetActive(true);
                    ticketIcon.SetActive(false);
                    SetRegenerateTarget(_regenerationTarget);
                }
            });
            OnMenuOpen.AddListener(ResetView);
        }

        private void GenerateMonster()
        {
            ChatMenuManager.Instance.CloseChatMenu();
            GenerateMonsterNFT().GetAwaiter();
            regenerateCompleteMenu.IndexMenu = 0;
        }

        public void ResetData()
        {
            ResetView();
        }

        private async UniTask GenerateMonsterNFT()
        {
            var loading = BackendLoadData.Instance.LoadingCanvas();
            var regenerateCycleData = new RegenerationDataRecive();
            string contractAddress = string.Empty;
            bool oasTypeRegen = false;
            try
            {
                switch (_regenerationTarget)
                {
                    case SerializeGeneralHash generalHash:
                        oasTypeRegen = true;
                        contractAddress = SmartContractInteract.Instance.ContractAddress.GeneralHashAddress;
                        break;
                    case SerializeGenesisHash genesisHash:
                        oasTypeRegen = true;
                        contractAddress = SmartContractInteract.Instance.ContractAddress.GenesisHashAddress;
                        break;
                    case SerializeHashChip hashChip:
                        oasTypeRegen = true;
                        contractAddress = SmartContractInteract.Instance.ContractAddress.ADDRESS_CONTRACT_HASHCHIP;
                        hashChip.SetMonsterTarget(regenerationProbabilitySO.GetMonsterRandom());
                        break;
                    case SerializeHashItem itemHash:
                        contractAddress = SmartContractInteract.Instance.ContractAddress.RegenerationItem;
                        if (itemHash.MainSeed == Enums.SeedsEnum.Random && itemHash.SubSeed == Enums.SeedsEnum.Random)
                        {
                            var monster = regenerationProbabilitySO.GetMonsterRandom();
                            _regenerationTarget = new SerializeHashItem(itemHash.HashId, itemHash.HashSprite, itemHash.HashName, monster.MonsterMainSeed, monster.MonsterSubSeed, itemHash.HashCount);
                            (_regenerationTarget as SerializeHashItem).SetLockMonster(monster);
                        }
                        break;
                    case SerializeRandomHash randomHash:
                        oasTypeRegen = true;
                        contractAddress = SmartContractInteract.Instance.ContractAddress.ADDRESS_CONTRACT_REGEN_FUSION;
                        randomHash.SetMonsterTarget(regenerationProbabilitySO.GetMonsterRandom());
                        if (AssistantManager.Instance.CurrentTutorial != null && !string.IsNullOrEmpty(AssistantManager.Instance.CurrentTutorial.TutorialKey))
                        {
                            while (randomHash.MonsterData.MonsterPublicNumber == 96)
                            {
                                randomHash.SetMonsterTarget(regenerationProbabilitySO.GetMonsterRandom());
                            }
                        }
                        break;
                    case SerializeNFTHash nftHash:
                        {
                            oasTypeRegen = true;
                            contractAddress = nftHash.ContractAddress;
                            switch (nftHash.TypeOfHash)
                            {
                                case "NFT":
                                    {
                                        List<int> availableChain = new();
                                        foreach (var item in UserNftDataManager.Instance.AvailableChain)
                                        {
                                            availableChain.Add(ChainDataCollection.GetChainID(item));
                                        }
                                        if (availableChain.Contains(nftHash.ChainId) && nftHash.MonsterData == null)
                                        {
                                            nftHash.SetMonsterTarget(regenerationProbabilitySOs[0].GetMonsterRandom());
                                        }
                                        if (nftHash.MonsterData == null)
                                        {
                                            nftHash.SetMonsterTarget(regenerationProbabilitySO.GetMonsterRandom());
                                        }
                                        break;
                                    }
                                case "COLLABORATION_NFT":
                                    {
                                        if (nftHash.MonsterData == null)
                                            nftHash.SetMonsterTarget(regenerationProbabilityCollabSOs.GetMonsterRandom(nftHash.CollectionName, nftHash.ChainId, nftHash.MainSeed));
                                        break;
                                    }
                            }
                            regenerateCycleData = new()
                            {
                                chain_id = nftHash.ChainId,
                                identifier = nftHash.HashId,
                                collection = nftHash.CollectionName,
                                contract = nftHash.ContractAddress,
                                chain = ChainDataCollection.GetChainById(nftHash.ChainId).ChainPublicName,
                                name = nftHash.HashName,
                                main_seed = (int)nftHash.MonsterData.MonsterMainSeed,
                                sub_seed = (int)nftHash.MonsterData.MonsterSubSeed,
                                monster_id = nftHash.MonsterData.MonsterPublicNumber,
                                cycle = CycleManager.Instance.CurrentCycle.CycleName
                            };
                            break;
                        }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError(ex.Message);
            }
            try
            {
                var result = await RegenerationController.PerformHashRegenerate(GameDataManager.Instance.LoadedPlayerData.PlayerWallet, _regenerationTarget, ticketRegenerateToggle.isOn, freeRegenerateToggle.isOn, serverInfo.BackendUrl, contractAddress);
                regenerationAnimationUI.SetMonsterToAnimation(null, _regenerationTarget, null, freeRegenerateToggle.isOn, ticketRegenerateToggle.isOn, regenerationTicketCountText.text);
                Destroy(loading);
                for (var i = 0; i < 30; i++)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(2));
                    if (await Controllers.RegenerationController.CheckRandomMonsterBlockchain(result.Item1))
                    {
                        if (ticketRegenerateToggle.isOn == false && freeRegenerateToggle.isOn == false && oasTypeRegen)
                            BackendLoadData.Instance.LogEvent("mint_monster", "monster_regeneration", $"game txid {result.Item1} nft_id {result.Item2} cost {_regenerationTarget.HashCost}");
                        int[] tokenIds = new int[1];
                        if (!int.TryParse(_regenerationTarget.HashId, out tokenIds[0]))
                            tokenIds[0] = -1;
                        int typeMint = Helpers.GetMintType(_regenerationTarget.TypeOfHash);
                        GameDataManager.AddRegenHistory?.Invoke(result.Item1, new()
                        {
                            typeMint = typeMint,
                            addressContract = contractAddress,
                            chainId = _regenerationTarget.ChainId,
                            account = GameDataManager.Instance.LoadedPlayerData.PlayerWallet,
                            tokenIds = tokenIds
                        }, _regenerationTarget.HashName);
                        break;
                    }
                }
                var foundMonster = false;
                Debug.Log($"Monster result: {result.Item2}");
                GameDataModel.LoadMonsterData();
                while (GameDataModel.IsLoadingMonster)
                    await UniTask.WaitForSeconds(5);
                for (int i = 0; i < GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList.Count; i++)
                {
                    var monsterData = GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList[i];
                    if (monsterData.MonsterNFTId == result.Item2 && result.Item2 >= 0)
                    {
                        foundMonster = true;
                        if (_regenerationTarget as SerializeNFTHash != null)
                        {
                            GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList[i].SetImage((_regenerationTarget as SerializeNFTHash).NftImageUrl);
                        }
                        monsterData = GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList[i];
                        regenerationAnimationUI.SetDataMonster(monsterData, () =>
                        {
                            regenerateCompleteMenu.SetMonster(monsterData);
                            regenerateCompleteMenu.OpenMenu();
                        });
                        GameProgressManager.Instance.GameProgress.AddMonsterObtained(monsterData.MonsterScriptableData.MonsterPublicNumber);
                        break;
                    }
                }

                if (!foundMonster)
                {
                    regenerationAnimationUI.TurnOffRegenAnimation();
                    var msg = LocalizationManager.CurrentLanguageCode == "ja" ? "処理がタイムアウトしました。\n後ほど改めてMonsterバッグを確認してください。\nMonsterを受け取れない場合は、トランザクションIDをお知らせください。" : "Monster regeneration timeout!\nPlease check your monster bag later for the require monster!\nIf there are no monster receive please send the transaction id for development team for checkup!";
                    UINotifyManager.AddNotifyResponse("Mint monster", () => UIPopupNotify.Instance.SetNotify("Notice!", msg), UINotifyManager.NotifyType.System);
                    UINotifyManager.ProcessNotify();
                    AssistantManager.Instance.ClearCurrentTutorial();
                    ResetView();
                }

                // Destroy(loading);
                if (AssistantManager.Instance.CurrentTutorial == null || string.IsNullOrEmpty(AssistantManager.Instance.CurrentTutorial.TutorialKey))
                {
                    UINotifyManager.AddNotifyResponse("Regeneration Success", () =>
                    {
                        LocalizationManager.TryGetTranslation("Assistance Msg General/Regeneration Result", out var msg);
                        AssistantManager.Instance.SetDialog(msg, "smile", false);
                        AssistantController.SetInteractionBlock();
                    }, UINotifyManager.NotifyType.System, 0);
                }
                if (!string.IsNullOrEmpty(regenerateCycleData.identifier))
                {
                    RegenerationDataRecive existRegenCyle = UserNftDataManager.Instance.Nft_regenerateCycleData.FirstOrDefault(x => x.cycle == regenerateCycleData.cycle && x.chain_id == regenerateCycleData.chain_id && x.contract == regenerateCycleData.contract && x.identifier == regenerateCycleData.identifier);
                    if (existRegenCyle == null)
                    {
                        GameDataManager.OnUpdateRegenerateData?.Invoke(regenerateCycleData);
                    }
                }
                GameProgressManager.Instance.GameProgress.AddMintMonsterSuccess();
                if (_regenerationTarget is not SerializeHashItem)
                    MintNon_FreeRegenerationItem();
                GameProgressManager.Instance.WeeklyProgress.SetTotalMonsterMint(GameProgressManager.Instance.WeeklyProgress.TotalMonsterRegenerate + 1);
            }
            catch (Exception e)
            {
                UINotifyManager.AddNotifyResponse("Regeneration Fail",
                            () =>
                            {
                                var message = LocalizationManager.CurrentLanguageCode == "ja"
                                    ? "Monsterの再生に失敗しました。"
                                    : "Fail To Regenerate Monster.";
                                UIPopupNotify.Instance.SetNotify("Regeneration Fail",
                                    $"{message}",
                                    UINotifyManager.ProcessNotify);
                            },
                            UINotifyManager.NotifyType.System);
                UINotifyManager.ProcessNotify();
                ChatMenuManager.Instance.OpenChatMenu();
                ResetView();
                Destroy(loading);
            }
            if (ticketRegenerateToggle.isOn)
                GameDataModel.LoadItemData();
            BackendLoadData.Instance.LoadOasBalance();
            UserNftDataManager.Instance.GetRegerateData();
        }

        private void MintNon_FreeRegenerationItem()
        {
            if (freeRegenerateToggle.isOn) return;
            var resultItem = RegenerationController.ItemObtainRandomize();
            try
            {
                if (resultItem == 0)
                {
                    if (AssistantManager.Instance.CurrentTutorial == null || string.IsNullOrEmpty(AssistantManager.Instance.CurrentTutorial.TutorialKey))
                    {
                        UINotifyManager.AddNotifyResponse("Regeneration Item Mint Success", () =>
                        {
                            UIPopupItemReceive.Instance?.SetItem(new List<Scriptables.Item.ScriptableItem>() { ItemDataManager.Instance.ItemDict["TOURNAMENT_TICKET_UC"] });
                            UIPopupItemReceive.Instance?.SetMenu("Ticket Bonus");
                        }, UINotifyManager.NotifyType.System, 1);
                    }
                    GameDataController.AddItemBalance(new() { new(ItemDataManager.Instance.ItemDict["TOURNAMENT_TICKET_UC"], 1) }, new() { 1 }, "regeneration_paid", true);
                }
                if (resultItem == 1)
                {
                    if (AssistantManager.Instance.CurrentTutorial == null || string.IsNullOrEmpty(AssistantManager.Instance.CurrentTutorial.TutorialKey))
                    {
                        UINotifyManager.AddNotifyResponse("Regeneration Item Mint Success", () =>
                        {
                            UIPopupItemReceive.Instance?.SetItem(new List<Scriptables.Item.ScriptableItem>() { ItemDataManager.Instance.ItemDict["EXPLORATION_TICKET_UC"] });
                            UIPopupItemReceive.Instance?.SetMenu("Ticket Bonus");
                        }, UINotifyManager.NotifyType.System, 1);
                    }
                    GameDataController.AddItemBalance(new() { new(ItemDataManager.Instance.ItemDict["EXPLORATION_TICKET_UC"], 1) }, new() { 1 }, "regeneration_paid", true);
                }
                if (resultItem == 2)
                {
                    if (AssistantManager.Instance.CurrentTutorial == null || string.IsNullOrEmpty(AssistantManager.Instance.CurrentTutorial.TutorialKey))
                    {
                        UINotifyManager.AddNotifyResponse("Regeneration Item Mint Success", () =>
                        {
                            UIPopupItemReceive.Instance?.SetItem(new List<Scriptables.Item.ScriptableItem>() { ItemDataManager.Instance.ItemDict["REGENERATION_TICKET_R"] });
                            UIPopupItemReceive.Instance?.SetMenu("Ticket Bonus");
                        }, UINotifyManager.NotifyType.System, 1);
                    }
                    GameDataController.AddItemBalance(new() { new(ItemDataManager.Instance.ItemDict["REGENERATION_TICKET_R"], 1) }, new() { 1 }, "regeneration_paid", true);
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        private void ResetView()
        {
            freeRegenerateToggle.SetIsOnWithoutNotify(false);
            currentNftRegenerateBtn.gameObject.SetActive(false);
            currentNftRegenerateBtn.gameObject.SetActive(true);
            regenerateBtn.interactable = false;
            regenerateObjectName.text = string.Empty;
            regenerateNameCard.SetActive(false);
            currentNftRegenerateBtn.gameObject.SetActive(false);
            regenerateCost.text = "0";
            var useTicket = ticketRegenerateToggle.isOn ? 1 : 0;
            var ticket = GameDataManager.Instance.LoadedPlayerData.PlayerItemList.FirstOrDefault(x => x.Item.ItemId == "REGENERATION_TICKET_R");
            var subTicket = GameDataManager.Instance.LoadedPlayerData.PlayerItemList.FirstOrDefault(x => x.Item.ItemId == "REGENERATION_TICKET_B");
            if (ticket != null && subTicket != null)
                regenerationTicketCountText.text = $"x{ticket.Quantity + subTicket.Quantity - useTicket}";
            else if (ticket != null)
                regenerationTicketCountText.text = $"x{ticket.Quantity - useTicket}";
            else if (subTicket != null)
                regenerationTicketCountText.text = $"x{subTicket.Quantity - useTicket}";
            else
                regenerationTicketCountText.text = "x0";
            ticketRegenerateToggle.SetIsOnWithoutNotify(false);
            freeRegenerateToggle.interactable = false;
            ticketRegenerateToggle.interactable = false;
            costTypeIcon.SetActive(true);
            ticketIcon.SetActive(false);
        }

        public void SetRegenerateTarget(IHashTypeObjects regenerationItem)
        {
            if (regenerationItem == null)
            {
                ResetView(); return;
            }
            ticketRegenerateToggle.isOn = false;
            freeRegenerateToggle.isOn = false;
            var ticket = GameDataManager.Instance.LoadedPlayerData.PlayerItemList.FirstOrDefault(x => x.Item.ItemId == "REGENERATION_TICKET_R");
            var subTicket = GameDataManager.Instance.LoadedPlayerData.PlayerItemList.FirstOrDefault(x => x.Item.ItemId == "REGENERATION_TICKET_B");
            var totalTicketCount = 0;
            if (ticket != null && subTicket != null)
                totalTicketCount = ticket.Quantity + subTicket.Quantity;
            else if (ticket != null)
                totalTicketCount = ticket.Quantity;
            else if (subTicket != null)
                totalTicketCount = subTicket.Quantity;

            _regenerationTarget = regenerationItem;
            regenerateObjectName.text = regenerationItem.HashName;
            regenerateNameCard.SetActive(true);
            regenerateBtn.interactable = true;
            currentNftRegenerateBtn.gameObject.SetActive(true);
            nftImage.gameObject.SetActive(false);
            switch (regenerationItem)
            {
                case SerializeGeneralHash generalHash:
                    nftImage.gameObject.SetActive(true);
                    backgroundImage.sprite = ItemDataManager.Instance.BackgroundNft[1];
                    nftImage.sprite = MonsterDataManager.Instance.MonsterSeedHashDictData[generalHash.MonsterSeed];
                    freeRegenerateToggle.interactable = !GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList.Exists(x => x.IsFree && !x.IsMemory);
                    ticketRegenerateToggle.interactable = totalTicketCount > 0;
                    regenerateCost.text = regenerationItem.HashCost;
                    questionMarkIcon.gameObject.SetActive(false);
                    break;
                case SerializeGenesisHash genesisHash:
                    nftImage.gameObject.SetActive(true);
                    backgroundImage.sprite = ItemDataManager.Instance.BackgroundNft[0];
                    nftImage.sprite = MonsterDataManager.Instance.MonsterSeedHashDictData[genesisHash.MonsterSeed];
                    freeRegenerateToggle.interactable = !GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList.Exists(x => x.IsFree && !x.IsMemory);
                    ticketRegenerateToggle.interactable = totalTicketCount > 0;
                    regenerateCost.text = regenerationItem.HashCost;
                    questionMarkIcon.gameObject.SetActive(false);
                    break;
                case SerializeHashItem:
                    nftImage.gameObject.SetActive(true);
                    backgroundImage.sprite = ItemDataManager.Instance.BackgroundNft[2];
                    nftImage.sprite = regenerationItem.HashSprite;
                    freeRegenerateToggle.interactable = false;
                    ticketRegenerateToggle.interactable = false;
                    regenerateCost.text = "0";
                    questionMarkIcon.gameObject.SetActive(false);
                    break;
                case SerializeNFTHash:
                    nftImage.gameObject.SetActive(true);
                    nftImage.sprite = regenerationItem.HashSprite;
                    backgroundImage.sprite = ItemDataManager.Instance.BackgroundNft[2];
                    freeRegenerateToggle.interactable = !GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList.Exists(x => x.IsFree && !x.IsMemory);
                    ticketRegenerateToggle.interactable = totalTicketCount > 0;
                    regenerateCost.text = regenerationItem.HashCost;
                    questionMarkIcon.gameObject.SetActive(false);
                    break;
                case SerializeRandomHash:
                    nftImage.gameObject.SetActive(false);
                    nftImage.sprite = regenerationItem.HashSprite;
                    freeRegenerateToggle.interactable = !GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList.Exists(x => x.IsFree && !x.IsMemory);
                    ticketRegenerateToggle.interactable = totalTicketCount > 0;
                    regenerateCost.text = $"{10f / BlockchainLoadData.Instance.TemaPriceToday.Tema:N2}";
                    questionMarkIcon.gameObject.SetActive(true);
                    break;
                case SerializeHashChip:
                    nftImage.gameObject.SetActive(true);
                    nftImage.sprite = regenerationItem.HashSprite;
                    freeRegenerateToggle.interactable = !GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList.Exists(x => x.IsFree && !x.IsMemory);
                    ticketRegenerateToggle.interactable = totalTicketCount > 0;
                    regenerateCost.text = regenerationItem.HashCost;
                    questionMarkIcon.gameObject.SetActive(false);
                    break;
            }

            ValidateRegeneration();
        }

        private void ValidateRegeneration()
        {
            float.TryParse(GameDataManager.Instance.LoadedPlayerData.PlayerOas, out var oasFloat);
            float oasCost;
            switch (_regenerationTarget)
            {
                case SerializeGeneralHash generalHash:
                    if (generalHash.HashCount < 3)
                    {
                        float.TryParse(generalHash.HashCost, out oasCost);
                        if (oasCost > oasFloat)
                            regenerateBtn.interactable = false;
                        else
                            regenerateBtn.interactable = true;
                    }
                    else
                    {
                        regenerateBtn.interactable = false;
                    }
                    break;
                case SerializeGenesisHash genesisHash:
                    if (genesisHash.HashCount < 5)
                    {
                        float.TryParse(genesisHash.HashCost, out oasCost);
                        if (oasCost > oasFloat)
                            regenerateBtn.interactable = false;
                        else
                            regenerateBtn.interactable = true;
                    }
                    else
                    {
                        regenerateBtn.interactable = false;
                    }
                    break;
                case SerializeNFTHash nftHash:
                    if (nftHash.HashCount < 3)
                    {
                        float.TryParse(nftHash.HashCost, out oasCost);
                        if (oasCost > oasFloat)
                            regenerateBtn.interactable = false;
                        else
                            regenerateBtn.interactable = true;
                    }
                    else
                    {
                        regenerateBtn.interactable = false;
                    }
                    break;
                case SerializeHashItem itemHash:
                    regenerateBtn.interactable = true;
                    break;
                case SerializeRandomHash randomHash:
                    oasCost = 10f / (float)BlockchainLoadData.Instance.TemaPriceToday.Tema;
                    if (oasCost > oasFloat)
                        if (oasCost > oasFloat)
                            regenerateBtn.interactable = false;
                        else
                            regenerateBtn.interactable = true;
                    break;
                default:
                    regenerateBtn.interactable = false;
                    break;
            }
        }
    }
}
