using Assets.Scripts.DataStruct;
using Assets.Scripts.DataStruct.Data.HashData;
using Assets.Scripts.Managers;
using Assets.Scripts.UI.Utilities;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using TMPro;
using UI.Chat;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.Association.Regeneration
{
    internal class UIRegenerationAnimation : MonoBehaviour
    {
        [SerializeField] private UIMonster3dDisplayRender m_displayer;
        [SerializeField] private RawImage m_monsterViewer;
        [SerializeField] private Transform shineContainer;
        [SerializeField] private GameObject effectControl;
        [SerializeField] private GameObject shineObject;
        [SerializeField] private GameObject simpleShineObject;
        [SerializeField] private GameObject blueParticle;
        [SerializeField] private RectTransform monsterHash;
        [SerializeField] private Image hashImage;
        [SerializeField] private GameObject questionMark;
        [SerializeField] private Image hashBackground;
        [SerializeField] private Image dimImage;
        [SerializeField] private UIMonsterCreationMenu creationMenu;

        [Space(10)]
        [SerializeField] private Vector2 setPosition;
        [SerializeField] private Vector3 hashRotation;
        [SerializeField] private Quaternion monsterRotation;
        [SerializeField] private float hashRotateTime;
        [SerializeField] private Vector3 shineScale;

        [Space(10)]
        [SerializeField] private float hashScaleDownTime;
        [SerializeField] private float timeTillActiveOrb;
        [SerializeField] private float timeTillSpawn;
        [SerializeField] private float timeSetPosition;
        [SerializeField] private float timeTillShine;
        [SerializeField] private float shineScaleTime;

        [Space(10)]
        [SerializeField] private Ease scaleDownEase;
        [SerializeField] private Ease rotateEase;
        [SerializeField] private Ease hashMoveEase;
        [SerializeField] private AnimationCurve shineEase;
        [SerializeField] private UIBlankControl regenMenuComponents;
        [SerializeField] private UIBlankControl resultMenuComponents;
        [SerializeField] private Toggle freeToggle;
        [SerializeField] private Toggle ticketToggle;
        [SerializeField] private GameObject ticketIcon;
        [SerializeField] private GameObject oasIcon;
        [SerializeField] private TextMeshProUGUI costText;
        [SerializeField] private TextMeshProUGUI ticketCountText;
        private System.Action onClose;
        private SerializeMonster monster;
        private float orbSpawnCountdown = 0;
        private float monsterSpawnCountdown = 0;
        private float shineCountdown = 0;
        private float disableCountdown = 0;
        private bool isMonsterSet = false;

        private void Awake()
        {
            m_monsterViewer.texture = m_displayer.GetRender(2);
        }

        private void Update()
        {
            SetSpawnOrb();
            SetSpawnMonster();
            SetSpawnShine();
            SetActiveFalse();
        }

        private void OnDisable()
        {
            resultMenuComponents.CloseMenu();
            ChatMenuManager.Instance.OpenChatMenu();
            regenMenuComponents.OpenMenu();
            creationMenu.OpenMenu();
        }
        [ContextMenu("Test animation")]
        private void TestAnimation()
        {
            SetMonsterToAnimation(SerializeMonster.CreateDefaultMonster(fakeMonster), new SerializeGeneralHash(string.Empty, "0", 0, string.Empty, "golem"), null, false, false, string.Empty);
        }
        [SerializeField] private Scriptables.MonsterScriptable fakeMonster;
        [ContextMenu("Fake monster")]
        private void FakeMonster()
        {
            SetDataMonster(SerializeMonster.CreateDefaultMonster(fakeMonster), null);
        }

        public void SetDataMonster(SerializeMonster monster, System.Action onClose)
        {
            this.monster = monster;
            this.onClose = onClose;
            shineObject.SetActive(true);
            shineObject.transform.DOScale(shineScale, shineScaleTime).SetEase(shineEase);
        }

        public void SetMonsterToAnimation(SerializeMonster monster, Interfaces.IHashTypeObjects hashUse, System.Action onClose, bool isFree, bool isTicketUse, string ticketCount)
        {
            m_displayer.RemoveFromDisplay(2);
            this.onClose = onClose;
            this.monster = monster;
            timeTillShine = 0;
            regenMenuComponents.CloseMenu();
            monsterHash.localScale = Vector3.one;
            monsterHash.anchoredPosition = new(0, -80);
            shineContainer.DestroyChildrenHelper();
            shineObject.transform.localScale = Vector3.zero;
            resultMenuComponents.CloseMenu();
            dimImage.color = Color.clear;
            dimImage.DOColor(new(0, 0, 0, 0.8f), hashScaleDownTime + 1).SetEase(Ease.InQuad);
            blueParticle.SetActive(true);

            switch (hashUse)
            {
                case SerializeGeneralHash generalHash:
                    hashImage.sprite = MonsterDataManager.Instance.MonsterSeedHashDictData[generalHash.MonsterSeed];
                    hashBackground.sprite = Managers.ItemDataManager.Instance.BackgroundNft[1];
                    questionMark.SetActive(false);
                    hashImage.color = Color.white;
                    break;
                case SerializeRandomHash randomHash:
                    hashImage.color = Color.clear;
                    hashBackground.sprite = Managers.ItemDataManager.Instance.BackgroundNft[1];
                    questionMark.SetActive(true);
                    break;
                case SerializeGenesisHash genesisHash:
                    hashBackground.sprite = Managers.ItemDataManager.Instance.BackgroundNft[0];
                    hashImage.sprite = MonsterDataManager.Instance.MonsterSeedHashDictData[genesisHash.MonsterSeed];
                    questionMark.SetActive(false);
                    hashImage.color = Color.white;
                    break;
                case SerializeHashItem:
                case SerializeNFTHash:
                    hashBackground.sprite = Managers.ItemDataManager.Instance.BackgroundNft[2];
                    if (hashUse.HashSprite == null)
                    {
                        hashImage.color = Color.clear;
                        questionMark.SetActive(true);
                    }
                    else
                    {
                        hashImage.color = Color.white;
                        hashImage.sprite = hashUse.HashSprite;
                        questionMark.SetActive(false);
                    }
                    break;
            }
            gameObject.SetActive(true);
            monsterHash.DORotate(hashRotation, hashRotateTime, RotateMode.FastBeyond360).SetEase(rotateEase).SetLoops(-1, LoopType.Incremental);
            monsterHash.DOScale(0, hashScaleDownTime).SetEase(scaleDownEase);
            monsterHash.DOAnchorPos(setPosition, timeSetPosition).SetEase(hashMoveEase);
            ticketCountText.text = ticketCount;
            freeToggle.isOn = isFree;
            ticketToggle.isOn = isTicketUse;
            orbSpawnCountdown = 0;
            monsterSpawnCountdown = 0;
            shineCountdown = 0;
            disableCountdown = 0;
            isMonsterSet = false;
            if (isFree)
            {
                costText.text = "Free";
                oasIcon.SetActive(true);
                ticketIcon.SetActive(false);
            }
            if (isTicketUse)
            {
                costText.text = "x1";
                oasIcon.SetActive(false);
                ticketIcon.SetActive(true);
            }
            else
            {
                oasIcon.SetActive(true);
                ticketIcon.SetActive(false);
                costText.text = hashUse.HashCost;
            }
        }

        public void TurnOffRegenAnimation()
        {
            gameObject.SetActive(false);
        }

        bool isNotifyTriggered = false;
        private void SetActiveFalse()
        {
            if (!isNotifyTriggered && isMonsterSet)
            {
                isNotifyTriggered = true;
                UINotifyManager.ProcessNotifyAsync(1f);
            }
            if (disableCountdown < 2 && isMonsterSet)
            {
                disableCountdown += Time.deltaTime;
                return;
            }
            if (isMonsterSet && disableCountdown >= 2)
            {
                gameObject.SetActive(false);
                isNotifyTriggered = false;
            }
        }

        private void SetSpawnOrb()
        {
            if (isMonsterSet) return;
            if (effectControl.activeSelf) return;
            if (orbSpawnCountdown < timeTillActiveOrb)
            {
                orbSpawnCountdown += Time.deltaTime;
                return;
            }
            effectControl.SetActive(true);
        }

        private void SetSpawnMonster()
        {
            if (isMonsterSet) return;
            if (monsterSpawnCountdown < timeTillSpawn)
            {
                monsterSpawnCountdown += Time.deltaTime;
                return;
            }

            onClose?.Invoke();
            dimImage.color = new(0, 0, 0, 0);
            isMonsterSet = true;
            Instantiate(simpleShineObject, shineContainer);
            m_displayer.SetMonsterToDisplay(monster, 2);
            var monsterObject = m_displayer.GetMonsterModelByIndex(2);
            var gridUnit = monsterObject.AddComponent<GridUnit>();
            gridUnit.PlayAnimation(monster.MonsterScriptableData.UnitScriptableData.m_BlowAwayAnimation);
            shineObject.transform.DOKill();
            shineObject.SetActive(false);
            effectControl.SetActive(false);
            resultMenuComponents.OpenMenu();
            monsterHash.DOKill();
            var animator = monsterObject.GetComponent<Animator>();
            PlayAnimationSync(animator, monster.MonsterScriptableData.UnitScriptableData.m_IdleAnimation, monster.MonsterScriptableData.UnitScriptableData.m_BlowAwayAnimation.length);
            blueParticle.SetActive(false);
        }

        private async void PlayAnimationSync(Animator controlAnimator, AnimationClip animationToPlay, float waitTime)
        {
            await UniTask.WaitForSeconds(waitTime);
            controlAnimator.Play(animationToPlay.name);
        }

        private void SetSpawnShine()
        {
            if (isMonsterSet) return;
            if (monster is null)
            {
                timeTillShine += Time.deltaTime;
                timeTillSpawn = timeTillShine + 2.2f;
            }

            if (shineCountdown < timeTillShine)
            {
                shineCountdown += Time.deltaTime;
            }
        }
    }
}
