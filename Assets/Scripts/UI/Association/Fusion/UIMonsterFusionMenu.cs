using System;
using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.DataStruct;
using Assets.Scripts.DataStruct.Data.HashData;
using Assets.Scripts.Managers;
using Assets.Scripts.Models;
using Assets.Scripts.Scriptables.Item;
using Assets.Scripts.SerializeDataStruct.ItemData;
using Assets.Scripts.UI.Utilities;
using Cysharp.Threading.Tasks;
using I2.Loc;
using UI.Chat;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.Association.Fusion
{
    public class UIMonsterFusionMenu : UIMenuStruct
    {
        [SerializeField] private UIFusionMonsterHolder uiFusionBaseMonsterItem;
        [SerializeField] private UIFusionMonsterHolder uiFusionSubMonsterItem;
        [SerializeField] private UIFusionItemMenu uiFusionItemMenu;
        [SerializeField] private Button addItemBtn;
        [SerializeField] private Button removeItemBtn;
        [SerializeField] private Button switchBtn;
        [SerializeField] private Button fusionBtn;
        [SerializeField] private GameObject itemFusionInfo;
        [SerializeField] private Image itemFusionImg;
        [SerializeField] private Image itemFusionBg;
        [SerializeField] private UIResultPredictionMenu resultPrediction;
        [SerializeField] private float fusionCost;
        [SerializeField] private UIMonsterBagInAssociation monsterBagMenu;
        [SerializeField] private UIMonsterCreationMenu monsterCreationMenu;
        [SerializeField] private ServerInfoSO serverInfo;
        [SerializeField] private UIMonsterFusionAnimation fusionAnimationMenu;
        [SerializeField] private UIMonster3dDisplayRender m_monsterRender;
        [SerializeField] private Button testFusion;

        public Action<SerializeItem> OnSelectedItem { get; private set; }
        public Action OnDeSelectedItem { get; private set; }
        public SerializeItem Item { get; private set; }
        public SerializeMonster BaseMonster { get; private set; }
        public SerializeMonster SubMonster { get; private set; }

        private void Awake()
        {
            uiFusionBaseMonsterItem.SetMenu(this, monsterBagMenu, m_monsterRender);
            uiFusionSubMonsterItem.SetMenu(this, monsterBagMenu, m_monsterRender);
            m_monsterRender.RemoveFromDisplay(0);
            m_monsterRender.RemoveFromDisplay(1);
            m_monsterRender.RemoveFromDisplay(2);
            OnSelectedItem += SelectedItem;
            OnDeSelectedItem += DeSelectedItem;

            addItemBtn.onClick.AddListener(() =>
            {
                uiFusionItemMenu.OpenMenu();
            });
            switchBtn.onClick.AddListener(SwitchMonster);
            testFusion.gameObject.SetActive(BackendLoadData.Instance.ServerInfo.BuildType == ServerInfoSO.BuildTypeEnum.TEST);
            testFusion.onClick.AddListener(TestFusion);
            testFusion.gameObject.SetActive(BackendLoadData.Instance.ServerInfo.BuildType != ServerInfoSO.BuildTypeEnum.PRODUCTION);
            OnMenuOpen.AddListener(() =>
            {
                DeSelectedItem();
                if (GameProgressManager.Instance.GameProgress.FirstFusion == default && string.IsNullOrEmpty(AssistantManager.Instance.CurrentTutorial.TutorialKey))
                    AssistantManager.Instance.PlayTutorial(25);
                uiFusionSubMonsterItem.OnDeselectingMonster?.Invoke();
                uiFusionBaseMonsterItem.OnDeselectingMonster?.Invoke();
                if (AssistantManager.Instance != null)
                    AssistantManager.Instance.OnRequestSelected += PerformFusion;
                OpenResultPrediction();
                Managers.GameDataManager.OnMonsterDataUpdated += ReloadSelection;
                m_monsterRender.RemoveFromDisplay(0);
                m_monsterRender.RemoveFromDisplay(1);
                m_monsterRender.RemoveFromDisplay(2);
            });

            OnMenuClose.AddListener(() =>
            {
                resultPrediction.gameObject.SetActive(false);
                uiFusionBaseMonsterItem.OnDeselectingMonster?.Invoke();
                uiFusionSubMonsterItem.OnDeselectingMonster?.Invoke();
                uiFusionItemMenu.CloseMenu();
                monsterBagMenu.ClearMonster();
                Managers.GameDataManager.OnMonsterDataUpdated -= ReloadSelection;
                if (AssistantManager.Instance != null)
                    AssistantManager.Instance.OnRequestSelected -= PerformFusion;
            });

            DeSelectedItem();
            if (AssistantManager.Instance != null)
                AssistantManager.Instance.OnRequestSelected += PerformFusion;
            OpenResultPrediction();
            fusionBtn.onClick.AddListener(FusionConfirm);
            if (GameProgressManager.Instance.GameProgress.FirstFusion == default && string.IsNullOrEmpty(AssistantManager.Instance.CurrentTutorial.TutorialKey))
                AssistantManager.Instance.PlayTutorial(25);

        }

        public void ResetData()
        {
            Item = null;
            itemFusionInfo.SetActive(false);
            fusionBtn.interactable = false;
            resultPrediction.gameObject.SetActive(false);
            uiFusionBaseMonsterItem.OnDeselectingMonster?.Invoke();
            uiFusionSubMonsterItem.OnDeselectingMonster?.Invoke();
            uiFusionItemMenu.CloseMenu();
        }

        private void TestFusion()
        {
            SerializeMonster monsterResult = null;
            if (Item != null)
            {
                monsterResult = Controllers.FusionController.PerformFusionMonster(BaseMonster, SubMonster, Item.Item as ScriptableFushionItem);
            }
            else
            {
                monsterResult = Controllers.FusionController.PerformFusionMonster(BaseMonster, SubMonster, null);
            }
            monsterResult = SerializeMonster.CreateDefaultMonster(monsterResult.MonsterScriptableData);
            fusionAnimationMenu.SetMonsterToAnimation(monsterResult, uiFusionBaseMonsterItem.FusionMaterialSetup, uiFusionSubMonsterItem.FusionMaterialSetup, () =>
            {
                monsterCreationMenu.SetMonster(monsterResult);
            }, BaseMonster, SubMonster, Item);
        }

        private void FusionConfirm()
        {
            //float.TryParse(GameDataManager.Instance.LoadedPlayerData.PlayerOas, out var oasFloat);
            //if (.1f > oasFloat)
            //{
            //    UINotifyManager.AddNotifyResponse("Mint perform", () =>
            //    {
            //        UIPopupNotify.Instance.SetNotify("Notice", I2.Loc.LocalizationManager.GetTranslation("General/Transaction Fee Warning"));
            //    }, UINotifyManager.NotifyType.System, 0);
            //}
            //else
            {
                UINotifyManager.AddNotifyResponse("Fusion Warning", () =>
                {
                    var msg = LocalizationManager.GetTranslation("Assistance Msg General/Fusion Warning");
                    AssistantManager.Instance.SetDialog(msg, "normal", true, true, true);
                }, UINotifyManager.NotifyType.System, 1);
            }
            UINotifyManager.ProcessNotify();
        }

        private async void PerformFusion(bool value)
        {
            if (!value) return;
            ChatMenuManager.Instance.CloseChatMenu();
            var loading = BackendLoadData.Instance.LoadingCanvas();
            try
            {
                SerializeMonster monsterResult = new();
                if (Item != null)
                {
                    monsterResult = Controllers.FusionController.PerformFusionMonster(BaseMonster, SubMonster, Item.Item as ScriptableFushionItem);
                }
                else
                {
                    monsterResult = Controllers.FusionController.PerformFusionMonster(BaseMonster, SubMonster, null);
                }
                var result = await Controllers.FusionController.GenerateFusionMonsterNFT(GameDataManager.Instance.LoadedPlayerData.PlayerWallet, serverInfo.BackendUrl, monsterResult, Item);

                if (!string.IsNullOrEmpty(result.Item1) && Item != null)
                {
                    var cacheItem = GameDataManager.Instance.LoadedPlayerData.PlayerItemList.FirstOrDefault(x => x.Item.ItemId == Item.Item.ItemId);
                    cacheItem.SetQuantity(cacheItem.Quantity - 1);
                }

                for (var i = 0; i < 20; i++)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(2));
                    if (await Controllers.RegenerationController.CheckRandomMonsterBlockchain(result.Item1))
                    {
                        SerializeFusionMonster hashData = new(monsterResult.BornOf1TokenId, monsterResult.BornOf2TokenId, monsterResult.MonsterScriptableData.MonsterMainSeed, monsterResult.MonsterScriptableData.MonsterSubSeed);
                        int[] tokenIds = new int[2];
                        tokenIds[0] = BaseMonster.MonsterNFTId;
                        tokenIds[1] = SubMonster.MonsterNFTId;
                        GameDataManager.AddRegenHistory?.Invoke(result.Item1, new()
                        {
                            typeMint = 10,
                            addressContract = SmartContractInteract.Instance.ContractAddress.ADDRESS_CONTRACT_REGEN_FUSION,
                            chainId = hashData.ChainId,
                            account = GameDataManager.Instance.LoadedPlayerData.PlayerWallet,
                            tokenIds = tokenIds
                        }, string.Empty);
                        break;
                    }
                }

                GameDataModel.LoadMonsterData();
                while (GameDataModel.IsLoadingMonster)
                    await UniTask.WaitForSeconds(5);
                var foundMonster = false;
                Destroy(loading);
                SerializeMonster monsterData = new();
                for (int monsterIndex = 0; monsterIndex < GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList.Count; monsterIndex++)
                {
                    SerializeMonster monster = GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList[monsterIndex];
                    if (monster.MonsterNFTId != result.Item2 || result.Item2 < 0)
                    {
                        continue;
                    }
                    Debug.Log("monster id: " + monster.MonsterId + " lifespan: " + monsterResult.MonsterAlteredTrainingP.Lifespan);
                    fusionAnimationMenu.SetMonsterToAnimation(monster, uiFusionBaseMonsterItem.FusionMaterialSetup, uiFusionSubMonsterItem.FusionMaterialSetup, () =>
                    {
                        monsterCreationMenu.SetMonster(monster);
                    }, BaseMonster, SubMonster, Item);
                    foundMonster = true;
                    monsterData = monster;
                    GameDataManager.Instance.LoadedPlayerData.Burn_Monster(new()
                        {
                            new()
                            {
                                monsterId = BaseMonster.MonsterId,
                                turnMemory = BaseMonster.ComplexLifespan == 0
                            },
                            new()
                            {
                                monsterId = SubMonster.MonsterId,
                                turnMemory = SubMonster.ComplexLifespan == 0
                            }
                        });
                    GameDataManager.OnMonsterDataChanged?.Invoke();
                    break;
                }
                await UniTask.WaitForSeconds(5);
                while (GameDataModel.IsLoadingMonster)
                    await UniTask.WaitForSeconds(5);

                if (!foundMonster)
                {
                    var msg = LocalizationManager.CurrentLanguageCode == "ja" ? "処理がタイムアウトしました。\n後ほど改めてMonsterバッグを確認してください。\nMonsterを受け取れない場合は、トランザクションIDをお知らせください。" : "Monster regeneration timeout!\nPlease check your monster bag later for the require monster!\nIf there are no monster receive please send the transaction id for development team for checkup!";
                    UINotifyManager.AddNotifyResponse("Mint monster", () => UIPopupNotify.Instance.SetNotify("Notice!", msg), UINotifyManager.NotifyType.System);
                    UINotifyManager.ProcessNotify();
                    ChatMenuManager.Instance.OpenChatMenu();
                    BackendLoadData.Instance.LoadOasBalance();
                    ResetData();
                    return;
                }
                monsterCreationMenu.IndexMenu = 1;

                BackendLoadData.Instance.AddBit(-1000, 0, "monster_fusion", "association_monster_fusion");
                BackendLoadData.Instance.LoadOasBalance();
                GameProgressManager.Instance.GameProgress.AddFusionMonsterSuccess();
                GameProgressManager.Instance.GameProgress.AddMintMonsterSuccess();
                UINotifyManager.AddNotifyResponse("Fusion success", () =>
                {
                    var msg = LocalizationManager.GetTranslation("Assistance Msg General/Fusion Result");
                    AssistantManager.Instance.SetDialog(msg, "smile", false);
                    Controllers.AssistantController.SetInteractionBlock();
                }, UINotifyManager.NotifyType.System);
                GameProgressManager.Instance.GameProgress.AddMonsterObtained(monsterData.MonsterScriptableData.MonsterPublicNumber);
            }
            catch (Exception e)
            {
                UINotifyManager.AddNotifyResponse("Regeneration Fail",
                            () =>
                            {
                                var message = LocalizationManager.CurrentLanguageCode == "ja"
                                    ? "Monsterの再生に失敗しました。"
                                    : "Fail To Regenerate Monster.";
                                UIPopupNotify.Instance.SetNotify("Regeneration Fail",
                                    $"{message}\n{e.Message}",
                                    UINotifyManager.ProcessNotify);
                            },
                            UINotifyManager.NotifyType.System);
                UINotifyManager.ProcessNotify();
                ChatMenuManager.Instance.OpenChatMenu();
                ResetData();
                Destroy(loading);
            }
        }

        private void OnDestroy()
        {
            OnSelectedItem -= SelectedItem;
            OnDeSelectedItem -= DeSelectedItem;
        }

        private bool AreMonstersSet => uiFusionBaseMonsterItem.SelectedMonster != null && uiFusionSubMonsterItem.SelectedMonster != null && uiFusionBaseMonsterItem.SelectedMonster.MonsterScriptableData != null && uiFusionSubMonsterItem.SelectedMonster.MonsterScriptableData != null;

        public void OpenResultPrediction()
        {
            resultPrediction.gameObject.SetActive(AreMonstersSet);
            switchBtn.gameObject.SetActive(AreMonstersSet);
            ResultPredictionDetail();
        }

        private void SwitchMonster()
        {
            if (!AreMonstersSet) return;
            m_monsterRender.RemoveFromDisplay(0);
            m_monsterRender.RemoveFromDisplay(1);
            var baseMonster = uiFusionBaseMonsterItem.SelectedMonster;
            var subMonster = uiFusionSubMonsterItem.SelectedMonster;
            uiFusionBaseMonsterItem?.OnSelectingMonster(subMonster);
            uiFusionSubMonsterItem?.OnSelectingMonster(baseMonster);
            OpenResultPrediction();
        }

        private void ResultPredictionDetail()
        {
            var resultValidate = false;
            if (BaseMonster != null && SubMonster != null)
            {
                if (Item != null && Item.Item as ScriptableFushionItem != null)
                    resultValidate = resultPrediction.SetData(BaseMonster, SubMonster, ((ScriptableFushionItem)Item.Item).FixedSeed);
                else
                    resultValidate = resultPrediction.SetData(BaseMonster, SubMonster);
            }
            FusionValidate(resultValidate);
        }

        public void SetBaseMonster(SerializeMonster monster) => BaseMonster = monster;

        public void SetSubMonster(SerializeMonster monster) => SubMonster = monster;

        private void SelectedItem(SerializeItem item)
        {
            Item = item;
            itemFusionInfo.SetActive(true);
            itemFusionImg.sprite = item.Item.ItemGraphic;
            itemFusionBg.sprite = GameComponentsSettingManager.Instance.GetItemQualityBackground(item.Item.ItemRarity);
            var resultValidate = false;
            if (Item.Item as ScriptableFushionItem != null)
            {
                resultValidate = resultPrediction.SetData(BaseMonster, SubMonster, ((ScriptableFushionItem)Item.Item).FixedSeed);
            }
            FusionValidate(resultValidate);
            removeItemBtn.gameObject.SetActive(true);
            removeItemBtn.onClick.AddListener(DeSelectedItem);
        }

        public void DeSelectedItem()
        {
            Item = null;
            itemFusionInfo.SetActive(false);
            var resultValidate = resultPrediction.SetData(BaseMonster, SubMonster);
            FusionValidate(resultValidate);
            removeItemBtn.gameObject.SetActive(false);
            removeItemBtn.onClick.RemoveAllListeners();
        }

        private void FusionValidate(bool isSeedExist)
        {
            fusionBtn.interactable = AreMonstersSet && isSeedExist && GameDataManager.Instance.LoadedPlayerData.PlayerBit >= fusionCost;
        }

        private void ReloadSelection()
        {
            uiFusionBaseMonsterItem.OnSelectingMonster?.Invoke(BaseMonster);
            uiFusionSubMonsterItem.OnSelectingMonster?.Invoke(SubMonster);
        }
    }
}