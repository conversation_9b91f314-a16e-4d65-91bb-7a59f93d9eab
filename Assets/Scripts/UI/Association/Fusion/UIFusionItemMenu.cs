using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.Managers;
using Assets.Scripts.Scriptables.Item;
using Assets.Scripts.SerializeDataStruct.ItemData;
using Assets.Scripts.UI.Utilities;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.Association.Fusion
{
    public class UIFusionItemMenu : UIMenuStruct
    {
        [SerializeField] private GameObject initPrefab;
        [SerializeField] private Transform container;
        [SerializeField] private Image currentItemImage;
        [SerializeField] private Image currentItemRank;
        [SerializeField] private Image currentItemBackground;
        [SerializeField] private TextMeshProUGUI itemNameText;
        [SerializeField] private TextMeshProUGUI itemDescriptionText;
        [SerializeField] private Button useItemBtn;
        [SerializeField] private Button removeItemBtn;
        [SerializeField] private Image useItemOutline;
        [SerializeField] private TextMeshProUGUI useItemText;
        [SerializeField] private UIBlankControl itemDetailUse;
        private SerializeItem _currentSelectItem;
        private List<SerializeItem> _sortedItemList = new();
        [SerializeField] private UIBlankControl sortMenu;
        [SerializeField] private Toggle sortToggle;
        [Header("Sort Toggles")]
        [SerializeField] private List<Toggle> sortOptions = new();
        private int _currentSortOrder;
        private SerializeItem _itemUse;
        public override void Start()
        {
            base.Start();
            useItemBtn.onClick.AddListener(SetItem);
            removeItemBtn.onClick.AddListener(RemoveItem);
            for (var i = 0; i < sortOptions.Count; i++)
            {
                var sortOption = sortOptions[i];
                var sortIndex = i;
                sortOption.onValueChanged.AddListener(value =>
                {
                    if (!value) return;
                    _sortedItemList = GetFusionItemSort(_sortedItemList, sortIndex);
                    ReloadItems();
                });
            }

            sortToggle.onValueChanged.AddListener(value =>
            {
                if (value)
                    sortMenu.OpenMenu();
            });
        }

        public override void OpenMenu()
        {
            base.OpenMenu();
            RefreshList();
            ReloadItems();
        }
        private void RefreshList()
        {
            _sortedItemList = new List<SerializeItem>();
            _sortedItemList.Clear();
            _sortedItemList = GetFusionItem(GameDataManager.Instance.LoadedPlayerData.PlayerItemList);
            _sortedItemList = GetFusionItemSort(_sortedItemList, _currentSortOrder);
            if (_sortedItemList.Count > 0)
            {
                SetViewData(_sortedItemList[0]);
                itemDetailUse.OpenMenu();
                EnableButton(_currentSelectItem);
            }
            else
            {
                itemDetailUse.CloseMenu();
                DisableButton();
            }
        }

        private void SetItem()
        {
            gameObject.GetComponentInParent<UIMonsterFusionMenu>().OnSelectedItem(_itemUse);
            CloseMenu();
        }

        private void RemoveItem()
        {
            gameObject.GetComponentInParent<UIMonsterFusionMenu>().OnDeSelectedItem();
            CloseMenu();
        }
        private void EnableButton(SerializeItem item)
        {
            useItemBtn.interactable = true;
            useItemText.color = Color.white;
            useItemOutline.canvasRenderer.SetAlpha(1);
            _itemUse = item;
        }

        private void DisableButton()
        {
            useItemBtn.interactable = false;
            useItemText.color = Color.gray;
            useItemOutline.canvasRenderer.SetAlpha(0.5f);
            _itemUse = null;
        }

        private void ReloadItems()
        {
            container.DestroyChildrenHelper();
            foreach (var t in _sortedItemList)
            {
                var itemObject = Instantiate(initPrefab, container);
                var itemData = t;
                if (!itemObject.TryGetComponent(out UIItemButton itemButton)) continue;
                string itemName = itemData.Item.ItemName;
                System.Text.StringBuilder itemDescription = new();
                itemDescription.AppendLine(itemData.Item.ItemDescription);
                if (itemData.Item.HasSupplyLimit)
                    itemDescription.AppendLine(itemData.Item.SupplyAmountString);

                itemButton.SetDisplay(itemSprite: itemData.Item.ItemGraphic,
                    itemRankOutlineSprite: GameComponentsSettingManager.Instance.GetItemQualityIcon(itemData.Item.ItemRarity),
                    itemRankBackgroundSprite: GameComponentsSettingManager.Instance.GetItemQualityBackground(itemData.Item.ItemRarity),
                    itemCount: itemData.Quantity,
                    itemName: itemName,
                    itemDescription: itemDescription.ToString());

                itemButton.ItemButton.onClick.AddListener(() => SetViewData(itemData));
            }
        }

        public void ReloadItemList()
        {
            RefreshList();
            ReloadItems();
        }

        public override void CloseMenu()
        {
            base.CloseMenu();
            _currentSelectItem = null;
        }
        private void SetViewData(SerializeItem itemData)
        {
            currentItemImage.sprite = itemData.Item.ItemGraphic;
            currentItemRank.sprite = GameComponentsSettingManager.Instance.GetItemQualityIcon(itemData.Item.ItemRarity);
            currentItemBackground.sprite = GameComponentsSettingManager.Instance.GetItemQualityBackground(itemData.Item.ItemRarity);
            string itemName;
            string itemDescription;
            itemName = itemData.Item.ItemName;
            itemDescription = itemData.Item.ItemDescription;
            itemNameText.text = itemName;
            itemDescriptionText.text = itemDescription;
            _currentSelectItem = itemData;
            useItemBtn.gameObject.SetActive(true);
            removeItemBtn.gameObject.SetActive(false);
            if (gameObject.GetComponentInParent<UIMonsterFusionMenu>().Item == _currentSelectItem)
            {
                removeItemBtn.gameObject.SetActive(true);
                useItemBtn.gameObject.SetActive(false);
            }
            if (_currentSelectItem.Quantity > 0)
            {
                EnableButton(_currentSelectItem);
            }
            else
            {
                DisableButton();
            }

        }

        private static List<SerializeItem> GetFusionItem(IReadOnlyCollection<SerializeItem> inputList)
        {
            List<SerializeItem> outputList = new();
            if (inputList.Count == 0)
            {
                return outputList;
            }

            outputList.AddRange(from itemData in inputList let trainingItemScriptableData = itemData.Item as ScriptableFushionItem where trainingItemScriptableData != null && itemData.Quantity > 0 select itemData);

            return outputList;
        }

        private List<SerializeItem> GetFusionItemSort(List<SerializeItem> inputList, int sortIndex)
        {
            List<SerializeItem> outputList = new();
            if (inputList == null || inputList.Count == 0)
            {
                return outputList;
            }
            _currentSortOrder = sortIndex;
            outputList = sortIndex switch
            {
                0 => inputList.OrderBy(i => i.Item.ItemName).ToList(),
                1 => inputList.OrderByDescending(i => i.Item.ItemName).ToList(),
                2 => inputList.OrderBy(i => i.Quantity).ToList(),
                3 => inputList.OrderByDescending(i => i.Quantity).ToList(),
                4 => inputList.OrderBy(i => i.Item.ItemRarity).ToList(),
                5 => inputList.OrderByDescending(i => i.Item.ItemRarity).ToList(),
                _ => inputList
            };
            return outputList;
        }
    }
}
