using System;
using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.DataStruct;
using Assets.Scripts.UI.Utilities;
using TMPro;
using UI.Chat;
using UnityEngine;
using UnityEngine.UI;

namespace UI.UIFriend
{
    public class UiFriendMenu : UIMenuStruct
    {
        [Header("TOGGLE")] [SerializeField] private ToggleGroup toggleGroup;
        [SerializeField] private Toggle listFriendToggle;
        [SerializeField] private Toggle friendRequestsToggle;
        [SerializeField] private Toggle friendApprovalToggle;
        [SerializeField] private Toggle muteListToggle;
        [Header("INFO")]
        [SerializeField] private GameObject friendInfoPrefab;
        [SerializeField] private Button searchBtn;
        [SerializeField] private TMP_InputField searchInput;
        [SerializeField] private TextMeshProUGUI countFriend;
        [SerializeField] private Transform containerFriendTransform;
        [SerializeField] private Transform containerRequestsTransform;
        [SerializeField] private Transform containerApprovalTransform;
        [SerializeField] private Transform containerMuteTransform;
        [SerializeField] private Transform containerSearchTransform;
        [SerializeField] private GameObject titleListFriend;
        [SerializeField] private GameObject titleFriendRequests;
        [SerializeField] private GameObject titleFriendApproval;
        [SerializeField] private GameObject titleMuteList;
        [SerializeField] private GameObject titleSearchList;
        [SerializeField] private GameObject noUserFound;
        [SerializeField] private GameObject friendApprovalNotice;
        
        private Dictionary<Toggle, List<SerializeUserData>> _toggleToFriendList;
        private Dictionary<Toggle, GameObject> _toggleToTitleMapping;
        private Dictionary<Toggle, Transform> _toggleToContainerMapping;

        private List<SerializeUserData> _searchResults; 

    
        public override void OpenMenu()
        {
            Initialize();
            SetupToggleToFriendListMapping();
            SetupToggleToTitleMapping();
            SetupToggleToContainerMapping();
            OnApprovalListChanged();
            noUserFound.gameObject.SetActive(false);
            CheckAndActiveToggle();
            base.OpenMenu();
        }

        private void CheckAndActiveToggle()
        {
            if (listFriendToggle.isOn)
            {
                OnToggleSelected(listFriendToggle);
            }
            else if (friendRequestsToggle.isOn)
            {
                OnToggleSelected(friendRequestsToggle);
            }
            else if (friendApprovalToggle.isOn)
            {
                OnToggleSelected(friendApprovalToggle);
            }
            else
            {
                OnToggleSelected(muteListToggle);
            }
        }

        private void OnApprovalListChanged()
        {
            friendApprovalNotice.SetActive(FriendDataManager.Instance.GetFriendApprovalList().Count > 0);
        }
        private void Initialize()
        {
            listFriendToggle.onValueChanged.RemoveAllListeners();
            friendRequestsToggle.onValueChanged.RemoveAllListeners();
            friendApprovalToggle.onValueChanged.RemoveAllListeners();
            muteListToggle.onValueChanged.RemoveAllListeners();
            searchBtn.onClick.RemoveAllListeners();
            listFriendToggle.onValueChanged.AddListener(isOn =>
            {
                if (isOn) OnToggleSelected(listFriendToggle);
            });
            friendRequestsToggle.onValueChanged.AddListener(isOn =>
            {
                if (isOn) OnToggleSelected(friendRequestsToggle);
            });
            friendApprovalToggle.onValueChanged.AddListener(isOn =>
            {
                if (isOn) OnToggleSelected(friendApprovalToggle);
            });
            muteListToggle.onValueChanged.AddListener(isOn =>
            {
                if (isOn) OnToggleSelected(muteListToggle);
            });
            searchBtn.onClick.AddListener(OnSearchButtonClicked);
        }

        private void SetupToggleToFriendListMapping()
        {
            _toggleToFriendList = new Dictionary<Toggle, List<SerializeUserData>>
            {
                { listFriendToggle, FriendDataManager.Instance.GetFriendList() },
                { friendRequestsToggle, FriendDataManager.Instance.GetFriendPendingList() },
                { friendApprovalToggle, FriendDataManager.Instance.GetFriendApprovalList() },
                { muteListToggle, FriendDataManager.Instance.GetFriendMuteList() }
            };
        }

        private void SetupToggleToTitleMapping()
        {
            _toggleToTitleMapping = new Dictionary<Toggle, GameObject>
            {
                { listFriendToggle, titleListFriend },
                { friendRequestsToggle, titleFriendRequests },
                { friendApprovalToggle, titleFriendApproval },
                { muteListToggle, titleMuteList }
            };
        }

        private void SetupToggleToContainerMapping()
        {
            _toggleToContainerMapping = new Dictionary<Toggle, Transform>
            {
                { listFriendToggle, containerFriendTransform },
                { friendRequestsToggle, containerRequestsTransform },
                { friendApprovalToggle, containerApprovalTransform },
                { muteListToggle, containerMuteTransform }
            };
        }
        

        private async void FetchFriendList(Toggle selectedToggle)
        {
            try
            {
                if (Equals(selectedToggle, listFriendToggle))
                {
                    await FriendDataManager.Instance.GetListFriend();
                }
                else if (Equals(selectedToggle, friendRequestsToggle))
                {
                    await FriendDataManager.Instance.GetListFriendRequests();
                }
                else if (Equals(selectedToggle, friendApprovalToggle))
                {
                    await FriendDataManager.Instance.GetListFriendApproval();
                }
                else if (Equals(selectedToggle, muteListToggle))
                {
                    await FriendDataManager.Instance.GetMuteList();
                }
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private  void OnSearchButtonClicked()
        {
            var searchText = searchInput.text.Trim().ToLower();
            if (string.IsNullOrEmpty(searchText)) return;

            containerSearchTransform.DestroyChildrenHelper();
            
            Toggle selectedToggle = null;
            if (listFriendToggle.isOn) selectedToggle = listFriendToggle;
            else if (friendRequestsToggle.isOn) selectedToggle = friendRequestsToggle;
            else if (friendApprovalToggle.isOn) selectedToggle = friendApprovalToggle;
            else if (muteListToggle.isOn) selectedToggle = muteListToggle;

            if (selectedToggle)
            {
                var friendList = _toggleToFriendList[selectedToggle];
                _searchResults = (from serializeUserData in friendList
                                  where serializeUserData.user_name.ToLower().Contains(searchText) ||
                                        serializeUserData.address_wallet.ToLower().Contains(searchText)
                                  select serializeUserData).Distinct().ToList();
            }
            else
            {
                _searchResults = (from serializeUserData in _searchResults
                    where serializeUserData.user_name.ToLower().Contains(searchText) ||
                          serializeUserData.address_wallet.ToLower().Contains(searchText)
                    select serializeUserData).Distinct().ToList();
            }

            if (_searchResults.Count == 0)
            {
                noUserFound.gameObject.SetActive(true);
                containerSearchTransform.gameObject.SetActive(false);
                titleSearchList.SetActive(true);
                foreach (var toggle in _toggleToContainerMapping.Keys)
                {
                    toggle.isOn = false;
                    _toggleToContainerMapping[toggle].gameObject.SetActive(false);
                    _toggleToTitleMapping[toggle].SetActive(false);
                }

                return;
            }

            noUserFound.gameObject.SetActive(false);

            foreach (var serializeUserData in _searchResults)
            {
                
                var obj = Instantiate(friendInfoPrefab, containerSearchTransform);
                if (!obj.TryGetComponent(out UiFriendItem friendItem)) continue;

                var dataType = FriendDataType.FriendList;
                if (selectedToggle)
                {
                    if (Equals(selectedToggle, friendRequestsToggle))
                    {
                        dataType = FriendDataType.FriendRequests;
                    }
                    else if (Equals(selectedToggle, friendApprovalToggle))
                    {
                        dataType = FriendDataType.FriendApproval;
                    }
                    else if (Equals(selectedToggle, muteListToggle))
                    {
                        dataType = FriendDataType.MuteList;
                    }
                }

                friendItem.gameObject.SetActive(true);
                friendItem.SetData(ChatMenuManager.Instance.PopupPanel, serializeUserData, Equals(selectedToggle, friendApprovalToggle), dataType, true);
            }

            foreach (var toggle in _toggleToContainerMapping.Keys)
            {
                toggle.isOn = false;
                _toggleToContainerMapping[toggle].gameObject.SetActive(false);
                _toggleToTitleMapping[toggle].SetActive(false);
            }

            containerSearchTransform.gameObject.SetActive(true);
            titleSearchList.SetActive(true);
        }

        private void OnToggleSelected(Toggle selectedToggle)
        {
            // Hide search container and title
            containerSearchTransform.gameObject.SetActive(false);
            titleSearchList.SetActive(false);
            noUserFound.gameObject.SetActive(false); // Hide noUserFound when switching toggles

            // Fetch and update the selected toggle's data
            FetchFriendList(selectedToggle);
            UpdateFriendList(selectedToggle);

            // Update friend count
            UpdateFriendCount(selectedToggle);
        }

        private void UpdateFriendList(Toggle selectedToggle)
        {
            if (!_toggleToFriendList.TryGetValue(selectedToggle, out var friendList)) return;

            foreach (var toggle in _toggleToContainerMapping.Keys)
            {
                _toggleToContainerMapping[toggle].gameObject.SetActive(Equals(toggle, selectedToggle));
            }

            foreach (var toggle in _toggleToTitleMapping.Keys)
            {
                _toggleToTitleMapping[toggle].SetActive(Equals(toggle, selectedToggle));
            }
            
            var containerTransform = _toggleToContainerMapping[selectedToggle];
            
            containerTransform.DestroyChildrenHelper();
            
            UpdateFriendCount(selectedToggle);
            
            if (friendList.Count == 0)
            {
                return;
            }
            
            foreach (var serializeUserData in friendList)
            {
                var dataType = FriendDataType.FriendList;
                if (Equals(selectedToggle, friendRequestsToggle))
                {
                    dataType = FriendDataType.FriendRequests;
                }
                else if (Equals(selectedToggle, friendApprovalToggle))
                {
                    dataType = FriendDataType.FriendApproval;
                }
                else if (Equals(selectedToggle, muteListToggle))
                {
                    dataType = FriendDataType.MuteList;
                }

                var obj = Instantiate(friendInfoPrefab, containerTransform);
                obj.TryGetComponent(out UiFriendItem friendItem);
                if (!friendItem) continue;
                friendItem.gameObject.SetActive(true);
                friendItem.SetData(ChatMenuManager.Instance.PopupPanel, serializeUserData, Equals(selectedToggle, friendApprovalToggle), dataType, false);

            }

            noUserFound.gameObject.SetActive(false); 
        }

        private void UpdateFriendCount(Toggle selectedToggle)
        {
            if (Equals(selectedToggle, listFriendToggle) || Equals(selectedToggle, friendRequestsToggle) || Equals(selectedToggle, friendApprovalToggle))
            {
                var count = 0;
                var limit = 0;
                countFriend.gameObject.SetActive(true);

                if (selectedToggle == listFriendToggle)
                {
                    var uniqueIds = new HashSet<string>();

                    count += FriendDataManager.Instance.GetFriendList().Count(friend => uniqueIds.Add(friend._id));
                    
                    limit = 100;
                }
                else if (selectedToggle == friendRequestsToggle)
                {
                    var uniqueIds = new HashSet<string>();

                    count = FriendDataManager.Instance.GetFriendPendingList().Count(friend => uniqueIds.Add(friend._id));
                    limit = 10;
                }
                else if (selectedToggle == friendApprovalToggle)
                {
                    var uniqueIds = new HashSet<string>();
                    count = FriendDataManager.Instance.GetFriendApprovalList().Count(friend => uniqueIds.Add(friend._id));
                    limit = 20;
                }

                countFriend.text = $"{count} / {limit}";
            }
            else
            {
                countFriend.gameObject.SetActive(false);
            }
        }

        public enum FriendDataType
        {
            FriendList,
            FriendRequests,
            FriendApproval,
            MuteList
        }
    }
}