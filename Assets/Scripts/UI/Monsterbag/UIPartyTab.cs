using Assets.Scripts.Managers;
using Assets.Scripts.UI.Utilities;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.Monsterbag
{
    public class UIPartyTab : MonoBehaviour
    {
        [SerializeField] public TMP_InputField inputfieldButton;
        [SerializeField] private Button selectButton;
        private int partyId;
        private string partyName;

        private void Awake()
        {
            inputfieldButton.onEndEdit.AddListener(OnEndEdit);
        }

        public int PartyId => partyId;

        internal void SetParty(string partyName, int partyId, System.Action onSelectParty)
        {
            this.partyId = partyId;
            this.inputfieldButton.text = partyName;
            this.partyName = partyName;
            selectButton.onClick.AddListener(() =>
            {
                onSelectParty?.Invoke();
                RefreshPartyName();
            });
        }

        internal void RefreshPartyName()
        {
            if (GameDataManager.Instance.LoadedPlayerData.PlayerPartyGroupDict.TryGetValue(partyId, out var setup))
            {
                this.inputfieldButton.text = setup.PartyName;
            }
        }

        internal void SetSelected()
        {
            selectButton.interactable = false;
        }

        internal void SetDeselected()
        {
            selectButton.interactable = true;
        }

        #region Input field
        private void OnEndEdit(string input)
        {
            if(string.IsNullOrEmpty(input.Trim()))
            {
                UINotifyManager.AddNotifyResponse("Party name empty", () =>
                {
                    var msg = I2.Loc.LocalizationManager.CurrentLanguageCode == "ja" ? "パーティー名を入力してください。" : "Party name can not be empty.";
                    UIPopupNotify.Instance.SetNotify("Warning!", msg);
                }, UINotifyManager.NotifyType.System);
                UINotifyManager.ProcessNotify();
                OnChangeDeny();
                return;
            }

            if (!string.Equals(input.Trim(), partyName))
            {
                OnChangeAccept();
                return;
            }
        }

        private void OnChangeAccept()
        {
            partyName = inputfieldButton.text;
            var party = GameDataManager.Instance.LoadedPlayerData.PlayerPartyGroupDict[partyId];
            party.ChangePartyName(partyName);
            GameDataManager.OnMonsterPartyDataChanged?.Invoke();
        }

        private void OnChangeDeny()
        {
            inputfieldButton.SetTextWithoutNotify(partyName);
        }
        #endregion
    }
}