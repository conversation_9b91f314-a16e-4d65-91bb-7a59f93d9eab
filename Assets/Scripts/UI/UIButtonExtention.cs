using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI
{
    [RequireComponent(typeof(Button))]
    public class UIButtonExtention : MonoBehaviour
    {
        private Button button;
        [SerializeField] private List<TextMeshProUGUI> buttonTextList = new();
        [SerializeField] private List<Image> subImageList = new();
        [SerializeField] private List<Image> textImage = new();
        [SerializeField] private Color enableColorText;
        [SerializeField] private Color disabledColorText;
        [SerializeField] private Color enableColorOutline;
        [SerializeField] private Color disabledColorOutline;
        
        public Color EnableColorText
        {
            get => enableColorText;
            set => enableColorText = value;
        }

        bool isInteractable = true;
        public Button Button
        {
            get
            {
                if (button == null)
                {
                    button = GetComponent<Button>();
                }
                return button;
            }
        }

        private void Awake()
        {
            button = GetComponent<Button>();
        }

        private void Update()
        {
            if (isInteractable != button.interactable)
            {
                isInteractable = button.interactable;
                UpdateComponent();
            }
            else
            {
                UpdateComponent();
            }
        }
        
        public void UpdateComponent()
        {
            foreach (var t in buttonTextList)
            {
                t.color = isInteractable ? enableColorText : disabledColorText;
            }
            foreach (var t in subImageList)
            {
                t.color = isInteractable ? enableColorOutline : disabledColorOutline;
            }
            foreach (var t in textImage)
            {
                t.color = isInteractable ? enableColorText : disabledColorText;
            }
        }
    }
}