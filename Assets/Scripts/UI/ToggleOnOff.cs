using UnityEngine;

namespace UI
{
    public class ToggleOnOff : MonoBehaviour
    {
        [SerializeField] private GameObject on;
        [SerializeField] private GameObject off;

        public void SetData(bool value)
        {
            if (value)
            {
                on.SetActive(true);
                off.SetActive(false);

            }
            else
            {
                off.SetActive(true);
                on.SetActive(false);
            }
        }
    }
}