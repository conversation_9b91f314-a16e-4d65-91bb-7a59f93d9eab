using Assets.Scripts.Managers;
using UnityEngine;
using UnityEngine.UI;

namespace UI.PlayerInfo
{
    public class MainPlayerInfoButton : MonoBehaviour
    {
        [SerializeField] private Button button;
        [SerializeField] private UIPlayerProfileMenu uiPlayerProfileMenu;
        private void Awake()
        {
            if (GameDataManager.Instance.IsOnline)
                BackendLoadData.Instance.LoadPlayerInfoServer(true);
            button.onClick.AddListener(OpenPlayerInfoMenu);
        }

        private void OpenPlayerInfoMenu()
        {
            var playerData = GameDataManager.Instance.LoadedPlayerData; 
            uiPlayerProfileMenu.SetCurrentDataPlayer(playerData);
        }
    }
}