using Assets.Scripts.Monster.Skill;
using System;
using System.Collections.Generic;
using System.Globalization;
using Assets.Scripts.Scriptables;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UI.BattleUI.UIBattleAbility;

namespace Assets.Scripts.UI.BattleUI.UIBattleAbility
{
    public sealed class UIAbilitySetItem : MonoBehaviour
    {
        [Header("Skill type")]
        [SerializeField] private TextMeshProUGUI skillTypeText;
        [SerializeField] private Image skillIcon;
        [SerializeField] private Image skillTypeBackground;
        [SerializeField] private Image skillTypeOutline;
        [Header("Skill Element")]
        [SerializeField] private Image elementIcon;
        [SerializeField] private Image elementBackground;
        [SerializeField] private Image elementOutline;
        [Header("Skill Rank")]
        [SerializeField] private TextMeshProUGUI skillRankText;
        [Header("Skill detail")]
        [SerializeField] private TextMeshProUGUI skillNameText;
        [SerializeField] private TextMeshProU<PERSON><PERSON> skillLevelText;
        [SerializeField] private TextMesh<PERSON><PERSON><PERSON><PERSON><PERSON> skillCostText;
        [SerializeField] private TextMeshProU<PERSON><PERSON> skillStCostText;
        [SerializeField] private TextMeshProUGUI skillDelayText;
        [SerializeField] private TextMeshProUGUI skillRangeText;
        [Header("Attack skill attribute")]
        [SerializeField] private GameObject attackInfoContainer;
        [SerializeField] private TextMeshProUGUI hpDmgText;
        [SerializeField] private TextMeshProUGUI stDmgText;
        [SerializeField] private TextMeshProUGUI delayDmgText;
        [SerializeField] private TextMeshProUGUI hitText;
        [SerializeField] private TextMeshProUGUI criText;
        [Header("Support skill attribute")]
        [SerializeField] private GameObject supportInfoContainer;
        [SerializeField] private TextMeshProUGUI hpHealText;
        [SerializeField] private TextMeshProUGUI stHealText;
        [SerializeField] private TextMeshProUGUI stDmgSpText;
        [SerializeField] private TextMeshProUGUI delayDmgSpText;
        [SerializeField] private TextMeshProUGUI hitSpText;
        [SerializeField] private GameObject blankEffect;
        [SerializeField] private List<MonsterDetailsInfo.SkillAlimentItem> effects;
        [SerializeField] private Color buffColor;
        [SerializeField] private Color debuffColor;
        [Header("Skill set")]
        [SerializeField] private GameObject skillSetBlockedObject;
        [SerializeField] private UIButtonExtention skillSetBtn;
        [SerializeField] private UIButtonExtention skillRemoveBtn;
        [SerializeField] private Button skillDetailBtn;
        [SerializeField] private List<Toggle> levelToggles = new();
        [SerializeField] private UIAbilitySetMenu _abilitySetMenu;
        private FarmMenu.RaiseUI.UIMonsterSkillDetail _skillDetailMenu;
        private DataStruct.SerializeMonster _monsterInfo;
        private int _maxSkillLevel;
        private Action<int> _onValidate { get; set; }

        public UIButtonExtention SkillSetBtn => skillSetBtn;
        public UIButtonExtention SkillRemoveBtn => skillRemoveBtn;
        public SkillDataScriptable SkillData { get; private set; }

        public int TargetLevel { get; private set; }

        private void Start()
        {
            for (var i = 0; i < levelToggles.Count; i++)
            {
                var levelIndex = i + 1;
                levelToggles[i].onValueChanged.AddListener(value =>
                {
                    if (!value) return;
                    TargetLevel = levelIndex;
                    SetSkillView();
                    int cost = GetTotalCost();
                    _onValidate?.Invoke(cost);
                });
            }
        }

        private int GetTotalCost()
        {
            int result = 0;
            var monsterData = _monsterInfo.MonsterScriptableData;
            for (int i = 0; i < _monsterInfo.MonsterSkillLevel.Count; i++)
            {
                if (_abilitySetMenu.CurrentEditSkill != null)
                {
                    if (_monsterInfo.MonsterSkillLevel[i].IsSelected && _abilitySetMenu.CurrentEditSkill.SkillId != _monsterInfo.MonsterSkillLevel[i].SkillId)
                    {
                        var monsterSkill = monsterData.SkillDataDict[_monsterInfo.MonsterSkillLevel[i].SkillId];
                        var skillDetail = monsterSkill.SkillDetail[_monsterInfo.MonsterSkillLevel[i].SelectLevel - 1];
                        result += skillDetail.SkillCost;
                    }
                    continue;
                }
                if (_monsterInfo.MonsterSkillLevel[i].IsSelected)
                {
                    var monsterSkill = monsterData.SkillDataDict[_monsterInfo.MonsterSkillLevel[i].SkillId];
                    var skillDetail = monsterSkill.SkillDetail[_monsterInfo.MonsterSkillLevel[i].SelectLevel - 1];
                    result += skillDetail.SkillCost;
                }
            }
            return result;
        }

        internal void SetSkillData(DataStruct.SerializeMonster monsterInfo, SkillDataScriptable skillData, int maxLevel, int selectLevel = 1)
        {
            this._monsterInfo = monsterInfo;
            _maxSkillLevel = maxLevel;
            TargetLevel = maxLevel;
            if (maxLevel == 0)
            {
                gameObject.SetActive(false);
                return;
            }
            gameObject.SetActive(true);
            SkillData = skillData;
            for (var i = 0; i < levelToggles.Count; i++)
            {
                if (selectLevel > 0 && i == selectLevel - 1)
                {
                    levelToggles[i].SetIsOnWithoutNotify(true);
                    levelToggles[i].gameObject.SetActive(true);
                    TargetLevel = selectLevel;
                }
                else if (i == maxLevel - 1 && selectLevel == 0)
                {
                    levelToggles[i].SetIsOnWithoutNotify(true);
                    levelToggles[i].gameObject.SetActive(true);
                    TargetLevel = maxLevel;
                }
                else if (i < maxLevel)
                {
                    levelToggles[i].gameObject.SetActive(true);
                    levelToggles[i].SetIsOnWithoutNotify(false);
                }
                else
                {
                    levelToggles[i].gameObject.SetActive(false);
                    levelToggles[i].SetIsOnWithoutNotify(false);
                }
            }
        }

        internal void SetSkillLockedRemoveable(bool isLocked)
        {
            skillSetBtn.gameObject.SetActive(!isLocked);
            skillSetBlockedObject.SetActive(isLocked);
            skillRemoveBtn.gameObject.SetActive(isLocked);
        }

        internal void SetSkillView(int selectLevel = 1, bool isSelected = false)
        {
            if (isSelected)
            {
                TargetLevel = selectLevel;
            }
            if (TargetLevel == 0) return;
            skillIcon.sprite = SkillData.SkillSprite;
            #region Skill Type and element
            switch (SkillData.SkillType)
            {
                case Enums.SkillTypesEnum.Str:
                    skillTypeBackground.color = Managers.GameComponentsSettingManager.Instance.StrSkillSecondaryColor;
                    skillTypeOutline.color = Managers.GameComponentsSettingManager.Instance.StrSkillPrimaryColor;
                    elementIcon.sprite = Managers.GameComponentsSettingManager.Instance.ElementDataDict[SkillData.SkillElementType].ElementIconWhite;
                    var strElementColor = Managers.GameComponentsSettingManager.Instance.ElementDataDict[SkillData.SkillElementType].ElementColor;
                    elementBackground.color = strElementColor;
                    elementOutline.color = Color.clear;
                    skillTypeText.text = SkillData.SkillType.ToString().ToUpper();
                    break;
                case Enums.SkillTypesEnum.Int:
                    skillTypeBackground.color = Managers.GameComponentsSettingManager.Instance.IntSkillSecondaryColor;
                    skillTypeOutline.color = Managers.GameComponentsSettingManager.Instance.IntSkillPrimaryColor;
                    elementIcon.sprite = Managers.GameComponentsSettingManager.Instance.ElementDataDict[SkillData.SkillElementType].ElementIconWhite;
                    var intElementColor = Managers.GameComponentsSettingManager.Instance.ElementDataDict[SkillData.SkillElementType].ElementColor;
                    elementBackground.color = intElementColor;
                    elementOutline.color = Color.clear;
                    skillTypeText.text = SkillData.SkillType.ToString().ToUpper();
                    break;
                case Enums.SkillTypesEnum.Heal:
                    skillTypeBackground.color = Managers.GameComponentsSettingManager.Instance.HealSkillSecondaryColor;
                    skillTypeOutline.color = Managers.GameComponentsSettingManager.Instance.HealSkillPrimaryColor;
                    elementIcon.sprite = Managers.GameComponentsSettingManager.Instance.HealSkillIcon;
                    var healColor = Managers.GameComponentsSettingManager.Instance.HealSkillSecondaryColor;
                    elementBackground.color = healColor;
                    elementOutline.color = Managers.GameComponentsSettingManager.Instance.HealSkillPrimaryColor;
                    skillTypeText.text = Helpers.ToUpperFirstLetter(SkillData.SkillType.ToString());
                    break;
                case Enums.SkillTypesEnum.Support:
                    skillTypeBackground.color = Managers.GameComponentsSettingManager.Instance.SupportSkillSecondaryColor;
                    skillTypeOutline.color = Managers.GameComponentsSettingManager.Instance.SupportSkillPrimaryColor;
                    elementIcon.sprite = Managers.GameComponentsSettingManager.Instance.SupportSkillIcon;
                    var supportColor = Managers.GameComponentsSettingManager.Instance.SupportSkillSecondaryColor;
                    elementBackground.color = supportColor;
                    elementOutline.color = Managers.GameComponentsSettingManager.Instance.SupportSkillPrimaryColor;
                    skillTypeText.text = Helpers.ToUpperFirstLetter(SkillData.SkillType.ToString());
                    break;
            }
            #endregion

            var logicLevel = Mathf.Clamp(TargetLevel - 1, 0, 2);

            skillNameText.text = I2.Loc.LocalizationManager.CurrentLanguageCode switch
            {
                "en" => SkillData.SkillName,
                "ja" => SkillData.SkillNameJP,
                _ => SkillData.SkillName
            };

            skillLevelText.text = TargetLevel.ToString();
            skillCostText.text = SkillData.SkillDetail[logicLevel].SkillCost.ToString();
            skillStCostText.text = SkillData.SkillDetail[logicLevel].StaminaConsume.ToString(CultureInfo.InvariantCulture);
            skillDelayText.text = SkillData.SkillDetail[logicLevel].SkillDelay.ToString(CultureInfo.InvariantCulture);
            skillRangeText.text = SkillData.SkillRange.ToString();
            var skillRank = SkillData.SkillDetail[logicLevel].SkillRank;
            skillRankText.text = skillRank.ToString().ToUpper();
            skillRankText.color = Managers.GameComponentsSettingManager.Instance.GetRankColor(skillRank);
            if (SkillData.SkillType is Enums.SkillTypesEnum.Heal or Enums.SkillTypesEnum.Support)
            {
                attackInfoContainer.SetActive(false);
                supportInfoContainer.SetActive(true);
            }
            else
            {
                attackInfoContainer.SetActive(true);
                supportInfoContainer.SetActive(false);
            }

            SetSkillStatValue(SkillValuesConvert.Get_Skill_HP_DMG_Rank(SkillData.SkillDetail[logicLevel].HpDmg), hpDmgText, SkillData.SkillDetail[logicLevel].HpDmg);
            SetSkillStatValue(SkillValuesConvert.Get_Skill_ST_DMG_Rank(SkillData.SkillDetail[logicLevel].StDmg), stDmgText, SkillData.SkillDetail[logicLevel].StDmg);
            SetSkillStatValue(SkillValuesConvert.Get_Skill_DELAY_DMG_Rank(SkillData.SkillDetail[logicLevel].DelayDmg), delayDmgText, SkillData.SkillDetail[logicLevel].DelayDmg);
            SetSkillStatValue(SkillValuesConvert.Get_Skill_HIT_Rank(SkillData.SkillDetail[logicLevel].HitRate * 100), hitText, SkillData.SkillDetail[logicLevel].HitRate * 100);
            SetSkillStatValue(SkillValuesConvert.Get_Skill_CRT_Rank(SkillData.SkillDetail[logicLevel].CritRate * 100), criText, SkillData.SkillDetail[logicLevel].CritRate * 100);
            SetSkillStatValue(SkillValuesConvert.Get_Skill_HP_HEAL_Rank(SkillData.SkillDetail[logicLevel].HpHeal * 100), hpHealText, SkillData.SkillDetail[logicLevel].HpHeal * 100);
            SetSkillStatValue(SkillValuesConvert.Get_Skill_ST_HEAL_Rank(SkillData.SkillDetail[logicLevel].StHeal), stHealText, SkillData.SkillDetail[logicLevel].StHeal);
            SetSkillStatValue(SkillValuesConvert.Get_Skill_ST_DMG_Rank(SkillData.SkillDetail[logicLevel].StDmg), stDmgSpText, SkillData.SkillDetail[logicLevel].StDmg);
            SetSkillStatValue(SkillValuesConvert.Get_Skill_DELAY_DMG_Rank(SkillData.SkillDetail[logicLevel].DelayDmg), delayDmgSpText, SkillData.SkillDetail[logicLevel].DelayDmg);
            SetSkillStatValue(SkillValuesConvert.Get_Skill_HIT_Rank(SkillData.SkillDetail[logicLevel].HitRate * 100), hitSpText, SkillData.SkillDetail[logicLevel].HitRate * 100);

            if (SkillData.SkillDetail[logicLevel].BuffValues.Count == 0 && SkillData.SkillDetail[logicLevel].DebuffValues.Count == 0)
            {
                blankEffect.SetActive(true);
                foreach (var t in effects)
                {
                    t.parentObject.SetActive(false);
                }
            }
            else
            {
                blankEffect.SetActive(false);
                var buffCount = 0;
                foreach (var t in effects)
                {
                    if (buffCount >= SkillData.SkillDetail[logicLevel].BuffValues.Count)
                    {
                        t.parentObject.SetActive(false);
                    }
                    else
                    {
                        t.parentObject.SetActive(true);
                        var buffEffectsScriptable = Managers.BattleAlimentDataManager.Instance.BuffEffectDetailDict[SkillData.SkillDetail[logicLevel].BuffValues[buffCount].BuffType];
                        t.icon.sprite = buffEffectsScriptable.BuffSprite;
                        t.descriptionText.text = I2.Loc.LocalizationManager.CurrentLanguageCode switch
                        {
                            "ja" => buffEffectsScriptable.BuffJPDescription.Replace("{skill}",
                                $"{SkillData.SkillDetail[logicLevel].BuffValues[buffCount].Value}"),
                            _ => buffEffectsScriptable.BuffDescription.Replace("{skill}",
                                $"{SkillData.SkillDetail[logicLevel].BuffValues[buffCount].Value}")
                        };
                        t.outlineBackground.color = buffColor;
                        buffCount++;
                    }
                }

                var debuffCount = 0;
                for (var i = buffCount; i < effects.Count; i++)
                {
                    if (debuffCount >= SkillData.SkillDetail[logicLevel].DebuffValues.Count)
                    {
                        effects[i].parentObject.SetActive(false);
                    }
                    else
                    {
                        effects[i].parentObject.SetActive(true);
                        var debuffEffectsScriptable = Managers.BattleAlimentDataManager.Instance.DebuffEffectDetailDict[SkillData.SkillDetail[logicLevel].DebuffValues[debuffCount].DebuffType];
                        effects[i].icon.sprite = debuffEffectsScriptable.DebuffSprite;
                        effects[i].descriptionText.text = I2.Loc.LocalizationManager.CurrentLanguageCode switch
                        {
                            "ja" => debuffEffectsScriptable.DebuffJPDescription.Replace("{skill}",
                                $"{SkillData.SkillDetail[logicLevel].DebuffValues[debuffCount].Value * 100f}"),
                            _ => debuffEffectsScriptable.DebuffDescription.Replace("{skill}",
                                $"{SkillData.SkillDetail[logicLevel].DebuffValues[debuffCount].Value * 100f}")
                        };
                        effects[i].outlineBackground.color = debuffColor;
                        debuffCount++;
                    }
                }
            }
        }

        internal void SetSkillDetailMenu(FarmMenu.RaiseUI.UIMonsterSkillDetail skillDetailMenu, UIAbilitySetMenu abilitySetMenu)
        {
            _skillDetailMenu = skillDetailMenu;
            _abilitySetMenu = abilitySetMenu;
            skillDetailBtn.onClick.RemoveListener(OpenSkillDetail);
            skillDetailBtn.onClick.AddListener(OpenSkillDetail);
        }

        private void SetSkillStatValue(RankTypeEnums rank, TextMeshProUGUI rankText, float value)
        {
            if (value == 0)
            {
                rankText.text = "-";
                rankText.color = Color.white;
            }
            else
            {
                rankText.text = rank.ToString().ToUpper();
                rankText.color = Managers.GameComponentsSettingManager.Instance.GetRankColor(rank);
            }
        }

        private void OpenSkillDetail()
        {
            if (!_skillDetailMenu) return;
            _skillDetailMenu.SetSkill(SkillData, _maxSkillLevel);
            _skillDetailMenu.OpenMenu();
        }

        internal void SetSkillButtonUnlock(bool value)
        {
            skillSetBtn.Button.interactable = value;
        }

        internal void AddValidation(Action<int> validateSkillCost)
        {
            _onValidate += validateSkillCost;
        }
    }
}