using CustomBattle;
using Network;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.BattleUI
{
    public class SurrenderPopupUI : MonoBehaviour
    {
        [SerializeField]
        private Button surrenderBtn;

        [SerializeField]
        private Button yesBtn;

        [SerializeField]
        private Button noBtn;

        private void Start()
        {
            ClosePoup();
            surrenderBtn.onClick.AddListener(OpenPoup);
        }

        public void OpenPoup()
        {
            gameObject.SetActive(true);
            yesBtn.onClick.AddListener(Surrender);
            noBtn.onClick.AddListener(ClosePoup);
        }

        private void ClosePoup()
        {
            gameObject.SetActive(false);
        }

        private void Surrender()
        {
            CustomNetworkManager.Instance.ClearReconnectionToken();
            if (CustomNetworkManager.Instance.GetRoom() != null)
            {
                CustomOnlineBattle.Instance.OnSurrenderMessage().GetAwaiter();
            }
     
            switch (CustomGameManager.Instance.PlayerSide)
            {
                case GameTeam.Red:
                    GameManager.Get().OnTeamWon.Invoke(GameTeam.Blue);
                    break;
                case GameTeam.Blue:
                    GameManager.Get().OnTeamWon.Invoke(GameTeam.Red);
                    break;
            }
            ClosePoup();
        }
    }
}