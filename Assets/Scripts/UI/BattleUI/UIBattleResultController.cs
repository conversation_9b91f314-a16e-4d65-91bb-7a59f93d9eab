using Assets.Scripts.Managers;
using UnityEngine;
using System.Collections.Generic;
using Assets.Scripts.SerializeDataStruct;
using Assets.Scripts.Enums;
using CustomBattle;
using Assets.Scripts.Tournament;
using Network;
using TurnBasedToolsAsset.Scripts.Gameplay.GameRules;
using Assets.Scripts.UI.BattleUI.PvP;
using Cysharp.Threading.Tasks;

namespace Assets.Scripts.UI.BattleUI
{
    public class UIBattleResultController : MonoBehaviour
    {
        [SerializeField] private UIBattleWinAnnouncer winAnnouncer;
        [SerializeField] private UIBattleLoseAnnouncer loseAnnouncer;
        [SerializeField] private UIBattleResultContent resultAnnouner;
        [SerializeField] private GameObject m_battleTimeUp;
        [SerializeField] private Transform canvas;
        private GameTeam _winningSide;
        UIBattleTimesUp timeUpMenu;

        private bool _isDataSend;

        private void Start()
        {
            _isDataSend = false;
            GameManager.Get().OnTeamWon.AddListener(HandleTeamWin);
            winAnnouncer.gameObject.SetActive(false);
            loseAnnouncer.gameObject.SetActive(false);
            var timeUpObject = Instantiate(m_battleTimeUp, canvas);
            timeUpMenu = timeUpObject.GetComponent<UIBattleTimesUp>();
        }

        private void HandleTeamWin(GameTeam winningSide)
        {
            AudioManager.Instance.StopAllClip();
            gameObject.SetActive(true);
            resultAnnouner.SetWinningTeam(winningSide);
            _winningSide = winningSide;
            Time.timeScale = 1.0f;
            if (CustomNetworkManager.Instance.GetRoom() != null)
            {
                SendPvpResult();
            }
            var customGameRules = GameManager.GetRules() as CustomGameRules;

            if (customGameRules != null && customGameRules.GetCurrentTurn() < GameManager.GetMaxTurn())
            {
                if (CustomGameManager.Instance.PlayerSide == GameTeam.Blue && winningSide == GameTeam.Blue ||
                    CustomGameManager.Instance.PlayerSide == GameTeam.Red && winningSide == GameTeam.Red)
                    WinAnnounce();
                else
                    LoseAnnounce();
            }
            else
            {
                timeUpMenu.SetValues(GameManager.GetHpDecision(GameTeam.Blue),
                    GameManager.GetDamageDecision(GameTeam.Blue), GameManager.GetStDecision(GameTeam.Blue),
                    GameManager.GetHpDecision(GameTeam.Red), GameManager.GetDamageDecision(GameTeam.Red),
                    GameManager.GetStDecision(GameTeam.Red), () =>
                    {
                        if (CustomGameManager.Instance.PlayerSide == GameTeam.Blue && winningSide == GameTeam.Blue ||
                            CustomGameManager.Instance.PlayerSide == GameTeam.Red && winningSide == GameTeam.Red)
                            WinAnnounce();
                        else
                            LoseAnnounce();
                    });
                timeUpMenu.OnTurnEnd();
            }
        }

        private void SendPvpResult()
        {
            if (_isDataSend)
            {
                return;
            }
            var roomState = CustomNetworkManager.Instance.GetRoom().State;
            var winner = _winningSide == GameTeam.Blue ? roomState.players[0].walletId : roomState.players[1].walletId;
            resultAnnouner.SetWinner(winner);
            if (_winningSide != CustomGameManager.Instance.PlayerSide)
            {
                return;
            }
            float userScore1 = CustomPvpModeAI.Instance.BlueScore;
            float userScore2 = CustomPvpModeAI.Instance.RedScore;
            var options = new Dictionary<string, object>
                {
                    { "overallA", CustomGameManager.Instance.GetTotalValueFromFriendlySpawnListInfo() },
                    { "overallB", CustomGameManager.Instance.GetTotalValueFromEnemySpawnListInfo() },
                    { "scoreA", userScore1 },
                    { "scoreB", userScore2 },
                    { "winnerUser", winner }
                };
            _isDataSend = true;
            CustomOnlineBattle.Instance.OnGameEndMessage(options).Forget();
        }
        private void WinAnnounce()
        {
            if (CustomNetworkManager.Instance.GetRoom() != null)
            {
                if (CustomNetworkManager.Instance.IsSpectator()) return;
                winAnnouncer.gameObject.SetActive(true);
                winAnnouncer.TriggerAnimation();
                UpdateMonsterStats(true);
                WinSet();
            }
            else
            {
                winAnnouncer.gameObject.SetActive(true);
                winAnnouncer.TriggerAnimation();
                UpdateMonsterStats(true);
                WinSet();
            }
            loseAnnouncer.gameObject.SetActive(false);
        }

        private void LoseAnnounce()
        {
            if (CustomNetworkManager.Instance.GetRoom() != null)
            {
                if (CustomNetworkManager.Instance.IsSpectator()) return;
                loseAnnouncer.gameObject.SetActive(true);
                loseAnnouncer.TriggerAnimation();
                UpdateMonsterStats(false);
                LoseSet();
            }
            else
            {
                loseAnnouncer.gameObject.SetActive(true);
                loseAnnouncer.TriggerAnimation();
                UpdateMonsterStats(false);
                LoseSet();
            }
            winAnnouncer.gameObject.SetActive(false);
        }

        private void WinSet()
        {
            AudioManager.Instance.PlayClipByName("Win");
        }

        private void LoseSet()
        {
            AudioManager.Instance.PlayClipByName("Lose");
            var selectedTournamentDetail = TournamentDataManager.Instance.SelectedTournament;
            if (selectedTournamentDetail == null) return;
            foreach (var tournamentData in GameDataManager.Instance.LoadedPlayerData.PlayerTournamentList.AsSpan())
            {
                if (tournamentData.TournamentID != selectedTournamentDetail.TournamentId ||
                    tournamentData.TournamentYear != GameProgressManager.Instance.CurrentGameYear) continue;
                tournamentData.SetTournamentWon(false);
                GameDataManager.OnTournamentDataChanged?.Invoke();
                break;
            }
        }

        void UpdateMonsterStats(bool win)
        {
            UpdateMonsterPvETournamentStat(win);
        }

        private void UpdateMonsterPvETournamentStat(bool win)
        {
            var selectedTournamentDetail = TournamentDataManager.Instance.SelectedTournament;

            if (selectedTournamentDetail == null) return;
            DebugPopup.InTraining(true);

            DebugPopup.LogRaise(win ? $"---PVE Battle result win---" : $"---PVE Battle result lose---");

            var gridUnits = GameManager.GetUnitsOnTeam(CustomGameManager.Instance.PlayerSide);

            //refresh monster from api
            foreach (var unit in gridUnits.AsSpan())
            {
                SerializeGrowthParameters battleGrowth = new();

                if (!GameDataManager.Instance.LoadedPlayerData.PlayerMonsterDict.TryGetValue(
                        unit.GameMonster.MonsterInfo.MonsterId, out var monsterInfo)) continue;
                DebugPopup.LogRaise($"---PVE Battle monster calculate = {monsterInfo.MonsterName}---");
                //Physical
                battleGrowth.AddPhysical(-5f);

                //Fatigue
                var fatigueTraitCorrection = monsterInfo.InnateTraitCombineValues.TrainingFatigueReduction;
                battleGrowth.AddFatigue(TrainingActionManager.Get_FatigueCaculatedIncrease(
                    TrainingParameterValues.Get_ActionPerformBaseFatigue(2), 0, 0, 0, fatigueTraitCorrection,
                    monsterInfo.ComplexPhysical, monsterInfo.ComplexTrainingPolicy));

                //Stress & friendship
                if (win)
                {
                    battleGrowth.AddStress(TrainingParameterValues.Get_ActionPerformBaseStress(2));
                    battleGrowth.AddFriendship(3f);
                }
                else
                {
                    var stressTraitCorrection = monsterInfo.InnateTraitCombineValues.TrainingStressReduction;
                    var trainingStress = TrainingActionManager.Get_StressCalculatedIncrease(
                        TrainingParameterValues.Get_ActionPerformBaseStress(3), 0, 0, 0, stressTraitCorrection,
                        monsterInfo.ComplexPhysical, monsterInfo.ComplexTrainingPolicy);
                    battleGrowth.AddStress(trainingStress);
                    battleGrowth.AddFriendship(-3f);
                }

                //Injury
                var injuryTypesEnum = monsterInfo.MonsterInjuryCondition;

                var damageTakeCorrection =
                    Mathf.Clamp01(unit.GameMonster.InGameParameter.HealthPercentageLeft - 1);

                var injuryChance = TrainingParameterValues.Get_InjuriesChanceFromTrainingResult(4);

                var injuyryInnateTraitCorrection =
                    monsterInfo.InnateTraitCombineValues.TrainingInjuryChance_Add;

                var injuryRate = TrainingActionManager.Get_InjuryProbabilityRate(injuryChance,
                    injuyryInnateTraitCorrection,
                    TrainingParameterValues.FatigueTypeConvert(battleGrowth.Fatigue), damageTakeCorrection);

                injuryTypesEnum =
                    TrainingActionManager.Get_CalculatedInjury(injuryTypesEnum, injuryRate.x, injuryRate.y);

                //Life time
                if (selectedTournamentDetail != null)
                {
                    var fatigueType = TrainingParameterValues.FatigueTypeConvert(monsterInfo.ComplexFatigue);
                    var stressType = TrainingParameterValues.StressTypeConvert(monsterInfo.ComplexStress);
                    battleGrowth.AddLifespan(
                        TrainingActionManager.Get_LifeSpanConsumption(0, -30, fatigueType, stressType));
                }

                //Pve Rank up
                if (win)
                {
                    foreach (var participant in gridUnits.AsSpan())
                    {
                        if (GameDataManager.Instance.LoadedPlayerData.PlayerMonsterDict.TryGetValue(
                                participant.GameMonster.MonsterInfo.MonsterId, out var monster))
                        {
                            monster.SetRankSWon(selectedTournamentDetail.TournamentId);
                        }
                    }

                    if (selectedTournamentDetail.IsOfficialTournament &&
                        monsterInfo.MonsterRank == selectedTournamentDetail.TournamentRank)
                    {
                        switch (monsterInfo.MonsterRank)
                        {
                            case RankTypeEnums.f:
                                monsterInfo.SetMonsterRank(RankTypeEnums.e);
                                GameProgressManager.Instance.UpdateHighestMonsterRank(RankTypeEnums.e);
                                break;
                            case RankTypeEnums.e:
                                monsterInfo.SetMonsterRank(RankTypeEnums.d);
                                GameProgressManager.Instance.UpdateHighestMonsterRank(RankTypeEnums.d);
                                break;
                            case RankTypeEnums.d:
                                monsterInfo.SetMonsterRank(RankTypeEnums.c);
                                GameProgressManager.Instance.UpdateHighestMonsterRank(RankTypeEnums.c);
                                break;
                            case RankTypeEnums.c:
                                monsterInfo.SetMonsterRank(RankTypeEnums.b);
                                GameProgressManager.Instance.UpdateHighestMonsterRank(RankTypeEnums.b);
                                break;
                            case RankTypeEnums.b:
                                monsterInfo.SetMonsterRank(RankTypeEnums.a);
                                GameProgressManager.Instance.UpdateHighestMonsterRank(RankTypeEnums.a);
                                break;
                            case RankTypeEnums.a:
                                monsterInfo.SetMonsterRank(RankTypeEnums.s);
                                GameProgressManager.Instance.UpdateHighestMonsterRank(RankTypeEnums.s);
                                break;
                        }
                    }
                }
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                DebugPopup.LogRaise($"///");
                DebugPopup.LogRaise($"---Battle Growth parameter---");
                DebugPopup.LogRaise($"Battle Fatigue = {battleGrowth.Fatigue}");
                DebugPopup.LogRaise($"Battle Stress = {battleGrowth.Stress}");
                DebugPopup.LogRaise($"Battle Physical = {battleGrowth.Physical}");
                DebugPopup.LogRaise($"Battle TrainingPolicy = {battleGrowth.TrainingPolicy}");
                DebugPopup.LogRaise($"Battle Friendship = {battleGrowth.Friendship}");
                DebugPopup.LogRaise($"Battle Lifespan = {battleGrowth.Lifespan}");
                DebugPopup.LogRaise($"///");
#endif
                monsterInfo.MonsterAlteredTrainingP.AddFatigue(battleGrowth.Fatigue);
                monsterInfo.MonsterAlteredTrainingP.AddStress(battleGrowth.Stress);
                monsterInfo.MonsterAlteredTrainingP.AddPhysical(battleGrowth.Physical);
                monsterInfo.MonsterAlteredTrainingP.AddTrainingPolicy(battleGrowth.TrainingPolicy);
                monsterInfo.MonsterAlteredTrainingP.AddFriendship(battleGrowth.Friendship);
                monsterInfo.MonsterAlteredTrainingP.AddLifespan(battleGrowth.Lifespan);
                if (injuryTypesEnum != InjuryTypesEnum.None)
                    monsterInfo.SetInjuryStatus(injuryTypesEnum);
                monsterInfo.IsDataChanged = true;
            }

            GameDataManager.OnMonsterDataChanged?.Invoke();
            DebugPopup.InTraining(false);
        }
    }
}