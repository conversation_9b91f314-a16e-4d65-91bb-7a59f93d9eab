using Assets.Scripts.Enums;
using Assets.Scripts.Managers;
using Assets.Scripts.Scriptables;
using Assets.Scripts.SerializeDataStruct;
using DG.Tweening;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.BattleUI
{
    public class UIMonsterBattleInfo : MonoBehaviour
    {
        [SerializeField] private Transform buffContainer;
        [SerializeField] private GameObject buffFragmentPrefab;
        [SerializeField] private TextMeshProUGUI monsterNameText;
        [SerializeField] private TextMeshProUGUI monsterBattleIdText;
        [SerializeField] private Image monsterRankImage;
        [Space(5)]
        [SerializeField] private TextMeshProUGUI monsterDelayText;
        [SerializeField] private TextMeshProUGUI mosnterMoveText;
        [SerializeField] private TextMeshProUGUI monsterStaRegenText;
        [Space(5)]
        [SerializeField] private TextMeshProUGUI monsterHealthValueText;
        [SerializeField] private TextMeshProUGUI monsterStaValueText;
        [SerializeField] private Slider monsterHealthSlider;
        [SerializeField] private Slider monsterStaSlider;
        [Header("Graphic components")]
        [SerializeField] private Image backgroundContainerGraph;
        [SerializeField] private Image backgroundOutlineGraph;
        [Space(5)]
        [SerializeField] private Image monsterBackground;
        [SerializeField] private Image monsterOutlineGraph;
        [Space(5)]
        [SerializeField] private Image monsterImage;
        [SerializeField] private Image turnBlockImage;
        [Header("Color control")]
        [SerializeField] private Color blockColor;
        [Space(5)]
        [SerializeField] private Color friendlyBackgroundColor;
        [SerializeField] private Color friendlyBackgroundOutlineColor;
        [SerializeField] private Color friendlyMonsterColor;
        [SerializeField] private Color friendlyOutlineMonsterColor;
        [Space(5)]
        [SerializeField] private Color hostileBackgroundColor;
        [SerializeField] private Color hostileBackgroundOutlineColor;
        [SerializeField] private Color hostileMonsterColor;
        [SerializeField] private Color hostileOutlineMonsterColor;

        [SerializeField] private Button detailBuffBtn;
        private int monsterRefId;
        private List<MonsterBuffData> alimentDataList = new();
        private float originalSpd;
        private float currentSpd;

        public int MonsterRefId => monsterRefId;
        public Button DetailBuffBtn => detailBuffBtn;

        public void SetMonsterRef(int monsterRefId, string monsterName, RankTypeEnums monsterRank, float maxSta, float currentSta, float maxHp, float currentHp, float spd, float move, float staRegen, Sprite monsterSprite)
        {
            this.monsterRefId = monsterRefId;
            monsterNameText.text = monsterName;
            monsterRankImage.sprite = GameComponentsSettingManager.Instance.GetRank(monsterRank);
            monsterStaRegenText.text = Mathf.CeilToInt(staRegen).ToString();
            monsterImage.sprite = monsterSprite;
            monsterBattleIdText.text = Helpers.ConvertNumberToAlphabet(this.monsterRefId);

            monsterDelayText.text = spd.ToString();
            originalSpd = spd;
            currentSpd = spd;
            mosnterMoveText.text = move.ToString();

            monsterHealthValueText.text = Mathf.CeilToInt(currentHp).ToString();
            monsterHealthSlider.maxValue = maxHp;
            monsterHealthSlider.value = currentHp;

            monsterStaSlider.maxValue = maxSta;
            monsterStaSlider.value = currentSta;
            monsterStaValueText.text = Mathf.CeilToInt(currentSta).ToString();
        }

        public void UpdateMonsterHealthValue(float currentHp)
        {
            monsterHealthSlider.DOValue(currentHp, 0.5f, true).SetEase(Ease.Linear);
            monsterHealthValueText.text = Mathf.Clamp(Mathf.CeilToInt(currentHp), 0, monsterHealthSlider.maxValue).ToString();
        }

        public void UpdateMonsterStaValue(float currentSta)
        {
            monsterStaSlider.DOValue(currentSta, 0.5f, true).SetEase(Ease.Linear);
            monsterStaValueText.text = Mathf.Clamp(Mathf.CeilToInt(currentSta), 0, 100).ToString();
        }

        public void UpdateMonsterDelay(float delay)
        {
            float newSpd = originalSpd - delay;
            if (currentSpd == Mathf.Max(newSpd, 0)) return;
            currentSpd = Mathf.Max(newSpd, 0);
            if (currentSpd > originalSpd)
            {
                monsterDelayText.DOText($"{currentSpd:0}(+{delay:0})", 0.5f, true, ScrambleMode.Numerals);
                return;
            }

            if (currentSpd < this.originalSpd)
            {
                monsterDelayText.DOText($"{currentSpd:0}(-{delay:0})", 0.5f, true, ScrambleMode.Numerals);
                return;
            }
            if (currentSpd == originalSpd)
            {
                monsterDelayText.DOText($"{currentSpd:0}", 0.5f, true, ScrambleMode.Numerals);
                return;
            }
        }

        public void SetTurn(bool isTurn) => turnBlockImage.color = isTurn ? Color.clear : blockColor;

        public void SetIsDefeated(bool isDefeated) => turnBlockImage.color = isDefeated ? blockColor : Color.clear;

        internal void SetHostile()
        {
            backgroundContainerGraph.color = hostileBackgroundColor;
            backgroundOutlineGraph.color = hostileBackgroundOutlineColor;
            monsterBackground.color = hostileMonsterColor;
            monsterOutlineGraph.color = hostileOutlineMonsterColor;
        }

        internal void SetFriendly()
        {
            backgroundContainerGraph.color = friendlyBackgroundColor;
            backgroundOutlineGraph.color = friendlyBackgroundOutlineColor;
            monsterBackground.color = friendlyMonsterColor;
            monsterOutlineGraph.color = friendlyOutlineMonsterColor;
        }

        internal void UpdateBuff(List<SerializeIngameBuff> inGameBuffDetails)
        {
            if (alimentDataList.Count <= 0)
            {
                for (int i = 0; i < inGameBuffDetails.Count; i++)
                {
                    GameObject buffFragmentGameobject = Instantiate(buffFragmentPrefab, buffContainer);
                    UIBuffFragment buffFragment = buffFragmentGameobject.GetComponent<UIBuffFragment>();
                    if (BattleAlimentDataManager.Instance.BuffEffectDetailDict.TryGetValue(inGameBuffDetails[i].BuffType, out BuffEffectsScriptable buffEffectData))
                    {
                        buffFragment.SetBufficon(buffEffectData.BuffSprite, true);
                        alimentDataList.Add(new(buffFragment, true, default, inGameBuffDetails[i].BuffType));
                    }
                }
                return;
            }

            List<MonsterBuffData> existBuffs = new();

            for (int i = 0; i < inGameBuffDetails.Count; i++)
            {
                existBuffs.Add(new(null, true, default, inGameBuffDetails[i].BuffType));
            }

            // Remove elements from listA that don't exist in listB and have isGoodBuff = true
            List<MonsterBuffData> removebuffs = alimentDataList.FindAll(a => a.IsBuff && !existBuffs.Exists(b => b.Comparebuff(a)));
            for (int i = 0; i < removebuffs.Count; i++)
            {
                GameObject referenceObject = removebuffs[i].BuffFragment.gameObject;
                alimentDataList.Remove(removebuffs[i]);
                Destroy(referenceObject);
            }

            // Add elements from listB that are not present in listA
            for (int i = 0; i < existBuffs.Count; i++)
            {
                int buffIndex = i;
                MonsterBuffData buffData = existBuffs[buffIndex];
                if (!alimentDataList.Exists(a => a.Comparebuff(buffData)))
                {
                    GameObject buffFragmentGameobject = Instantiate(buffFragmentPrefab, buffContainer);
                    UIBuffFragment buffFragment = buffFragmentGameobject.GetComponent<UIBuffFragment>();

                    if (BattleAlimentDataManager.Instance.BuffEffectDetailDict.TryGetValue(inGameBuffDetails[i].BuffType, out BuffEffectsScriptable buffDetail))
                    {
                        buffFragment.SetBufficon(buffDetail.BuffSprite, true);
                        alimentDataList.Add(new MonsterBuffData(buffFragment, existBuffs[buffIndex].IsBuff, existBuffs[buffIndex].Debuff, existBuffs[buffIndex].Buff));
                    }
                }
            }
        }

        internal void UpdateBuff(List<SerializeIngameDebuff> inGameDeBuffDetails)
        {
            if (alimentDataList.Count <= 0)
            {
                for (int i = 0; i < inGameDeBuffDetails.Count; i++)
                {
                    GameObject buffFragmentGameobject = Instantiate(buffFragmentPrefab, buffContainer);
                    UIBuffFragment buffFragment = buffFragmentGameobject.GetComponent<UIBuffFragment>();
                    if (BattleAlimentDataManager.Instance.DebuffEffectDetailDict.TryGetValue(inGameDeBuffDetails[i].DebuffType, out DebuffEffectsScriptable debuffDetail))
                    {
                        buffFragment.SetBufficon(debuffDetail.DebuffSprite, false);
                        alimentDataList.Add(new(buffFragment, false, inGameDeBuffDetails[i].DebuffType, default));
                    }
                }
                return;
            }

            List<MonsterBuffData> existBuffs = new();

            for (int i = 0; i < inGameDeBuffDetails.Count; i++)
            {
                existBuffs.Add(new(null, false, inGameDeBuffDetails[i].DebuffType, default));
            }

            // Remove elements from listA that don't exist in listB and have isGoodBuff = true
            List<MonsterBuffData> removebuffs = alimentDataList.FindAll(a => !a.IsBuff && !existBuffs.Exists(b => b.Comparebuff(a)));
            for (int i = 0; i < removebuffs.Count; i++)
            {
                GameObject referenceObject = removebuffs[i].BuffFragment.gameObject;
                alimentDataList.Remove(removebuffs[i]);
                Destroy(referenceObject);
            }

            // Add elements from listB that are not present in listA
            for (int i = 0; i < existBuffs.Count; i++)
            {
                int buffIndex = i;
                MonsterBuffData buffData = existBuffs[buffIndex];
                if (!alimentDataList.Exists(a => a.Comparebuff(buffData)))
                {
                    GameObject buffFragmentGameobject = Instantiate(buffFragmentPrefab, buffContainer);
                    UIBuffFragment buffFragment = buffFragmentGameobject.GetComponent<UIBuffFragment>();
                    if (BattleAlimentDataManager.Instance.DebuffEffectDetailDict.TryGetValue(inGameDeBuffDetails[i].DebuffType,out var debuffData))
                    {
                        buffFragment.SetBufficon(debuffData.DebuffSprite, false);

                        alimentDataList.Add(new MonsterBuffData(buffFragment, existBuffs[buffIndex].IsBuff, existBuffs[buffIndex].Debuff, existBuffs[buffIndex].Buff));
                    }
                }
            }
        }

        private class MonsterBuffData
        {
            private UIBuffFragment buffFragment;
            private bool isBuff = false;
            private DebuffsEnum debuff;
            private BuffsEnum buff;

            public MonsterBuffData() { }

            public MonsterBuffData(UIBuffFragment buffFragment, bool isGoodBuff, DebuffsEnum debuff, BuffsEnum buff)
            {
                this.buffFragment = buffFragment;
                this.isBuff = isGoodBuff;
                this.debuff = debuff;
                this.buff = buff;
            }

            public UIBuffFragment BuffFragment => buffFragment;
            public bool IsBuff => isBuff;
            public DebuffsEnum Debuff => debuff;
            public BuffsEnum Buff => buff;

            public bool Comparebuff(MonsterBuffData otherBuff)
            {
                if (otherBuff == null) return false;
                if (otherBuff.Debuff == this.debuff && !isBuff) return true;
                if (otherBuff.Buff == this.buff && isBuff) return true;
                return false;
            }
        }
    }
}