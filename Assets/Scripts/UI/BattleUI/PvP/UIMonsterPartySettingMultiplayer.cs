using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.Controllers;
using Assets.Scripts.DataStruct;
using Assets.Scripts.Helper;
using Assets.Scripts.Managers;
using Assets.Scripts.SerializeDataStruct;
using Assets.Scripts.SerializeDataStruct.Data;
using Assets.Scripts.Tournament;
using Assets.Scripts.UI.Monsterbag;
using Assets.Scripts.UI.TabMenu;
using Assets.Scripts.UI.Utilities;
using Colyseus.Schema;
using DG.Tweening;
using I2.Loc;
using Monster.PVE_Monster;
using Network;
using TMPro;
using UI.Utilities;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

namespace Assets.Scripts.UI.BattleUI.PvP
{
    public enum PvpGameMode
    {
        FreeBattle,
        PrizedBattle,
        RankedBattle,
        TournamentBattle,
        TournamentMatch,
        None
    }

    public class UIMonsterPartySettingMultiplayer : UIMonsterPartySetting
    {
        [Header("PVP Party Setting")]
        [SerializeField]
        private TextMeshProUGUI partyModeName;

        [SerializeField] private Button readyBtn;
        [SerializeField] private Button exitBtn;
        [SerializeField] private Button closeMenuBtn;
        [SerializeField] private TextMeshProUGUI countDownText;
        [SerializeField] private Slider countDownSlider;
        [SerializeField] private UIWaitingNotify waitingNotify;
        [SerializeField] private TournamentConditionParty tournamentConditionParty;
        [SerializeField] private TournamentConditionParty tournamentConditionPartyWarning;
        [SerializeField] private Button tournamentConditionButton;

        private string _monsterRank;
        private bool _isMemoryAllow;
        private bool _isCorrectParty;
        private PvpGameMode _pvpGameMode;

        private UIFreeBattleRoom _uiFreeBattleRoom;

        [Header("Information Box")]
        [SerializeField]
        private Button infoButton;

        [SerializeField] private UIBlankControl freeBattleIbox;
        [SerializeField] private UIBlankControl rankedBattleIbox;
        [SerializeField] private UIBlankControl tournamentBattleIbox;

        private void Awake()
        {
            OnMenuClose.AddListener(() =>
            {
                waitingNotify.CloseMenu();
                if (_uiFreeBattleRoom)
                {
                    _uiFreeBattleRoom.CloseMenu();
                }
            });
            OnMenuOpen.AddListener(AddListeners);
            AddListeners();

            if (GameDataManager.Instance.LoadedPlayerData.PlayerPartyGroupList != null)
            {
                if (GameDataManager.Instance.LoadedPlayerData.PlayerPartyGroupList.Count == 0)
                    CreateParty();
                ReloadPartyData();
                SelectParty(partyFarmTab.FirstOrDefault());
            }

            OnMenuOpen.AddListener(() =>
            {
                MenuSetup();
                ReloadPartyData();
            });
            MenuSetup();
            ValidatePartySetting(true);

            readyBtn.onClick.AddListener(() =>
            {
                ValidatePartySetting(false);
                if (_isCorrectParty)
                {
                    var currentRoom = CustomNetworkManager.Instance.GetCurrentRoom();
                    if (currentRoom == null)
                    {
                        LoadBattleScene();
                    }
                    else
                    {
                        OnPartyReadyMessage();
                        waitingNotify.OpenMenu();
                    }
                }

                readyBtn.interactable = false;
            });
            addPartyOption.onClick.AddListener(CreateParty);
            exitBtn.onClick.AddListener(Exit);
            closeMenuBtn.onClick.AddListener(Exit);
            OnMenuClose.AddListener(RemoveListeners);
        }

        private void AddListeners()
        {
            GameDataManager.OnMonsterPartyUpdated += ReloadPartyData;
            GameDataManager.OnMonsterDataUpdated += ReloadPartyData;
        }

        private void RemoveListeners()
        {
            GameDataManager.OnMonsterPartyUpdated -= ReloadPartyData;
            GameDataManager.OnMonsterDataUpdated -= ReloadPartyData;
        }

        private void OnApplicationQuit()
        {
            if (CustomNetworkManager.Instance.GetRoom() != null)
            {
                OnSurrenderMessage();
            }
        }

        private void OnDestroy()
        {
            RemoveListeners();
        }

        private void MenuSetup()
        {
            exitBtn.interactable = true;
            countDownSlider.value = 60;
            countDownSlider.maxValue = 60;
            infoButton.onClick.RemoveAllListeners();
            infoButton.onClick.AddListener(() =>
            {
                switch (_pvpGameMode)
                {
                    case PvpGameMode.FreeBattle:
                        freeBattleIbox.OpenMenu();
                        freeBattleIbox.GetComponentInChildren<UITabGroupColor>().SetCurrentPage(3);
                        break;
                    case PvpGameMode.RankedBattle:
                    case PvpGameMode.PrizedBattle:
                        rankedBattleIbox.OpenMenu();
                        rankedBattleIbox.GetComponentInChildren<UITabGroupColor>().SetCurrentPage(2);
                        break;
                    case PvpGameMode.TournamentBattle:
                        tournamentBattleIbox.OpenMenu();
                        rankedBattleIbox.GetComponentInChildren<UITabGroupColor>().SetCurrentPage(4);
                        break;
                }
            });
            LoadPartyButton();
            LoadPartyGroup();
            ValidatePartySetting(true);
        }

        public void SetPvPMode(PvpGameMode gameMode, int monsterNum, string battleRank)
        {
            if (CustomNetworkManager.Instance.GetRoom() != null)
            {
                _ = CustomNetworkManager.Instance.LeaveRoom();
            }
            countDownSlider.gameObject.transform.parent.gameObject.SetActive(true);

            CustomPvpModeAI.Instance.IsPvpModeAI = true;
            CustomPvpModeAI.Instance.BattleRank = battleRank;
            _pvpGameMode = gameMode;
            CustomPvpModeAI.Instance.MonsterNum = monsterNum;
            _monsterRank = battleRank;
            _isMemoryAllow = false;
            switch (gameMode)
            {
                case PvpGameMode.RankedBattle:
                    partyModeName.text = $"Ranked Battle ({_monsterRank})";
                    break;
                case PvpGameMode.PrizedBattle:
                    partyModeName.text = $"Prize Ranked Match ({_monsterRank})";
                    break;
            }

            OpenMenu();
            countDownSlider.value = 60;
            countDownSlider.maxValue = 60;
            StartCoroutine(CountDownSlider());
        }

        public void SetPveMode(PvpGameMode gameMode, int monsterNum, string battleRank, string specialHex, string turnNum, bool isMemoryAllow, string terrainsType)
        {
            if (CustomNetworkManager.Instance.GetRoom() != null)
                CustomNetworkManager.Instance.LeaveRoom();
            countDownSlider.gameObject.transform.parent.gameObject.SetActive(false);
            CustomPvpModeAI.Instance.IsPveMode = true;
            CustomPvpModeAI.Instance.BattleRank = battleRank;
            CustomPvpModeAI.Instance.InGameTerrain = CustomPvpModeAI.GetTerrain(terrainsType);
            CustomPvpModeAI.Instance.TurnNum = int.Parse(turnNum);
            CustomPvpModeAI.Instance.SpecialHex = specialHex;
            CustomPvpModeAI.Instance.MonsterNum = monsterNum;
            _pvpGameMode = gameMode;
            _monsterRank = battleRank;
            _isMemoryAllow = isMemoryAllow;
            switch (gameMode)
            {
                case PvpGameMode.FreeBattle:
                    partyModeName.text = "Free Battle";
                    break;
            }

            OpenMenu();
        }

        private IEnumerator CountDownSlider()
        {
            var timeLeft = 60f;
            while (timeLeft > 0)
            {
                timeLeft -= Time.deltaTime;
                countDownSlider.value = timeLeft;
                // Calculate minutes and seconds
                var minutes = Mathf.FloorToInt(timeLeft / 60);
                var seconds = Mathf.FloorToInt(timeLeft % 60);
                // Update the countdown text (format: Remaining... mm:ss)
                LocalizedString remainingText = "Remaining...";
                countDownText.text = $"{remainingText} {minutes:0}:{seconds:00}";

                yield return null;
            }
            // When countdown is finished, trigger the countdown finished event
            OnCountdownFinished();
        }

        private void UpdateMultiPlayerPartyConfig()
        {
            //this.closeBtn.interactable = false;
            if (CustomNetworkManager.Instance.GetRoom() == null) return;
            var roomState = CustomNetworkManager.Instance.GetGameState();
            var monsterNumString = roomState.metadata.monsterNum;
            CustomPvpModeAI.Instance.MonsterNum = int.TryParse(monsterNumString, out var monsterNum) ? monsterNum : 3;
            _monsterRank = roomState.metadata.monsterRank;
            CustomPvpModeAI.Instance.BattleRank = _monsterRank;

            if (CustomNetworkManager.Instance.GetRoom().Name.ToLower().Contains("free"))
            {
                partyModeName.text = "Free Battle";
                _pvpGameMode = PvpGameMode.FreeBattle;
                _isMemoryAllow = roomState.metadata.memoryAllow;

            }
            else if (CustomNetworkManager.Instance.GetRoom().Name.ToLower().Contains("ranked"))
            {
                partyModeName.text = $"Ranked Battle ({_monsterRank})";
                _pvpGameMode = PvpGameMode.RankedBattle;
                _isMemoryAllow = false;

            }
            else if (CustomNetworkManager.Instance.GetRoom().Name.ToLower().Contains("prize"))
            {
                partyModeName.text = $"Prize Ranked Match ({_monsterRank})";
                _pvpGameMode = PvpGameMode.PrizedBattle;
                _isMemoryAllow = false;

            }
            else
            {
                partyModeName.text = "Sponsored Tournament";
                _pvpGameMode = PvpGameMode.TournamentBattle;
                tournamentConditionParty.SetData(TournamentManager.Instance.CurrentTournament);
                tournamentConditionButton.gameObject.SetActive(true);
                exitBtn.gameObject.SetActive(false);
                _isMemoryAllow = roomState.metadata.memoryAllow;
            }
        }

        private void OnBothReady(string obj)
        {
            waitingNotify.DeactivateCloseButton();
            CustomNetworkManager.Instance.partyReferenceId = ReferencePartyId;
            if (GameDataManager.Instance.LoadedPlayerData.PlayerPartyGroupDict.TryGetValue(ReferencePartyId, out var partyGroup))
            {
                for (var i = 0; i < CustomPvpModeAI.Instance.MonsterNum; i++)
                {
                    if (partyGroup.PartyId != ReferencePartyId) continue;
                    var set = partyGroup.PartySetup[i];
                    if (string.IsNullOrEmpty(set.MonsterId)) continue;
                    if (!GameDataManager.Instance.LoadedPlayerData.PlayerMonsterDict.TryGetValue(set.MonsterId,
                            out var monster)) continue;
                    if (!string.IsNullOrEmpty(monster.MonsterId) && monster.MonsterScriptableData)
                    {
                        OnSendParty(monster.MonsterId, monster.InnateTraitCombineValues.RandomNumber_Add);
                    }
                }
            }
            else
            {
                Debug.LogWarning($"Party Group {ReferencePartyId} not found");
            }

            CustomNetworkManager.Instance.GetRoom().State.OnMonstersChange(BothReady);
        }

        private void BothReady(ArraySchema<Network.RoomState.Monster> currentValue, ArraySchema<Network.RoomState.Monster> previousValue)
        {
            LoadBattleScene();
        }

        private void LoadBattleScene()
        {
            CustomPvpModeAI.Instance.PvpGameMode = _pvpGameMode;
            CustomNetworkManager.Instance.previousGameMode = _pvpGameMode;

            if (CustomPvpModeAI.Instance.IsTournamentMode == false)
            {
                CustomPvpModeAI.Instance.IsBattleMenuActive = true;
            }
            if (CustomNetworkManager.Instance.GetRoom() != null)
            {
                CustomNetworkManager.Instance.GetRoom().RemoveMessage("countdown");
                CustomNetworkManager.Instance.GetRoom().RemoveMessage("countdown_finished");
                CustomNetworkManager.Instance.GetRoom().RemoveMessage("opponent_sur");
                CustomNetworkManager.Instance.GetRoom().RemoveMessage("both_ready");
            }

            if (SceneHelper.Instance is null)
            {
                SceneManager.LoadScene(SceneManager.GetActiveScene().buildIndex + 1);
            }
            else
            {
                SceneHelper.Instance.LoadScene(2, 1,
                    _pvpGameMode is PvpGameMode.TournamentBattle or PvpGameMode.TournamentMatch
                        ? TournamentManager.Instance.CurrentTournament : null);
            }

            CloseMenu();
        }

        private void CreateParty()
        {
            var party = GameDataManager.Instance.LoadedPlayerData.PlayerPartyGroupList.LastOrDefault();
            GameDataController.CreateParty(party != null
                ? new SerializePartyGroup(string.Empty, party.PartyId + 1, $"Party {party.PartyId + 1}", true)
                : new SerializePartyGroup(string.Empty, 1, $"Party 1", true));
        }

        private void ReloadPartyData()
        {
            LoadPartyButton();
            LoadPartyGroup();
            ValidatePartySetting(false);
        }

        private void LoadPartyButton()
        {
            foreach (var party in GameDataManager.Instance.LoadedPlayerData.PlayerPartyGroupList)
            {
                var partyTab = partyFarmTab.FirstOrDefault(x => x.PartyId == party.PartyId);
                if (partyTab != null)
                {
                    partyTab.RefreshPartyName();
                }
                else
                {
                    CreatePartyButton(party.PartyId, party);
                }
            }
        }

        private void LoadPartyGroup()
        {
            var playerFarm = GameDataManager.Instance.LoadedPlayerData.PlayerFarmList;

            if (playerFarm == null || playerFarm.Count == 0)
            {
                UIPopupPanel.Instance.SetupConfirm("Party Group", "No Farm Selected", UIPopupPanel.Instance.CloseMenu);
                UIPopupPanel.Instance.OpenMenu();
                return;
            }

            var cardsToCreate = Math.Min(playerFarm.Count, CustomPvpModeAI.Instance.MonsterNum);

            for (var i = 0; i < cardsToCreate; i++)
            {
                if (PartyCardSpawned.Count < cardsToCreate)
                {
                    CreatePartyCard(i);
                }
            }
            UpdateCardsView();
        }

        private void CreatePartyButton(int partyId, SerializePartyGroup partyGroup)
        {
            if (partyFarmTab.Count >= 10)
            {
                addPartyOption.interactable = false;
                addPartyOption.gameObject.SetActive(false);
                return;
            }
            if (partyFarmTab.Exists(x => x.PartyId == partyId))
            {
                var partyTab = partyFarmTab.FirstOrDefault(x => x.PartyId == partyId);
                if (partyTab != null) partyTab.RefreshPartyName();
            }
            else
            {
                var tabButton = Instantiate(prefabPartyButton, partyButtonHolder);
                var partyTab = tabButton.GetComponent<UIPartyTab>();
                partyFarmTab.Add(partyTab);
                partyTab.SetParty(partyGroup.PartyName, partyId, () => SelectParty(partyTab));
            }
        }

        private void CreatePartyCard(int index)
        {
            var instantiate = Instantiate(farmcardPrefab, farmcardContainer);
            if (!instantiate.TryGetComponent(out UIPartyCard farmCard)) return;
            PartyCardSpawned.Add(farmCard);
            farmCard.SetCardNumber(index, displayController.GetRender(index));
            farmCard.SetRequireMenu(partyBag, this, farmPartyMenu);
            farmPartyMenu.SetUpPartyMenu(this);
        }

        private void UpdateCardsView()
        {
            if (!GameDataManager.Instance.LoadedPlayerData.PlayerPartyGroupDict.TryGetValue(referencePartyId, out var partyGroup)) return;

            _isCorrectParty = true;

            for (var i = 0; i < CustomPvpModeAI.Instance.MonsterNum; i++)
            {
                if (partyGroup.PartyId != referencePartyId) continue;
                var set = partyGroup.PartySetup[i];
                if (string.IsNullOrEmpty(set.MonsterId)) continue;
                if (!GameDataManager.Instance.LoadedPlayerData.PlayerMonsterDict.TryGetValue(set.MonsterId, out var monster)) continue;
                if (string.IsNullOrEmpty(monster.MonsterId) || !monster.MonsterScriptableData) continue;
                var skillCost = CalculateSkillCost(monster);
                var skillCostCorrect = CheckSkillCost(_monsterRank, skillCost);
                if (skillCostCorrect) continue;
                _isCorrectParty = false;
                break;
            }

            for (var i = 0; i < CustomPvpModeAI.Instance.MonsterNum; i++)
            {
                if (partyGroup.PartyId != referencePartyId) continue;
                var partySetup = partyGroup.PartySetup[i];
                if (GameDataManager.Instance.LoadedPlayerData.PlayerFarmDict.TryGetValue(partySetup.FarmId, out var farm))
                {
                    if (!GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList.Exists(x => x.MonsterId.Equals(partySetup.MonsterId)))
                    {
                        partyGroup.PartySetup[i] = new SerializePartyGroup.SerializePartySetup(
                            partyGroup.PartySetup[i].Position,
                            partyGroup.PartySetup[i].FarmId, string.Empty);
                        UpdateFarmCard(farm, null, PartyCardSpawned[partySetup.Position]);
                        continue;
                    }

                    if (!string.IsNullOrEmpty(partySetup.MonsterId))
                    {
                        if (!GameDataManager.Instance.LoadedPlayerData.PlayerMonsterDict.TryGetValue(partySetup.MonsterId, out var monster)) continue;
                        if (string.IsNullOrEmpty(monster.MonsterId) || monster.MonsterScriptableData == null) continue;
                        UpdateFarmCard(farm, monster, PartyCardSpawned[partySetup.Position]);
                        continue;
                    }
                    UpdateFarmCard(farm, null, PartyCardSpawned[partySetup.Position]);
                }
                else
                {
                    partyGroup.PartySetup[i] = new SerializePartyGroup.SerializePartySetup(partyGroup.PartySetup[i].Position, string.Empty, string.Empty);
                }

            }

            for (var i = 0; i < PartyCardSpawned.Count; i++)
            {
                if (PartyCardSpawned[i].FarmInfo == null ||
                    string.IsNullOrEmpty(PartyCardSpawned[i].FarmInfo.FarmId) ||
                    string.IsNullOrEmpty(partyGroup.PartySetup[i].FarmId))
                    UpdateFarmCard(null, null, PartyCardSpawned[i]);
            }
        }

        private int CalculateSkillCost(SerializeMonster monsterInfo)
        {
            return monsterInfo.MonsterSkillLevel.Where(skillData => skillData.IsSelected)
                .Sum(skillData => monsterInfo.MonsterSkillDetailDict[skillData.SkillId]
                    .SkillDetail[skillData.SelectLevel - 1].SkillCost);
        }

        private void UpdateFarmCard(SerializeFarm farmInfo, SerializeMonster monsterInfo, UIPartyCard partyCard)
        {
            if (monsterInfo != null)
            {
                displayController.SetMonsterToDisplay(monsterInfo, partyCard.CardNumber);
                displayController.SetMonsterState(monsterInfo.MonsterId, monsterInfo.MonsterInjuryCondition != Enums.InjuryTypesEnum.None || monsterInfo.ComplexStress >= 51 || monsterInfo.ComplexFatigue >= 51, monsterInfo.MonsterDiseasesCondition != Enums.DiseasesTypesEnum.None);
                partyCard.SetFarmInfo(farmInfo, monsterInfo);
                var skillCost = CalculateSkillCost(monsterInfo);

                var skillCostCorrect = CheckSkillCost(_monsterRank, skillCost);
                partyCard.ShowSkillLimitWarning(!skillCostCorrect);
                partyCard.UpdateSkillLColorOutline(!skillCostCorrect);

                readyBtn.interactable = _isCorrectParty;

                switch (_pvpGameMode)
                {
                    case PvpGameMode.FreeBattle:
                        partyCard.HideBattleCount();
                        break;
                    case PvpGameMode.RankedBattle when monsterInfo.RankBattleCount > 30:
                        partyCard.ShowBattleCountError();
                        readyBtn.interactable = false;
                        break;
                    case PvpGameMode.RankedBattle:
                        partyCard.HideBattleCountError();
                        readyBtn.interactable = _isCorrectParty;
                        break;
                    case PvpGameMode.TournamentBattle or PvpGameMode.TournamentMatch:
                        partyCard.HideBattleCount();
                        var countErrors = 0;
                        var conditionmsg = new List<string>();
                        var tournament = TournamentManager.Instance.CurrentTournament;

                        if (tournament.cycle is not null && tournament.cycle.Count > 0)
                        {
                            if (tournament.cycle[0] > monsterInfo.CycleNumber ||
                                tournament.cycle[1] < monsterInfo.CycleNumber)
                            {
                                LocalizedString cycle = "Cycle";
                                countErrors++;
                                conditionmsg.Add(cycle);
                            }
                        }

                        if (tournament.monster_type is not null && tournament.monster_type.Count < 4)
                        {
                            foreach (var allowMonsterType in tournament.monster_type)
                            {
                                switch (allowMonsterType)
                                {
                                    case 0 when monsterInfo.IsFree:
                                    case 1 when monsterInfo.MintType != 10:
                                    case 2 when monsterInfo.MintType == 10:
                                    case 3 when !monsterInfo.IsFree:
                                        continue;
                                    default:
                                        countErrors++;
                                        conditionmsg.Add("Monster type");
                                        break;
                                }
                            }
                        }

                        if (tournament.terrain_ban.Contains((int)monsterInfo.MonsterScriptableData
                                .MonsterMainTerrainComp) ||
                            tournament.terrain_ban.Contains(
                                (int)monsterInfo.MonsterScriptableData.MonsterSubTerrainComp))
                        {
                            LocalizedString terrain = "Terrain";
                            countErrors++;
                            conditionmsg.Add(terrain);
                        }

                        if (tournament.main_seed_ban.Contains((int)monsterInfo.MonsterScriptableData.MonsterMainSeed))
                        {
                            LocalizedString mainSeed = "Main Seed";
                            countErrors++;
                            conditionmsg.Add(mainSeed);
                        }

                        if (tournament.sub_seed_ban.Contains((int)monsterInfo.MonsterScriptableData.MonsterSubSeed))
                        {
                            LocalizedString subSeed = "Sub Seed";
                            countErrors++;
                            conditionmsg.Add(subSeed);
                        }

                        if (countErrors > 0)
                        {
                            Debug.LogError("countErrors: " + conditionmsg.Aggregate(" ", (current, text) => current + text));
                            _isCorrectParty = false;
                            readyBtn.interactable = false;
                            tournamentConditionPartyWarning.OpenMenu();
                        }

                        break;
                }
            }
            else if (farmInfo != null)
            {
                displayController.RemoveFromDisplay(partyCard.CardNumber);
                partyCard.SetFarmInfo(farmInfo);
            }
            else
            {
                displayController.RemoveFromDisplay(partyCard.CardNumber);
                partyCard.SetFarmInfo();
            }

            partyCard.SetMonsterDetailMenu(monsterDetailMenu);
        }

        private bool CheckSkillCost(string rank, int skillCost)
        {
            return rank.ToLower() switch
            {
                "f" => skillCost <= 30,
                "e" => skillCost <= 35,
                "d" => skillCost <= 40,
                "c" => skillCost <= 45,
                "b" => skillCost <= 50,
                "a" => skillCost <= 55,
                "s" => skillCost <= 60,
                _ => false
            };
        }

        private void SelectParty(UIPartyTab tabButton)
        {
            ReferencePartyId = tabButton.PartyId;
            ReloadPartyData();
            tabButton.SetSelected();
            ValidatePartySetting(true);
            foreach (var button in partyFarmTab.AsSpan())
            {
                if (button != tabButton)
                    button.SetDeselected();
            }
        }

        private void ValidatePartySetting(bool showValidationError)
        {
            CustomPvpModeAI.Instance.PartyReferenceId = ReferencePartyId;
            CustomPvpModeAI.Instance.ListMonsterInParty = new List<string>();
            CustomPvpModeAI.Instance.ListMonsterInParty.Clear();
            var listMonsterInParty = new List<SerializeMonster>();
            if (!GameDataManager.Instance.LoadedPlayerData.PlayerPartyGroupDict.TryGetValue(CustomPvpModeAI.Instance.PartyReferenceId, out var partyGroup)) return;
            bool _skillCostValid = true;
            for (var i = 0; i < CustomPvpModeAI.Instance.MonsterNum; i++)
            {
                if (partyGroup.PartyId != ReferencePartyId) continue;
                var set = partyGroup.PartySetup[i];
                if (string.IsNullOrEmpty(set.MonsterId)) continue;
                if (!GameDataManager.Instance.LoadedPlayerData.PlayerMonsterDict.TryGetValue(set.MonsterId, out var monster)) continue;
                if (string.IsNullOrEmpty(monster.MonsterId) || !monster.MonsterScriptableData) continue;
                CustomPvpModeAI.Instance.ListMonsterInParty.Add(monster.MonsterId);
                listMonsterInParty.Add(monster);
                if (_skillCostValid)
                    _skillCostValid = MonsterDataManager.Instance.CheckSkillCost(monster);
            }

            if (listMonsterInParty.Count <= 0)
            {
                _isCorrectParty = false;
            }

            var memoryAllow = true;
            if (_isMemoryAllow == false)
            {
                if (listMonsterInParty.Any(monster => monster is { IsMemory: true }))
                {
                    memoryAllow = false;
                }
            }

            if (!_isCorrectParty)
            {
                readyBtn.interactable = false;
                if (showValidationError)
                    PartyError();
            }

            if (!_skillCostValid && showValidationError)
                MonsterDataManager.Instance.ExceedSkillCostError();
            if (memoryAllow) return;
            _isCorrectParty = false;
            readyBtn.interactable = false;
            if (showValidationError)
                PartyErrorMemory();
        }

        private void PartyError()
        {
            UINotifyManager.AddNotifyResponse("Party Error", () =>
            {
                var popupContent = LocalizationManager.CurrentLanguageCode switch
                {
                    "ja" => "パーティ設定を確認して、もう一度お試しください！",
                    _ => "Please Check Your Party Setting and Try Again!"
                };

                UIPopupNotify.Instance.SetNotify("Party Error", popupContent, () => { readyBtn.interactable = false; });
                UIPopupNotify.Instance.OpenMenu();
            }, UINotifyManager.NotifyType.DefaultNotify);
            UINotifyManager.ProcessNotify();
        }

        private void PartyErrorMemory()
        {
            UINotifyManager.AddNotifyResponse("Party Error", () =>
            {
                var popupContent = LocalizationManager.CurrentLanguageCode switch
                {
                    "ja" => "Monster Memoryは使用できません！パーティ設定を確認して、もう一度お試しください。",
                    _ => "No Monster Memory Allowed! Please Check Your Party Setting and Try Again!"
                };

                UIPopupNotify.Instance.SetNotify("Party Error", popupContent, () => { readyBtn.interactable = false; });
                UIPopupNotify.Instance.OpenMenu();
            }, UINotifyManager.NotifyType.DefaultNotify);
            UINotifyManager.ProcessNotify();
        }

        private void Exit()
        {
            var popupContent = LocalizationManager.CurrentLanguageCode switch
            {
                "ja" => "本当に終了してもよろしいでしょうか？",
                _ => "Are you sure to quit?"
            };
            UIPopupPanel.Instance.Setup("Room Quit", popupContent, OnSurrenderMessage, ExitPopup);
            UIPopupPanel.Instance.OpenMenu();
        }

        private void ExitPopup()
        {
            readyBtn.interactable = true;
            ValidatePartySetting(false);
            UIPopupPanel.Instance.CloseMenu();
        }

        private void OnPartyReadyMessage()
        {
            var currentRoom = CustomNetworkManager.Instance.GetRoom();
            if (currentRoom != null)
            {
                _ = currentRoom.Send("party_ready", new { content = GameDataManager.Instance.LoadedPlayerData.PlayerId });
                Debug.Log("Party Ready: " + GameDataManager.Instance.LoadedPlayerData.PlayerId);
            }
            else
            {
                Debug.LogWarning("Not connected to any room!");
            }
        }

        private void OnSendParty(string monsterId, float randomNumberCorrection)
        {
            var currentRoom = CustomNetworkManager.Instance.GetRoom();
            if (currentRoom != null)
            {
                _ = currentRoom.Send("add_monster", new
                {
                    monsterId,
                    randomNumberCorrection,
                    BackendLoadData.Instance.token,
                    playerId = GameDataManager.Instance.LoadedPlayerData.PlayerId,
                });
            }
            else
            {
                Debug.LogWarning("Not connected to any room!");
            }
        }

        private async void OnSurrenderMessage()
        {
            try
            {
                if (CustomPvpModeAI.Instance.IsPvpModeAI || CustomPvpModeAI.Instance.IsPveMode)
                {
                    CustomPvpModeAI.Instance.ResetData();
                    CloseMenu();
                }
                else
                {
                    if (CustomNetworkManager.Instance.GetRoom() == null) return;
                    if (CustomNetworkManager.Instance.GetRoom() != null)
                    {
                        CustomNetworkManager.Instance.GetRoom().RemoveMessage("countdown");
                        CustomNetworkManager.Instance.GetRoom().RemoveMessage("countdown_finished");
                        CustomNetworkManager.Instance.GetRoom().RemoveMessage("opponent_sur");
                        CustomNetworkManager.Instance.GetRoom().RemoveMessage("both_ready");
                    }

                    var currentRoom = CustomNetworkManager.Instance.GetRoom();
                    if (currentRoom != null)
                    {
                        await currentRoom.Send("player_sur", new { content = GameDataManager.Instance.LoadedPlayerData.PlayerId });
                        OnLeaveRoom();
                        CustomPvpModeAI.Instance.ResetData();
                        CloseMenu();
                    }
                    else
                    {
                        Debug.LogWarning("Not connected to any room!");
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        private async void OnLeaveRoom()
        {
            try
            {
                await CustomNetworkManager.Instance.LeaveRoom();
            }
            catch (Exception e)
            {
                Debug.LogWarning("Can't Leave Room : " + e);
            }
        }

        private void OnOpponentSurrender(string message)
        {
            OnLeaveRoom();
            UIPopupNotify.Instance.SetNotify("You Win", message, CloseMenu);
            UIPopupNotify.Instance.OpenMenu();
        }

        private Tween _countDownTween;

        private void OnCountDown(string countdown)
        {
            if (_countDownTween != null && _countDownTween.IsActive())
            {
                _countDownTween.Kill();
            }

            countDownSlider.value = int.Parse(countdown);
            _countDownTween = countDownSlider.DOValue(0, int.Parse(countdown)).SetEase(Ease.Linear).OnUpdate(() =>
            {
                var currentTime = countDownSlider.value;

                // Calculate minutes and seconds
                var minutes = Mathf.FloorToInt(currentTime / 60);
                var seconds = Mathf.FloorToInt(currentTime % 60);

                // Update the countdown text (format: Remaining... mm:ss)
                LocalizedString remaining = "Remaining...";
                countDownText.text = $"{remaining} {minutes:0}:{seconds:00}";
            });
        }

        private void OnCountdownFinished(string message)
        {
            _countDownTween.Kill();
            readyBtn.interactable = false;
            exitBtn.interactable = false;
            ValidatePartySetting(true);
            if (_isCorrectParty)
            {
                OnPartyReadyMessage();
                waitingNotify.OpenMenu();
            }
            else
            {
                if (_pvpGameMode == PvpGameMode.TournamentBattle)
                {
                    TournamentManager.Instance.SetWinner(CustomNetworkManager.Instance.GetRoom().State.players[0]
                        .walletId).GetAwaiter();
                }
                var popupContent = LocalizationManager.CurrentLanguageCode switch
                {
                    "ja" => "バトルがキャンセルされました。",
                    _ => "\nYou/Opponent failed to set the correct party in time."
                };
                UIPopupNotify.Instance.SetNotify("Match Cancel",
                    popupContent, () =>
                    {
                        OnLeaveRoom();
                        CloseMenu();
                    });
                UIPopupNotify.Instance.OpenMenu();
            }
        }

        private void OnCountdownFinished()
        {
            readyBtn.interactable = false;
            exitBtn.interactable = false;
            ValidatePartySetting(true);
            if (_isCorrectParty)
            {
                LoadBattleScene();
            }
            else
            {
                var popupContent = LocalizationManager.CurrentLanguageCode switch
                {
                    "ja" => "バトルがキャンセルされました。",
                    _ => "\nYou/Opponent failed to set the correct party in time."
                };
                UIPopupNotify.Instance.SetNotify("Match Cancel",
                    popupContent, CloseMenu);
                UIPopupNotify.Instance.OpenMenu();
            }
        }

        public void SetUIBattleRoom(UIFreeBattleRoom uiFreeBattleRoom)
        {
            CustomPvpModeAI.Instance.ResetData();
            UpdateMultiPlayerPartyConfig();
            OpenMenu();
            if (uiFreeBattleRoom)
                _uiFreeBattleRoom = uiFreeBattleRoom;
            if (CustomNetworkManager.Instance.GetRoom() == null) return;
            countDownSlider.gameObject.transform.parent.gameObject.SetActive(true);
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("countdown_finished", OnCountdownFinished);
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("opponent_sur", OnOpponentSurrender);
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("countdown", OnCountDown);
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("both_ready", OnBothReady);
        }
    }
}