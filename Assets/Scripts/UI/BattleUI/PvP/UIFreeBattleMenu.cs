using System;
using System.Collections.Generic;
using Assets.Scripts.Managers;
using Assets.Scripts.UI;
using Assets.Scripts.UI.Utilities;
using Network;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.BattleUI.PvP
{
    public class UIFreeBattleMenu : UIMenuStruct
    {
        [SerializeField] private UIRoomPrefab playerRoomPrefab;
        [SerializeField] private Transform roomContainer;
        [SerializeField] private Button refreshBtn;
        [SerializeField] private Button joinRoomByIDBtn;
        [SerializeField] private Button createRoomBtn;

        [SerializeField] private UIFreeBattleRoom roomDetailMenu;
        [SerializeField] private UICreateBattleRoomMenu createRoomMenu;
        [SerializeField] private TMP_InputField roomIdToSearch;

        public override void Start()
        {
            CustomNetworkManager.onRoomsReceived += HandleRoom;
            createRoomBtn.onClick.AddListener(() =>
            {
                createRoomMenu.OpenMenu();
            });
            refreshBtn.onClick.AddListener(GetAvailableRooms);
            joinRoomByIDBtn.onClick.AddListener(() => JoinRoom(roomIdToSearch.text));
        }

        public override void OpenMenu()
        {
            base.OpenMenu();
            GetAvailableRooms();
            var currentRoom = CustomNetworkManager.Instance.GetCurrentRoom();
            if (currentRoom != null)
            {
                OpenRoomDetailMenu();
            }
            else
            {
                if (CustomPvpModeAI.Instance.IsBattleMenuActive && CustomPvpModeAI.Instance.PreviousPvpGameMode == PvpGameMode.FreeBattle)
                {
                    createRoomMenu.OpenMenu();
                }
            }
        }

        public void OnDestroy()
        {
            CustomNetworkManager.onRoomsReceived -= HandleRoom;
        }

        private void RefreshRoom()
        {
            roomContainer.DestroyChildrenHelper();
        }

        // Should Limit the number of room spawnm
        private void HandleRoom(RoomAvailable[] rooms)
        {
            if (rooms.Length <= 0) return;
            RefreshRoom();
            for (int i = 0; i < rooms.Length && i < 8; i++)
            {
                var room = rooms[i];
                if (!string.IsNullOrEmpty(room.password)) continue;
                var newRoom = Instantiate(playerRoomPrefab, roomContainer);
                if (newRoom.TryGetComponent(out UIRoomPrefab roomPrefab))
                {
                    roomPrefab.isInBattleIndicator.gameObject.SetActive(room.metadata.isInBattle);
                    roomPrefab.firstPlayerName.GetComponent<TextMeshProUGUI>().text = room.metadata.firstPlayer;
                    roomPrefab.secondPlayerName.GetComponent<TextMeshProUGUI>().text = room.metadata.secondPlayer;
                    roomPrefab.roomName.GetComponent<TextMeshProUGUI>().text = room.metadata.roomName;
                    roomPrefab.transform.name = room.roomId;
                    roomPrefab.GetComponent<Button>().onClick.AddListener(() => JoinRoom(room.roomId));
                }
                newRoom.gameObject.SetActive(true);
            }
        }

        private async void JoinRoom(string selectedRoomID)
        {
            try
            {
                var loading = BackendLoadData.Instance.LoadingCanvas("JoinRoom");
                var options = new Dictionary<string, object>
                {
                    { "playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId },
                    { "token", BackendLoadData.Instance.token },
                    { "walletId", GameDataManager.Instance.LoadedPlayerData.PlayerWallet }
                };
                var roomConnect = await CustomNetworkManager.Instance.ConnectToRoomAsync(selectedRoomID, options);
                OnRoomConnectionComplete(roomConnect.Item1, roomConnect.Item2, roomConnect.Item3);
                Destroy(loading);
            }
            catch (Exception)
            {
                RefreshRoom();
                Debug.LogError("Can't Join Room :" + selectedRoomID);
            }
        }

        private async void JoinRoomWithPassword(string selectedRoomID, string password)
        {
            try
            {
                var options = new Dictionary<string, object>
                {
                    { "playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId },
                    { "password", password },
                    { "token", BackendLoadData.Instance.token },
                    { "walletId", GameDataManager.Instance.LoadedPlayerData.PlayerWallet }
                };
                PlayerPrefs.SetString("roomPassword", password);
                var roomConnect = await CustomNetworkManager.Instance.ConnectToRoomAsync(selectedRoomID, options);
                OnRoomConnectionComplete(roomConnect.Item1, roomConnect.Item2, roomConnect.Item3);
            }
            catch (Exception)
            {
                RefreshRoom();
                Debug.LogError("Can't Join Room :" + selectedRoomID);
            }
        }

        private void OnRoomConnectionComplete(bool success, string errorMessage, string roomId)
        {
            if (success)
            {
                CustomNetworkManager.onGameStateChange += OpenRoomDetailMenu;
            }
            else
            {
                if (errorMessage.Contains("incorrect"))
                {
                    errorMessage = "The password you entered is incorrect.";
                    UIPasswordNotify.Instance.SetupNotifyPassword("Room Found", errorMessage, () =>
                    {
                        var password = UIPasswordNotify.Instance.GetPasswordInputField();
                        JoinRoomWithPassword(roomId, password);
                    });
                }
                else if (errorMessage.Contains("Enter Room Password"))
                {
                    errorMessage = "Please enter Room password";
                    UIPasswordNotify.Instance.SetupNotifyPassword("Room Found", errorMessage, () =>
                    {
                        var password = UIPasswordNotify.Instance.GetPasswordInputField();
                        JoinRoomWithPassword(roomId, password);
                    });
                }
                RefreshRoom();
            }
        }
        
        private void OpenRoomDetailMenu()
        {
            CustomNetworkManager.onGameStateChange -= OpenRoomDetailMenu;
            roomDetailMenu.OpenRoomDetailMenu(CustomNetworkManager.Instance.GetRoom());
        }

        public void GetAvailableRooms()
        {
            RefreshRoom();
            CustomNetworkManager.Instance.GetAvailableRooms();
        }
    }
}