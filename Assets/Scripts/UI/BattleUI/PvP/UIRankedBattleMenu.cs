using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Assets.Scripts.Controllers;
using Assets.Scripts.Managers;
using Assets.Scripts.UI;
using Assets.Scripts.UI.Utilities;
using Cysharp.Threading.Tasks;
using Network;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.BattleUI.PvP
{
    public class UIRankedBattleMenu : UIMenuStruct
    {
        // Rank and Score Data
        [SerializeField] private TextMeshProUGUI cycleText;
        [SerializeField] private TextMeshProUGUI cycleScoreText;
        [SerializeField] private TextMeshProUGUI weeklyScoreText;
        [SerializeField] private TextMeshProUGUI dailyScoreText;

        // Total Records
        [SerializeField] private TextMeshProUGUI totalWinsText;
        [SerializeField] private TextMeshProUGUI totalLossesText;
        [SerializeField] private TextMeshProUGUI totalWinRateText;

        // Current Cycle Records
        [SerializeField] private TextMeshProUG<PERSON> currentCycleWinsText;
        [SerializeField] private TextMeshP<PERSON><PERSON>G<PERSON> currentCycleLossesText;
        [SerializeField] private TextMeshProUGUI currentCycleWinRateText;

        // Previous Cycle Records
        [SerializeField] private TextMeshProUGUI previousCycleWinsText;
        [SerializeField] private TextMeshProUGUI previousCycleLossesText;
        [SerializeField] private TextMeshProUGUI previousCycleWinRateText;

        // Recent Results
        [SerializeField] private UIRecentResults[] recentResultsTexts;

        // Date and Period Info
        [SerializeField] private TextMeshProUGUI currentPeriodText;
        [SerializeField] private TextMeshProUGUI currentDateText;

        [SerializeField] private ToggleGroup rankGroup;

        [SerializeField] private Toggle[] rankToggle;

        [SerializeField] private Button matchingButton;

        [SerializeField] private UIMonsterPartySettingMultiplayer partySettingMenu;

        private PvpGameMode _pvpGameMode;

        private void OnEnable()
        {
            UIMatchingNotify.Instance.cancelBtn.onClick.RemoveAllListeners();
            UIMatchingNotify.Instance.cancelBtn.onClick.AddListener(() =>
            {
                StopAllCoroutines();
                UIMatchingNotify.Instance.CloseMenu();
                LeaveRoom();
                UnlockRankMatchRanks();
                matchingButton.interactable = true;
            });
            foreach (var toggle in rankToggle)
            {
                toggle.interactable = false;
                toggle.transform.Find("Image").GetChild(0).gameObject.SetActive(true);
                toggle.isOn = false;
            }
            CheckMatchingButton();
            UnlockRankMatchRanks();
            currentPeriodText.text = CycleManager.Instance.CurrentCycle.CycleName;
            var formattedDate = CycleManager.Instance.CurrentCycle.CycleEndTime.ToString("MMM. dd, yyyy", CultureInfo.InvariantCulture);
            currentDateText.text = formattedDate;
            // ScoreController.GetUserScore(GameDataManager.Instance.LoadedPlayerData.PlayerWallet);
            var rankingSnapshot = ScoreManager.Instance.ScoreSnapshot.FirstOrDefault(x => x.ScoreType == ScoreController.PERSONAL_PVP_SNAPSHOT && x.ScoreUser == GameDataManager.Instance.LoadedPlayerData.PlayerWallet && x.FromDate == CycleManager.Instance.CurrentCycle.CycleStartTime && x.ToDate == CycleManager.Instance.CurrentCycle.CycleEndTime);
            if (rankingSnapshot != null)
            {
                cycleText.text = rankingSnapshot.Ranking.ToString();
                cycleScoreText.text = $"{rankingSnapshot.ScoreValue:N0}";
            }
            else
            {
                cycleText.text = "-";
                cycleScoreText.text = "-";
            }
            var weekRange = Helpers.GetCurrentWeekRange(DateTime.UtcNow);
            var weeklySnapshot = ScoreManager.Instance.ScoreSnapshot.FirstOrDefault(x => x.ScoreType == ScoreController.WEEKLY_SNAPSHOT && x.ScoreUser == GameDataManager.Instance.LoadedPlayerData.PlayerWallet && x.FromDate == weekRange.Item1 && x.ToDate == weekRange.Item2);
            weeklyScoreText.text = weeklySnapshot != null ? $"{weeklySnapshot.RankedScore:N0}" : "-";
            var dailyScore = ScoreController.GetDailyScore(GameDataManager.Instance.LoadedPlayerData.PlayerWallet, 1, DateTime.UtcNow.Date);
            dailyScoreText.text = $"{dailyScore:N0}" + "/150";
            DisplayMatchHistory();
            UpdateUserRecords();
        }
        public override void CloseMenu()
        {
            base.CloseMenu();
            StopAllCoroutines();
        }
        private void CheckMatchingButton()
        {
            var isAnyToggleOn = rankToggle.Any(toggle => toggle.isOn);
            matchingButton.interactable = isAnyToggleOn;
        }

        private IEnumerator CalculateUptime()
        {
            UIMatchingNotify.Instance.OpenMenu();
            UIMatchingNotify.Instance.SetupSliderValue(30);
            yield return new WaitForSeconds(30f);
            LeaveRoom();
            UIMatchingNotify.Instance.CloseMenu();
            UpdateMultiPlayerPartyConfig();
            CloseMenu();

        }
        private void UpdateMultiPlayerPartyConfig()
        {
            StopAllCoroutines();
            partySettingMenu.SetPvPMode(PvpGameMode.RankedBattle, 3, GetActiveToggle(rankGroup).GetComponent<UIToggleValue>().value);
        }

        private void Awake()
        {
            foreach (var toggle in rankToggle)
            {
                toggle.onValueChanged.AddListener(_ =>
                {
                    CheckMatchingButton();
                });
            }
            matchingButton.onClick.AddListener(() =>
            {
                foreach (var toggle in rankToggle)
                {
                    toggle.interactable = false;
                }

                matchingButton.interactable = false;
                HandleRankedBattleMenu().Forget();
            });
        }

        private async UniTask HandleRankedBattleMenu()
        {
            try
            {
                var availableRankedRoom = await CustomNetworkManager.Instance.GetAvailableRankedRoom();
                if (availableRankedRoom.Count > 0)
                {
                    foreach (var room in from room in availableRankedRoom where room.metadata.monsterRank == GetActiveToggle(rankGroup).GetComponent<UIToggleValue>().value where room.metadata.sectorId != CustomNetworkManager.Instance.GetSectorId() where room.metadata.guildId != CustomNetworkManager.Instance.GetGuildId() || room.metadata.guildId == "" && CustomNetworkManager.Instance.GetGuildId() == "" select room)
                    {
                        var roomData = await JoinRankedRoomByID(room.roomId);
                        if (!roomData.Item1)
                        {
                            await CreateRankedBattleRoom();
                        }
                        return;
                    }
                }
                await CreateRankedBattleRoom();
            }
            catch (Exception ex)
            {
                Debug.LogWarning("Error fetching available ranked rooms: " + ex.Message);
                StartCoroutine(CalculateUptime());
            }
        }

        private async UniTask<(bool, string, string)> JoinRankedRoomByID(string selectedRoomID)
        {
            var loading = BackendLoadData.Instance.LoadingCanvas("JoinRankedRoomByID");
            var options = new Dictionary<string, object> {
                { "playerName", GetPlayerName().Trim()},
                { "playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId },
                { "token", BackendLoadData.Instance.token },
                { "walletId", GameDataManager.Instance.LoadedPlayerData.PlayerWallet}
            };
            var room = await CustomNetworkManager.Instance.ConnectToRoomAsync(selectedRoomID, options);
            Destroy(loading);
            var currentRoom = CustomNetworkManager.Instance.GetCurrentRoom();
            if (currentRoom != null)
            {
                currentRoom.OnMessage<string>("start_partysetting", OnPartySetting);
            }
            return room;
        }

        private void UnlockRankMatchRanks()
        {
            var playerRank = (int)GameProgressManager.Instance.GameProgress.PlayerRank;
            for (var i = 0; i <= Math.Min(playerRank, rankToggle.Length - 1); i++)
            {
                rankToggle[i].interactable = true;
                rankToggle[i].transform.Find("Image").GetChild(0).gameObject.SetActive(false);
            }
        }

        private async void LeaveRoom()
        {
            var loading = BackendLoadData.Instance.LoadingCanvas("LeaveRoom");
            await CustomNetworkManager.Instance.LeaveRoom();
            Destroy(loading);
        }


        private async UniTask CreateRankedBattleRoom()
        {
            try
            {
                LeaveRoom();
                var loading = BackendLoadData.Instance.LoadingCanvas("CreateRankedBattleRoom");
                var metadata = new Dictionary<string, object>
                {
                    ["firstPlayer"] = GetPlayerName().Trim(),
                    ["audience"] = false,
                    ["opponent"] = true,
                    ["monsterRank"] = GetActiveToggle(rankGroup).GetComponent<UIToggleValue>().value,
                    ["monsterNum"] = "3",
                    ["turnNum"] = "20",
                    ["specialHex"] = "Random",
                    ["memoryAllow"] = false,
                    ["hostId"] = GameDataManager.Instance.LoadedPlayerData.PlayerId,
                    ["guildId"] = CustomNetworkManager.Instance.GetGuildId(),
                    ["sectorId"] = CustomNetworkManager.Instance.GetSectorId()

                };
                var roomOptions = new Dictionary<string, object>
                {
                    ["roomName"] = "ranked_battle",
                    ["password"] = null,
                    ["private"] = false,
                    ["metadata"] = metadata
                };

                await CustomNetworkManager.Instance.CreateRankedBattleRoom(roomOptions);
                CustomNetworkManager.Instance.MonsterRank = GetActiveToggle(rankGroup).GetComponent<UIToggleValue>().value;
                CustomNetworkManager.Instance.MonsterNum = 3;

                Destroy(loading);
                CustomNetworkManager.Instance.GetRoom().OnMessage<string>("start_partysetting", OnPartySetting);
                StartCoroutine(CalculateUptime());
            }
            catch (Exception e)
            {
                Debug.LogError(e.Message);
            }
        }

        private void OnPartySetting(string obj)
        {

            StopAllCoroutines();
            UIMatchingNotify.Instance.CloseMenu();
            CloseMenu();
            partySettingMenu.SetUIBattleRoom(null);
        }

        private string GetPlayerName()
        {
            var playerName = GameDataManager.Instance.LoadedPlayerData.PlayerName.Trim();
            return playerName.Length > 12 ? playerName.Substring(0, 12) : playerName;
        }

        private Toggle GetActiveToggle(ToggleGroup toggleGroup)
        {
            return toggleGroup.ActiveToggles().FirstOrDefault();
        }

        private void DisplayMatchHistory()
        {
            var historyCount = CustomNetworkManager.Instance.GetRankedMatchHistoryByBattleType(1).Count;
            var recentResultsCount = recentResultsTexts.Length;
            var count = Math.Min(historyCount, Math.Min(recentResultsCount, 6));
            var histories = CustomNetworkManager.Instance.GetRankedMatchHistoryByBattleType(1);
            histories.Reverse();

            for (var i = 0; i < count; i++)
            {
                if (histories[i] != null)
                {
                    recentResultsTexts[i].SetMatchHistory(histories[i], i + 1);
                }
                else
                {
                    recentResultsTexts[i].gameObject.SetActive(false);
                }
            }
            for (var i = count; i < Math.Min(recentResultsCount, 6); i++)
            {
                recentResultsTexts[i].gameObject.SetActive(false);
            }
        }
        /// update userrRecords
        private void UpdateUserRecords()
        {
            totalWinsText.text = CustomNetworkManager.Instance.GetTotalWin().ToString();
            totalLossesText.text = CustomNetworkManager.Instance.GetTotalLose().ToString();
            var totalWinRateValue = CustomNetworkManager.Instance.GetTotalWinRate();
            totalWinRateText.text = $"({totalWinRateValue:F0}%)";

            var currentCycleWins = CustomNetworkManager.Instance.GetTotalWinBetweenDates(CycleManager.Instance.CurrentCycle.CycleStartTime, CycleManager.Instance.CurrentCycle.CycleEndTime);
            var currentCycleLosses = CustomNetworkManager.Instance.GetTotalLossBetweenDates(CycleManager.Instance.CurrentCycle.CycleStartTime, CycleManager.Instance.CurrentCycle.CycleEndTime);
            currentCycleWinsText.text = currentCycleWins.ToString();
            currentCycleLossesText.text = currentCycleLosses.ToString();
            var currentCycleWinRateValue = CustomNetworkManager.Instance.GetWinRate(currentCycleWins, currentCycleLosses);
            currentCycleWinRateText.text = $"({currentCycleWinRateValue:F0}%)";

            // 3 months from the current cycle start time
            var previousCycleStartTime = CycleManager.Instance.CurrentCycle.CycleStartTime.AddMonths(-3);
            var previousCycleWins = CustomNetworkManager.Instance.GetTotalWinBetweenDates(previousCycleStartTime, CycleManager.Instance.CurrentCycle.CycleStartTime);
            var previousCycleLosses = CustomNetworkManager.Instance.GetTotalLossBetweenDates(previousCycleStartTime, CycleManager.Instance.CurrentCycle.CycleStartTime);
            previousCycleWinsText.text = previousCycleWins.ToString();
            previousCycleLossesText.text = previousCycleLosses.ToString();
            var previousCycleWinRateValue = CustomNetworkManager.Instance.GetWinRate(previousCycleWins, previousCycleLosses);
            previousCycleWinRateText.text = $"({previousCycleWinRateValue:F0}%)";
        }

    }
}