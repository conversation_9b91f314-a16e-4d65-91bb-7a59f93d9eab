using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Assets.Scripts.Controllers;
using Assets.Scripts.Enums;
using Assets.Scripts.Managers;
using Assets.Scripts.SerializeDataStruct.ItemData;
using Assets.Scripts.UI;
using Assets.Scripts.UI.Utilities;
using Cysharp.Threading.Tasks;
using Network;
using TMPro;
using UI.BattleUI.ItemReward;
using UI.Utilities;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.BattleUI.PvP
{
    public class UIPrizedBattleMenu : UIMenuStruct
    {
        // Rank and Score Data
        [SerializeField] private TextMeshProUGUI cycleText;
        [SerializeField] private TextMeshProUGUI cycleScoreText;
        [SerializeField] private TextMeshProUGUI weeklyScoreText;
        [SerializeField] private TextMeshProUGUI dailyScoreText;

        // Date and Period Info
        [SerializeField] private TextMeshProUGUI currentPeriodText;
        [SerializeField] private TextMeshProUGUI currentDateText;

        [SerializeField] private TextMeshProUG<PERSON> ticketCount;
        [SerializeField] private TextMeshProUGUI prizedBattleStatus;
        [SerializeField] private TextMeshProUGUI currentPrizeBit;
        [SerializeField] private GameObject[] currentPrizeList;

        [SerializeField] private TextMeshProUGUI bonusValue;
        [SerializeField] private ToggleGroup rankGroup;

        [SerializeField] private Toggle[] rankToggle;

        [SerializeField] private Button matchingButton;
        [SerializeField] private Button startPrizeBattleButton;
        [SerializeField] private Button claimButton;

        [SerializeField] private UIMonsterPartySettingMultiplayer partySettingMenu;

        [SerializeField] private GameObject prePrizeBattle;
        [SerializeField] private GameObject onPrizeBattle;

        [SerializeField] private Slider currentProgressSlider;

        private bool _usedTicket;

        public override void OpenMenu()
        {
            foreach (var toggle in rankToggle)
            {
                toggle.isOn = false;
            }

            UIMatchingNotify.Instance.cancelBtn.onClick.RemoveAllListeners();

            UIMatchingNotify.Instance.cancelBtn.onClick.AddListener(() =>
            {
                StopAllCoroutines();
                UIMatchingNotify.Instance.CloseMenu();
                LeaveRoom();
                SetActiveToggleButton();
                matchingButton.interactable = true;
            });

            currentPeriodText.text = CycleManager.Instance.CurrentCycle.CycleName;
            var formattedDate =
                CycleManager.Instance.CurrentCycle.CycleEndTime.ToString("MMM. dd, yyyy", CultureInfo.InvariantCulture);
            currentDateText.text = formattedDate;
            // ScoreController.GetUserScore(GameDataManager.Instance.LoadedPlayerData.PlayerWallet);
            var pvpSnapshot = ScoreManager.Instance.ScoreSnapshot.FirstOrDefault(x =>
                x.ScoreType == ScoreController.PERSONAL_PVP_SNAPSHOT &&
                x.FromDate == CycleManager.Instance.CurrentCycle.CycleStartTime &&
                x.ToDate == CycleManager.Instance.CurrentCycle.CycleEndTime &&
                x.ScoreUser == GameDataManager.Instance.LoadedPlayerData.PlayerWallet);
            if (pvpSnapshot != null)
            {
                cycleText.text = pvpSnapshot.Ranking.ToString();
                cycleScoreText.text = $"{pvpSnapshot.ScoreValue:N0}";
            }
            else
            {
                cycleText.text = "-";
                cycleScoreText.text = "-";
            }

            var weekRange = Helpers.GetCurrentWeekRange(DateTime.UtcNow);
            var weeklySnapshot = ScoreManager.Instance.ScoreSnapshot.FirstOrDefault(x =>
                x.ScoreType == ScoreController.WEEKLY_SNAPSHOT && x.FromDate == weekRange.Item1 &&
                x.ToDate == weekRange.Item2 && x.ScoreUser == GameDataManager.Instance.LoadedPlayerData.PlayerWallet);
            weeklyScoreText.text = weeklySnapshot != null ? weeklySnapshot.ScoreValue.ToString("N0") : "-";
            var dailyScore = ScoreController.GetDailyScore(GameDataManager.Instance.LoadedPlayerData.PlayerWallet, 1,
                DateTime.UtcNow.Date);
            dailyScoreText.text =$"{dailyScore:N0}" + "/150";
            TournamentTicketCount();
            GetCurrentProgress();
            base.OpenMenu();
        }

        private void TournamentTicketCount()
        {
            var totalTicketLeft = 0;
            foreach (var item in GameDataManager.Instance.LoadedPlayerData.PlayerItemList.AsSpan())
            {
                switch (item.Item.ItemId)
                {
                    case "TOURNAMENT_TICKET_UC":
                    case "TOURNAMENT_TICKET_B":
                        totalTicketLeft += item.Quantity;
                        continue;
                }
            }

            ticketCount.text = $"{totalTicketLeft}";
        }

        private void SetActiveToggleButton()
        {
            UnlockRankMatchRanks();
            CheckMatchingButton();
        }

        private void GetCurrentProgress()
        {
            var prizeRankedBattle = CustomNetworkManager.Instance.GetRankedMatchHistoryByBattleType(2);
            prizeRankedBattle.Reverse();
            if (prizeRankedBattle.Count > 0)
            {
                if (prizeRankedBattle[0].winner_user == "claimed_reward")
                {
                    prePrizeBattle.SetActive(true);
                    onPrizeBattle.SetActive(false);
                    SetActiveToggleButton();
                    claimButton.gameObject.SetActive(false);
                }
                else
                {
                    _usedTicket = true;
                    onPrizeBattle.SetActive(true);
                    prePrizeBattle.SetActive(false);
                    prizedBattleStatus.text = "Open";
                    UpdatePrizeInfo(CustomNetworkManager.Instance.GetPrizePreviousWinCount());
                    
                    if (CustomNetworkManager.Instance.GetPrizePreviousWinCount() == 3)
                    {
                        prizedBattleStatus.text = "Close";
                        SetActiveToggleButton();
                        _usedTicket = false;
                        claimButton.gameObject.SetActive(true);
                    }

                    SetActiveToggleButton();
                    var playerWallet = prizeRankedBattle[0].user_wallet_a == GameDataManager.Instance.LoadedPlayerData.PlayerWallet ? prizeRankedBattle[0].user_wallet_a : prizeRankedBattle[0].user_wallet_b;
                    if (prizeRankedBattle[0].winner_user == playerWallet ||
                        prizeRankedBattle[0].winner_user == "used_ticket") return;
                    prizedBattleStatus.text = "Close";
                    UpdatePrizeInfo(CustomNetworkManager.Instance.GetPrizePreviousWinCount());
                    SetActiveToggleButton();
                    claimButton.gameObject.SetActive(true);
                }

                _usedTicket = false;
            }
            else
            {
                prePrizeBattle.SetActive(true);
                onPrizeBattle.SetActive(false);
                SetActiveToggleButton();
                _usedTicket = false;
                claimButton.gameObject.SetActive(false);
            }
        }

        public override void CloseMenu()
        {
            StopAllCoroutines();
            base.CloseMenu();
        }

        private IEnumerator CalculateUptime()
        {
            UIMatchingNotify.Instance.OpenMenu();
            UIMatchingNotify.Instance.SetupSliderValue(30);
            yield return new WaitForSeconds(30f);
            LeaveRoom();
            UIMatchingNotify.Instance.CloseMenu();
            UpdateMultiPlayerPartyConfig();
            CloseMenu();
        }

        private void UpdateMultiPlayerPartyConfig()
        {
            StopAllCoroutines();
            partySettingMenu.SetPvPMode(PvpGameMode.PrizedBattle, 3,
                GetActiveToggle(rankGroup).GetComponent<UIToggleValue>().value);
        }

        private void CheckMatchingButton()
        {
            var isAnyToggleOn = rankToggle.Any(toggle => toggle.isOn);
            matchingButton.interactable = isAnyToggleOn && _usedTicket;
        }

        private void Awake()
        {
            foreach (var toggle in rankToggle)
            {
                toggle.onValueChanged.AddListener((_) => CheckMatchingButton());
            }

            matchingButton.onClick.AddListener(() =>
            {
                foreach (var toggle in rankToggle)
                {
                    toggle.interactable = false;
                }

                matchingButton.interactable = false;
                HandlePrizeBattle().Forget();
            });

            startPrizeBattleButton.onClick.AddListener(OnStartPrizeBattleButtonClicked);
            claimButton.onClick.AddListener(ClaimReward);
        }

        private void OnStartPrizeBattleButtonClicked()
        {
            var ticketType = GetAvailableTicketType();
            if (ticketType != null)
            {
                ShowConfirmationPopup(ticketType);
            }
            else
            {
                NoTicket();
            }
        }

        private string GetAvailableTicketType()
        {
            if (CheckTicket("TOURNAMENT_TICKET_B")) return "TOURNAMENT_TICKET_B";
            return CheckTicket("TOURNAMENT_TICKET_UC") ? "TOURNAMENT_TICKET_UC" : null;
        }

        private bool CheckTicket(string ticketId)
        {
            var ticket = GameDataManager.Instance.LoadedPlayerData.PlayerItemList.Find(x => x.Item.ItemId == ticketId);
            return ticket is { Quantity: > 0 };
        }

        private void ShowConfirmationPopup(string ticketType)
        {
            var popupContent = I2.Loc.LocalizationManager.CurrentLanguageCode switch
            {
                "ja" => "大会チケットを消費して、プライズランクマッチを開始してもよろしいですか？",
                _ => "Are you sure to start a Prized Ranked Match by consuming one Tournament Ticket?"
            };

            Action confirmAction = ticketType == "TOURNAMENT_TICKET_B" ? ConsumedTicketB : ConsumedTicketUc;
            UIPopupPanel.Instance.Setup("Prize Ranked Match", popupContent, confirmAction,
                UIPopupPanel.Instance.CloseMenu);
            UIPopupPanel.Instance.OpenMenu();
        }

        private async void ConsumedTicketB()
        {
            await ConsumeTicketAndStartBattle("TOURNAMENT_TICKET_B");
        }

        private async void ConsumedTicketUc()
        {
            await ConsumeTicketAndStartBattle("TOURNAMENT_TICKET_UC");
        }

        private async Task ConsumeTicketAndStartBattle(string ticketId)
        {
            try
            {
                var ticket = GameDataManager.Instance.LoadedPlayerData.PlayerItemList.Find(x => x.Item.ItemId == ticketId);
                if (ticket is { Quantity: > 0 })
                {
                    await OpenNewBattle(ticket);
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning(e);
            }
        }

        private async Task OpenNewBattle(SerializeItem ticket)
        {
            GameDataController.AddItemBalance(new List<SerializeItem> { ticket }, new List<int> { -1 }, "prized_battle");
            ticket.AddQuantity(-1);
            TournamentTicketCount();
            await CustomNetworkManager.Instance.PostRankedMatchHistory(
                GameDataManager.Instance.LoadedPlayerData.PlayerWallet, 0, new List<string> { "", "", "" },
                0, 0, "",
                2, new List<string> { "", "", "" }, 0, 0, 2, "S", "used_ticket");
            await CustomNetworkManager.Instance.GetRankedMatchHistory();
            GetCurrentProgress();
        }

        private void NoTicket()
        {
            var popupContent = I2.Loc.LocalizationManager.CurrentLanguageCode switch
            {
                "ja" => "大会チケットがありません。",
                _ => "You don't have any Tournament ticket."
            };

            UIPopupPanel.Instance.SetupConfirm("Prize Ranked Match", popupContent, UIPopupPanel.Instance.CloseMenu);
            UIPopupPanel.Instance.OpenMenu();
        }
        private void UpdateBonusValue(int winStreak)
        {
            var bonusV = Mathf.Pow(1.2f, winStreak);

            if (winStreak <= 0)
            {
                bonusValue.text = "0%";
            }
            else
            {
                var bonusPercentage = (int)((bonusV - 1) * 100);
                bonusValue.text = bonusPercentage + "%";
            }
        }

        private void ClaimReward()
        {
            OnClaimReward();
        }

        private SerializeItem GetRandomItemC()
        {
            var itemListC = ItemDataManager.Instance.ItemDict.Values.Where(x =>
                    (x.RemainAmount > 0 && x.HasSupplyLimit || !x.HasSupplyLimit) &&
                    x.ItemRarity == ItemsRarityEnum.C)
                .ToList();
            var item = itemListC[UnityEngine.Random.Range(0, itemListC.Count)];
            ItemDataManager.Instance.ItemDict.TryGetValue(item.ItemId, out var itemData);
            uIItemRewardMenu.SetItem(itemData, 1);
            SerializeItem newItem = new(itemData, 1);
            return newItem;
        }

        private SerializeItem GetRandomItemUc()
        {
            var itemListUc = ItemDataManager.Instance.ItemDict.Values.Where(x =>
                    (x.RemainAmount > 0 && x.HasSupplyLimit || !x.HasSupplyLimit) &&
                    x.ItemRarity == ItemsRarityEnum.UC)
                .ToList();
            var item = itemListUc[UnityEngine.Random.Range(0, itemListUc.Count)];
            ItemDataManager.Instance.ItemDict.TryGetValue(item.ItemId, out var itemData);
            uIItemRewardMenu.SetItem(itemData, 1);
            SerializeItem newItem = new(itemData, 1);
            return newItem;
        }

        private SerializeItem GetRandomItemR()
        {
            var itemListR = ItemDataManager.Instance.ItemDict.Values.Where(x =>
                    (x.RemainAmount > 0 && x.HasSupplyLimit || !x.HasSupplyLimit) &&
                    x.ItemRarity == ItemsRarityEnum.R)
                .ToList();
            var item = itemListR[UnityEngine.Random.Range(0, itemListR.Count)];
            ItemDataManager.Instance.ItemDict.TryGetValue(item.ItemId, out var itemData);
            SerializeItem newItem = new(itemData, 1);
            uIItemRewardMenu.SetItem(itemData, 1);
            return newItem;
        }

        private SerializeItem AddHashFragment()
        {
            ItemDataManager.Instance.ItemDict.TryGetValue("HASH_FRAGMENT_UC", out var itemData);
            SerializeItem newItem = new(itemData, 1);
            uIItemRewardMenu.SetItem(itemData, 1);
            return newItem;
        }

        [SerializeField] private UIItemRewardMenu uIItemRewardMenu;

        private async void OnClaimReward()
        {
            var loading = BackendLoadData.Instance.LoadingCanvas("OnClaimReward");
            var value = CustomNetworkManager.Instance.GetPrizePreviousWinCount();
            var bitRewards = 0;
            string itemSource = "prized_battle";
            switch (value)
            {
                case 0:
                    bitRewards = 500;
                    break;
                case 1:
                    bitRewards = 5000;
                    var rewardItem = GetRandomItemC();
                    GameDataController.AddItemBalance(new List<SerializeItem> { rewardItem }, new List<int> { 1 }, itemSource);
                    break;
                case 2:
                    {
                        bitRewards = 10000;
                        List<SerializeItem> rewardItems = new List<SerializeItem> { GetRandomItemC(), GetRandomItemUc(), AddHashFragment() };
                        GameDataController.AddItemBalance(rewardItems, new List<int> { 1, 1, 1 }, itemSource);
                    }
                    break;
                case 3:
                    {
                        bitRewards = 15000;
                        List<SerializeItem> rewardItems = new List<SerializeItem> { GetRandomItemC(), GetRandomItemUc(), GetRandomItemR(), AddHashFragment() };
                        for (int i = 0; i < rewardItems.Count; i++)
                            GameDataController.AddItemBalance(rewardItems, new List<int> { 1, 1, 1, 1 }, itemSource);
                    }
                    break;
            }

            GameDataController.AddBitBalance(bitRewards, "prized_battle", "prize battlle reward");
            uIItemRewardMenu.SetBit(bitRewards);
            uIItemRewardMenu.OpenMenu();

            await CustomNetworkManager.Instance.PostRankedMatchHistory(
                GameDataManager.Instance.LoadedPlayerData.PlayerWallet, 0, new List<string> { "", "", "" },
                0, 0, "",
                2, new List<string> { "", "", "" }, 0, 0, 2, "S", "claimed_reward");
            await CustomNetworkManager.Instance.GetRankedMatchHistory();
            Destroy(loading);
            GetCurrentProgress();
        }

        private async void LeaveRoom()
        {
            var loading = BackendLoadData.Instance.LoadingCanvas("LeaveRoom");
            await CustomNetworkManager.Instance.LeaveRoom();
            Destroy(loading);
        }

        private async UniTask CreatePrizedBattleRoom()
        {
            matchingButton.interactable = false;
            var loading = BackendLoadData.Instance.LoadingCanvas("CreatePrizedBattleRoom");
            var battleRank = GetActiveToggle(rankGroup).GetComponent<UIToggleValue>().value;
            var metadata = new Dictionary<string, object>
            {
                ["firstPlayer"] = GetPlayerName().Trim(),
                ["audience"] = false,
                ["opponent"] = true,
                ["monsterRank"] = battleRank,
                ["monsterNum"] = "3",
                ["turnNum"] = "20",
                ["specialHex"] = "Random",
                ["memoryAllow"] = false,
                ["hostId"] = GameDataManager.Instance.LoadedPlayerData.PlayerId,
                ["guildId"] = CustomNetworkManager.Instance.GetGuildId(),
                ["sectorId"] = CustomNetworkManager.Instance.GetSectorId(),

            };
            var roomOptions = new Dictionary<string, object>
            {
                ["roomName"] = "prized_battle",
                ["password"] = null,
                ["private"] = false,
                ["metadata"] = metadata
            };

            await CustomNetworkManager.Instance.CreatePrizedBattleRoom(roomOptions);
            CustomNetworkManager.Instance.MonsterRank = battleRank;
            CustomNetworkManager.Instance.MonsterNum = 3;

            Destroy(loading);
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("start_partysetting", OnPartySetting);
            StartCoroutine(CalculateUptime());
        }

        private void OnPartySetting(string obj)
        {
            UIMatchingNotify.Instance.CloseMenu();
            CloseMenu();
            partySettingMenu.SetUIBattleRoom(null);
        }

        private string GetPlayerName()
        {
            var playerName = GameDataManager.Instance.LoadedPlayerData.PlayerName.Trim();
            return playerName.Length > 12 ? playerName[..12] : playerName;
        }

        private static Toggle GetActiveToggle(ToggleGroup toggleGroup)
        {
            return toggleGroup.ActiveToggles().FirstOrDefault();
        }

        private void UnlockRankMatchRanks()
        {
            var playerRank = (int)GameProgressManager.Instance.GameProgress.PlayerRank;
            for (var i = 0; i <= Math.Min(playerRank, rankToggle.Length - 1); i++)
            {
                rankToggle[i].interactable = true;
                rankToggle[i].transform.Find("Image").GetChild(0).gameObject.SetActive(false);
            }
        }

        private async UniTask HandlePrizeBattle()
        {
            try
            {
                var availableRankedRoom = await CustomNetworkManager.Instance.GetAvailablePrizedRoom();
                if (availableRankedRoom.Count > 0)
                {
                    foreach (var room in from room in availableRankedRoom
                             where room.metadata.monsterRank ==
                                   GetActiveToggle(rankGroup).GetComponent<UIToggleValue>().value
                             where room.metadata.sectorId != CustomNetworkManager.Instance.GetSectorId()
                             where room.metadata.guildId != CustomNetworkManager.Instance.GetGuildId()
                                   || room.metadata.guildId == ""
                                       && CustomNetworkManager.Instance.GetGuildId() == ""
                             select room)
                    {
                        var roomData = await JoinPrizedRoomByID(room.roomId);
                        if (!roomData.Item1)
                        {
                            await CreatePrizedBattleRoom();
                        }

                        return;
                    }
                }

                await CreatePrizedBattleRoom();
            }
            catch (Exception ex)
            {
                Debug.LogWarning("Error fetching available ranked rooms: " + ex.Message);
                StartCoroutine(CalculateUptime());
            }
        }

        private async UniTask<(bool, string, string)> JoinPrizedRoomByID(string selectedRoomID)
        {
            var loading = BackendLoadData.Instance.LoadingCanvas("JoinPrizedRoomByID");
            var options = new Dictionary<string, object>
            {
                { "playerName", GetPlayerName().Trim() },
                { "playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId },
                { "token", BackendLoadData.Instance.token },
                { "walletId", GameDataManager.Instance.LoadedPlayerData.PlayerWallet }
            };
            var roomData = await CustomNetworkManager.Instance.ConnectToRoomAsync(selectedRoomID, options);
            Destroy(loading);
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("start_partysetting", OnPartySetting);
            StopAllCoroutines();
            return roomData;
        }

        private void UpdatePrizeInfo(int value)
        {
            currentProgressSlider.value = value + 1;
            
            UpdateBonusValue(value);

            currentPrizeList[0].gameObject.SetActive(false);
            currentPrizeList[1].gameObject.SetActive(false);
            currentPrizeList[2].gameObject.SetActive(false);
            currentPrizeList[3].gameObject.SetActive(false);
            switch (value)
            {
                case 0:
                    currentPrizeBit.text = "500";
                    break;
                case 1:
                    currentPrizeBit.text = "5,000";
                    currentPrizeList[0].gameObject.SetActive(true);
                    break;
                case 2:
                    currentPrizeBit.text = "10,000";
                    currentPrizeList[0].gameObject.SetActive(true);
                    currentPrizeList[1].gameObject.SetActive(true);
                    currentPrizeList[3].gameObject.SetActive(true);
                    break;
                case 3:
                    currentPrizeBit.text = "15,000";
                    currentPrizeList[0].gameObject.SetActive(true);
                    currentPrizeList[1].gameObject.SetActive(true);
                    currentPrizeList[2].gameObject.SetActive(true);
                    currentPrizeList[3].gameObject.SetActive(true);
                    break;
            }
        }
    }
}