using System;
using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.Controllers;
using Assets.Scripts.DataStruct.Data;
using Assets.Scripts.Enums;
using Assets.Scripts.Managers;
using Assets.Scripts.Models;
using Assets.Scripts.Scriptables;
using Assets.Scripts.SerializeDataStruct.ItemData;
using Assets.Scripts.UI.Utilities;
using CustomBattle;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using UI.BattleUI.ItemReward;
using UnityEngine;
using UnityEngine.UI;
using Random = UnityEngine.Random;

namespace Assets.Scripts.UI.BattleUI
{
    public class UIBattleWinAnnouncer : MonoBehaviour
    {
        [SerializeField] private RectTransform winningLine;
        [SerializeField] private RectTransform winningBackLogo;
        [SerializeField] private RectTransform winningFrontLogo;
        [SerializeField] private RectTransform winningTextImage;
        [SerializeField] private Image background;
        [SerializeField] private float delayTrigger = 3;
        [SerializeField] private UIItemRewardMenu uIItemRewardMenu;
        [SerializeField] private UIBattleResultContent battleResultUI;
        [SerializeField] private Button battleResult;
        private float delayTimer = 0;
        [SerializeField] private bool canTriggerAnimation = false;
        private bool isAnimationPlaying = false;

        private void Awake()
        {
            battleResult.onClick.AddListener(() =>
            {
                WinSet();
                battleResultUI.OpenMenu();
                AssistantController.SuccessAction();
            });
        }

        private async void WinSet()
        {
            try
            {
                var selectedTournamentDetail = TournamentDataManager.Instance.SelectedTournament;
                var loading = BackendLoadData.Instance.LoadingCanvas("WinSet");
                var itemSource = "tournament_pve";
                try
                {
                    if (selectedTournamentDetail != null)
                    {
                        if (AssistantManager.Instance.CurrentTutorial == null || string.IsNullOrEmpty(AssistantManager.Instance.CurrentTutorial.TutorialKey))
                        {
                            UINotifyManager.AddNotifyResponse("Tournament Won", () =>
                            {
                                var msg = I2.Loc.LocalizationManager.GetTranslation("Assistance Msg General/Battle won");
                                AssistantManager.Instance.SetDialog(msg, "smile", false, true, true);
                                Controllers.AssistantController.SetInteractionBlock();
                            }, UINotifyManager.NotifyType.System);
                            UINotifyManager.ProcessNotify();
                        }
                        GameProgressManager.Instance.AddTournamentWon();
                        if (!TournamentDataManager.Instance.IsUsingTicket)
                        {
                            GameDataController.AddBitBalance(selectedTournamentDetail.BasicBitPrize * CustomGameManager.Instance.FriendlySpawnListInfo.Count,
                                "reward_tournament", $"tournament_reward_{selectedTournamentDetail.TournamentName}");
                            uIItemRewardMenu.SetBit(selectedTournamentDetail.BasicBitPrize * CustomGameManager.Instance.FriendlySpawnListInfo.Count);
                            if (selectedTournamentDetail.BasicRewardItem != null &&
                                selectedTournamentDetail.BasicRewardItemRandom.Count == 0)
                            {
                                GameDataController.AddItemBalance(new() { new(selectedTournamentDetail.BasicRewardItem, 1) }, new() { 1 }, itemSource);
                                uIItemRewardMenu.SetItem(selectedTournamentDetail.BasicRewardItem, 1);
                            }

                            if (selectedTournamentDetail.BasicRewardItemRandom.Count > 0)
                            {
                                var randomItem = selectedTournamentDetail.BasicRewardItemRandom[Random.Range(0, selectedTournamentDetail.BasicRewardItemRandom.Count - 1)];
                                GameDataController.AddItemBalance(new() { new(randomItem, 1) }, new() { 1 }, itemSource);
                                uIItemRewardMenu.SetItem(randomItem, 1);
                            }
                        }
                        else
                        {
                            SerializeItem ticket;
                            if (GameDataManager.Instance.LoadedPlayerData.PlayerItemDict.TryGetValue(TournamentDataManager.Instance.LowTicketData.ItemId, out var lowCostTicket))
                            {
                                if (lowCostTicket.Quantity > 0)
                                {
                                    GameDataController.AddItemBalance(new() { lowCostTicket }, new() { -1 }, itemSource);
                                    GameProgressManager.OnPvETournamentUpdatedEvent?.Invoke();
                                }
                                else if (GameDataManager.Instance.LoadedPlayerData.PlayerItemDict.TryGetValue(TournamentDataManager.Instance.TicketData.ItemId, out ticket))
                                {
                                    if (ticket.Quantity > 0)
                                    {
                                        GameDataController.AddItemBalance(new List<SerializeItem> { ticket }, new() { -1 }, itemSource);
                                        GameProgressManager.OnPvETournamentUpdatedEvent?.Invoke();
                                    }
                                }
                            }
                            else if (GameDataManager.Instance.LoadedPlayerData.PlayerItemDict.TryGetValue(TournamentDataManager.Instance.TicketData.ItemId, out ticket))
                            {
                                if (ticket.Quantity > 0)
                                {
                                    GameDataController.AddItemBalance(new List<SerializeItem> { ticket }, new() { -1 }, itemSource);
                                    GameProgressManager.OnPvETournamentUpdatedEvent?.Invoke();
                                }
                            }
                            while (GameDataModel.IsBurningItem)
                                await UniTask.WaitForEndOfFrame(this);
                            GameDataController.AddBitBalance(selectedTournamentDetail.TicketBitPrize * CustomGameManager.Instance.FriendlySpawnListInfo.Count,
                                "reward_tournament", $"tournament_reward_{selectedTournamentDetail.TournamentName}_ticket");
                            uIItemRewardMenu.SetBit(selectedTournamentDetail.TicketBitPrize * CustomGameManager.Instance.FriendlySpawnListInfo.Count);

                            if (selectedTournamentDetail.TicketRewardItem != null &&
                                selectedTournamentDetail.TicketRewardItemRandom.Count == 0)
                            {
                                GameDataController.AddItemBalance(new() { new(selectedTournamentDetail.TicketRewardItem, 1) }, new() { 1 }, itemSource);
                                uIItemRewardMenu.SetItem(selectedTournamentDetail.TicketRewardItem, 1);
                            }

                            if (selectedTournamentDetail.TicketRewardItemRandom.Count > 0)
                            {
                                var randomItem = selectedTournamentDetail.TicketRewardItemRandom[Random.Range(0, selectedTournamentDetail.TicketRewardItemRandom.Count - 1)];
                                GameDataController.AddItemBalance(new() { new(randomItem, 1) }, new() { 1 }, itemSource);
                                uIItemRewardMenu.SetItem(randomItem, 1);
                            }
                        }
                        while (GameDataModel.IsCreatingItem)
                            await UniTask.WaitForEndOfFrame(this);
                        if (!GameDataModel.IsCreateItemSuccess)
                            throw new System.Exception("Mint item fail");
                        foreach (var tournamentData in GameDataManager.Instance.LoadedPlayerData.PlayerTournamentList)
                        {
                            if (tournamentData.TournamentID == selectedTournamentDetail.TournamentId &&
                                tournamentData.TournamentYear == GameProgressManager.Instance.CurrentGameYear)
                            {
                                tournamentData.SetTournamentWon(true);
                                GameDataManager.OnTournamentDataChanged?.Invoke();
                                break;
                            }
                        }
                    
                        ScoreManager.AddUserScoreRecord?.Invoke(new SerializeUserScoreRecord(string.Empty, GameDataManager.Instance.LoadedPlayerData.PlayerWallet, 1, 2, System.DateTime.UtcNow.Date, true, 1));
                        uIItemRewardMenu.OpenMenu();
                    }

                    Destroy(loading);
                }
                catch (Exception ex)
                {
                    Destroy(loading);
                    Debug.LogError(ex.Message);
                    UIPopupNotify.Instance.SetNotify("Error", "Can't receive reward items\n" + ex.Message);
                }

                UpdatePlayerRank(selectedTournamentDetail);
            }
            catch (Exception e)
            {
                Debug.Log(e);
            }
        }

        private static void UpdatePlayerRank(ScriptableTournamentDetail selectedTournamentDetail)
        {
            if (GameProgressManager.Instance is null || selectedTournamentDetail is null)
                return;
            MintTrophy(selectedTournamentDetail).GetAwaiter();

            var currentPlayerRank = (int)GameProgressManager.Instance.GameProgress.PlayerRank;

            switch (selectedTournamentDetail.TournamentId)
            {
                case "3_4_F" or "6_4_F" or "9_4_F" or "12_4_F":
                    if (currentPlayerRank < (int)PlayerRanksEnum.BeginnerPlus)
                        GameProgressManager.Instance.ChangePlayerRank(PlayerRanksEnum.BeginnerPlus);
                    break;
                case "3_4_E" or "6_4_E" or "9_4_E" or "12_4_E":
                    if (currentPlayerRank < (int)PlayerRanksEnum.Intermediate)
                        GameProgressManager.Instance.ChangePlayerRank(PlayerRanksEnum.Intermediate);
                    break;
                case "3_4_D" or "6_4_D" or "9_4_D" or "12_4_D":
                    if (currentPlayerRank < (int)PlayerRanksEnum.IntermediatePlus)
                        GameProgressManager.Instance.ChangePlayerRank(PlayerRanksEnum.IntermediatePlus);
                    break;
                case "3_4_C" or "6_4_C" or "9_4_C" or "12_4_C":
                    if (currentPlayerRank < (int)PlayerRanksEnum.Advance)
                        GameProgressManager.Instance.ChangePlayerRank(PlayerRanksEnum.Advance);
                    break;
                case "3_4_B" or "6_4_B" or "9_4_B" or "12_4_B":
                    if (currentPlayerRank < (int)PlayerRanksEnum.AdvancePlus)
                        GameProgressManager.Instance.ChangePlayerRank(PlayerRanksEnum.AdvancePlus);
                    break;
                case "3_4_A" or "6_4_A" or "9_4_A" or "12_4_A":
                    if (currentPlayerRank < (int)PlayerRanksEnum.Expert)
                        GameProgressManager.Instance.ChangePlayerRank(PlayerRanksEnum.Expert);
                    break;
                case "3_4_S" or "6_4_S" or "9_4_S" or "12_4_S":
                    if (currentPlayerRank < (int)PlayerRanksEnum.Master)
                        GameProgressManager.Instance.ChangePlayerRank(PlayerRanksEnum.Master);
                    break;
                case "2_3_S":
                    if (GameProgressManager.Instance.GameProgress.MonsterGrandPrixComplete == default)
                    {
                        GameProgressManager.Instance.GameProgress.MonsterGrandPrixComplete = System.DateTime.UtcNow;
                        GameProgressManager.OnPlayerProgressUpdated?.Invoke();
                    }
                    break;
                case "5_3_S":
                    if (GameProgressManager.Instance.GameProgress.CrownCupComplete == default)
                    {
                        GameProgressManager.Instance.GameProgress.CrownCupComplete = System.DateTime.UtcNow;
                        GameProgressManager.OnPlayerProgressUpdated?.Invoke();
                    }
                    break;
                case "8_3_S":
                    if (GameProgressManager.Instance.GameProgress.SanctuaryCupComplete == default)
                    {
                        GameProgressManager.Instance.GameProgress.SanctuaryCupComplete = System.DateTime.UtcNow;
                        GameProgressManager.OnPlayerProgressUpdated?.Invoke();
                    }
                    break;
                case "11_3_S":
                    if (GameProgressManager.Instance.GameProgress.WinnersCupComplete == default)
                    {
                        GameProgressManager.Instance.GameProgress.WinnersCupComplete = System.DateTime.UtcNow;
                        GameProgressManager.OnPlayerProgressUpdated?.Invoke();
                    }
                    break;
                case "1_4_S":
                    if (currentPlayerRank < (int)PlayerRanksEnum.Legend)
                        GameProgressManager.Instance.ChangePlayerRank(PlayerRanksEnum.Legend);
                    if (GameProgressManager.Instance.GameProgress.ReMasterCupComplete == default)
                    {
                        GameProgressManager.Instance.GameProgress.ReMasterCupComplete = System.DateTime.UtcNow;
                        GameProgressManager.OnPlayerProgressUpdated?.Invoke();
                    }
                    break;
            }
            if (GameProgressManager.Instance.GameProgress.CrownCupComplete != default
                && GameProgressManager.Instance.GameProgress.SanctuaryCupComplete != default
                && GameProgressManager.Instance.GameProgress.MonsterGrandPrixComplete != default
                && GameProgressManager.Instance.GameProgress.WinnersCupComplete != default
                && GameProgressManager.Instance.GameProgress.ReMasterCupComplete != default)
            {
                AssistantManager.Instance.WaitForSceneChange = true;
                AssistantManager.Instance.PlayTutorialOnChangeScene(21);
            }
        }

        private static async UniTask MintTrophy(ScriptableTournamentDetail selectedTournamentDetail)
        {
            var nftData = await BackendLoadData.Instance.LoadItemTrophyAsync();
            var mintTrophyList = nftData.listToken.Select(trophy => trophy.rank).ToList();
            switch (selectedTournamentDetail.TournamentRank)
            {
                case RankTypeEnums.f:
                    if (!mintTrophyList.Contains((int)RankTypeEnums.f))
                        BackendLoadData.Instance.CreateTrophy("RANK_F");
                    break;
                case RankTypeEnums.e:
                    if (!mintTrophyList.Contains((int)RankTypeEnums.e))
                        BackendLoadData.Instance.CreateTrophy("RANK_E");
                    break;
                case RankTypeEnums.d:
                    if (!mintTrophyList.Contains((int)RankTypeEnums.d))
                        BackendLoadData.Instance.CreateTrophy("RANK_D");
                    break;
                case RankTypeEnums.c:
                    if (!mintTrophyList.Contains((int)RankTypeEnums.c))
                        BackendLoadData.Instance.CreateTrophy("RANK_C");
                    break;
                case RankTypeEnums.b:
                    if (!mintTrophyList.Contains((int)RankTypeEnums.b))
                        BackendLoadData.Instance.CreateTrophy("RANK_B");
                    break;
                case RankTypeEnums.a:
                    if (!mintTrophyList.Contains((int)RankTypeEnums.a))
                        BackendLoadData.Instance.CreateTrophy("RANK_A");
                    break;
                case RankTypeEnums.s:
                case RankTypeEnums.sp:
                    if (!mintTrophyList.Contains((int)RankTypeEnums.s))
                        BackendLoadData.Instance.CreateTrophy("RANK_S");
                    break;
            }
        }

        private void Update()
        {
            if (isAnimationPlaying) return;
            if (!canTriggerAnimation) return;
            if (delayTimer > 0)
            {
                delayTimer -= Time.deltaTime;
                return;
            }

            isAnimationPlaying = true;
            background.gameObject.SetActive(true);
            var animationSequence = DOTween.Sequence();
            animationSequence.Append(winningLine.DOAnchorPos(Vector2.zero, 0.5f))
                .Insert(0.4f,
                    winningBackLogo.DOAnchorPos(new Vector2(0, winningBackLogo.anchoredPosition.y), 0.2f)
                        .SetEase(Ease.OutBack))
                .Insert(0.4f,
                    winningFrontLogo.DOAnchorPos(new Vector2(0, winningFrontLogo.anchoredPosition.y), 0.2f)
                        .SetEase(Ease.OutBack))
                .Insert(0.6f, winningTextImage.DOAnchorPos(Vector2.zero, 0.2f).SetEase(Ease.OutBack))
                .OnComplete(() => { battleResult.gameObject.SetActive(true); });
        }

        internal void TriggerAnimation()
        {
            canTriggerAnimation = true;
            delayTimer = delayTrigger;
        }
    }
}