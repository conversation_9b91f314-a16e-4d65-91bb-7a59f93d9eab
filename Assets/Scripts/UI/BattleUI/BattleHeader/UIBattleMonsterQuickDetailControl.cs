using Assets.Scripts.UI.BattleUI.BattleViewDetail;
using System;
using System.Collections.Generic;
using CustomBattle;
using Network;
using TMPro;
using UI.BattleUI.BattleHeader;
using UI.Utilities;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
using Assets.Scripts.Helper;

namespace Assets.Scripts.UI.BattleUI.BattleHeader
{
    public class UIBattleMonsterQuickDetailControl : MonoBehaviour
    {
        #region Private variables
        [SerializeField] private List<UIBattleMonsterQuickDetail> teamAData;
        [SerializeField] private List<UIBattleMonsterQuickDetail> teamBData;
        [SerializeField] private UIMonsterBarPool battleMonsterPool;
        [SerializeField] private UIMonsterBattleDetail monsterBattleDetail;
        [SerializeField] private Canvas enviromentCanvas;
        [SerializeField] private Camera uiCamera;
        [SerializeField] private GameObject generalSetting;
        [SerializeField] private GameObject playerAData;
        [SerializeField] private GameObject playerBData;
        [SerializeField] private Button exitWatchingBtn;
        [SerializeField] private GameObject spectator;
        [SerializeField] private TextMeshProUGUI numberOfSpectators;
        [SerializeField] private Button returnHomePopupBtn;
        [SerializeField] private GameObject returnHomePopup;
        [SerializeField] private UIPopupPanel popupPanel;

        #endregion

        #region Public variables
        public static Action<int> OnTurnSwitched { get; private set; }

        public static Action<int> OnHealthChanged { get; private set; }

        public static Action<int> OnStaminaChanged { get; private set; }

        public static Action<int> OnActionPerformed { get; private set; }
        
        public List<UIBattleMonsterQuickDetail> ListTeamData { get ; set ; }

        #endregion

        private void Awake()
        {
            popupPanel.Init();
            OnTurnSwitched = null;
            OnHealthChanged = null;
            OnStaminaChanged = null;
            OnActionPerformed = null;
   
        }

        private void Start()
        {
            if (CustomNetworkManager.Instance.GetRoom() != null)
            {
                if (CustomNetworkManager.Instance.IsReconnected || CustomNetworkManager.Instance.IsSpectator())
                {
                    return;
                }
            }
            ListTeamData = new List<UIBattleMonsterQuickDetail>();

            if (CustomGameManager.Instance)
            {
                CustomGameManager.Instance.reviveUnitEvent.AddListener(OnRevive);
            }
            CustomGameManager.OnTeamReady += AssignObserveData;
            CustomGameManager.OnGameStart += AddGeneralButtons;
            CustomGameManager.OnGameStart += AssignObserveData;
        }
        private void OnDisable()
        {
            Remove();
        }

        private void Remove()
        {
            if (CustomGameManager.Instance)
            {
                CustomGameManager.Instance.reviveUnitEvent.RemoveAllListeners();
            }
            CustomGameManager.OnTeamReady -= AssignObserveData;
            CustomGameManager.OnGameStart -= AddGeneralButtons;
            CustomGameManager.OnGameStart -= AssignObserveData;
        }
        public void AddGeneralButtons()
        {
            if (CustomNetworkManager.Instance.GetRoom() != null)
            {
                if (CustomNetworkManager.Instance.IsSpectator())
                {
                    spectator.SetActive(true);
                    numberOfSpectators.text = CustomNetworkManager.Instance.GetRoom().State.metadata.spectatorCount.ToString();
                    CustomNetworkManager.onGameStateChange += UpdateNumberOfSpectators;
                    generalSetting.SetActive(false);
                    exitWatchingBtn.gameObject.SetActive(true);
                    exitWatchingBtn.onClick.AddListener(ExitWatching);
                }
                else
                {
                    spectator.SetActive(false);
                    generalSetting.SetActive(true);
                    exitWatchingBtn.gameObject.SetActive(false);
                }
            }
            else
            {   
                spectator.SetActive(false);
                generalSetting.SetActive(true);
                exitWatchingBtn.gameObject.SetActive(false);
            }
       
        }

        private void ExitWatching()
        {
            returnHomePopup.SetActive(true);
            returnHomePopupBtn.onClick.AddListener(() =>
            {
                CustomNetworkManager.onGameStateChange -= UpdateNumberOfSpectators;
                LeaveRoom();
                if (SceneHelper.Instance is null)
                {
                    SceneManager.LoadScene(SceneManager.GetActiveScene().buildIndex - 1);
                }
                else
                {
                    SceneHelper.Instance.LoadScene(1, 2);
                }
            });
        }

        private async void LeaveRoom()
        {
            try
            {
                if (CustomNetworkManager.Instance.GetRoom() != null)
                {
                    await CustomNetworkManager.Instance.LeaveRoom();
                }
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        public void AssignObserveData(GameTeam readyTeam)
        {
            switch (readyTeam)
            {
                case GameTeam.Blue:
                    AddStatUIView(teamAData, GameManager.GetUnitsOnTeam(GameTeam.Blue),GameTeam.Blue);
                    break;
                case GameTeam.Red:
                    AddStatUIView(teamBData, GameManager.GetUnitsOnTeam(GameTeam.Red), GameTeam.Red);
                    break;
            }
        }
        private void UpdateNumberOfSpectators()
        {
            numberOfSpectators.text = CustomNetworkManager.Instance.GetRoom().State.metadata.spectatorCount.ToString();
        }
        public void AssignObserveData()
        {
            AddStatUI(teamAData, GameManager.GetUnitsOnTeam(GameTeam.Blue), GameTeam.Blue);
            AddStatUI(teamBData, GameManager.GetUnitsOnTeam(GameTeam.Red), GameTeam.Red);
        }

        private void AddStatUIView(List<UIBattleMonsterQuickDetail> teamData, List<GridUnit> gridUnits, GameTeam playSide)
        {
            var assignedDataCount = 0;

            foreach (var t in gridUnits)
            {
                var monsterInfoUI = teamData[assignedDataCount];
                
                var monsterInGame = t.GameMonster;
                
                monsterInGame.MonsterInfo.BasicBattleParameters = t.GameMonster.MonsterInfo.BasicBattleParameters;
               
                monsterInfoUI.SetObserveMonster(monsterInGame);
                
                var monsterRank = monsterInGame.MonsterInfo.MonsterRank;

                var monsterScriptable = monsterInGame.MonsterInfo.MonsterScriptableData;

                var monsterName = monsterScriptable.MonsterName;

                var unitObserveData = new UIUnitObserveData(0, monsterName, monsterRank, monsterInGame, monsterInfoUI, monsterScriptable.MonsterScreenshot, playSide);
                unitObserveData.SetBattleMonsterDetail(monsterBattleDetail);
                assignedDataCount++;
            }
            
            for (var i = assignedDataCount; i < teamData.Count; i++)
            {
                var monsterInfoUI = teamData[i];
                monsterInfoUI.SetBlank();
            }
        }

        private void AddStatUI(List<UIBattleMonsterQuickDetail> teamData, List<GridUnit> gridUnits, GameTeam playSide)
        {
            var assignedDataCount = 0;

            foreach (var t in gridUnits)
            {
                var monsterInfoUI = teamData[assignedDataCount];
                
                var monsterInGame = t.GameMonster;
                
                monsterInGame.MonsterInfo.BasicBattleParameters = t.GameMonster.MonsterInfo.BasicBattleParameters;

                var monsterBattleId = monsterInGame.BattleID;
                
                var monsterRank = monsterInGame.MonsterInfo.MonsterRank;

                var monsterScriptable = monsterInGame.MonsterInfo.MonsterScriptableData;

                var monsterName = monsterScriptable.MonsterName;

                var unitObserveData = new UIUnitObserveData(monsterBattleId, monsterName, monsterRank, monsterInGame, monsterInfoUI, monsterScriptable.MonsterScreenshot, playSide);
                
                monsterInfoUI.SetObserveMonster(monsterInGame);

                unitObserveData.SetBattleMonsterDetail(monsterBattleDetail);
                
                var uiMonsterBattleBar = battleMonsterPool.Pool.Get();
                uiMonsterBattleBar.SetCanvasMonster(uiCamera, enviromentCanvas, monsterInGame.transform, t.GetTeam());
                unitObserveData.SetMonsterBar(uiMonsterBattleBar);
                ListTeamData.Add(monsterInfoUI);

                OnTurnSwitched += unitObserveData.ObserveTurn;
                OnHealthChanged += unitObserveData.ObserveHealth;
                OnStaminaChanged += unitObserveData.ObserveStamina;
                OnActionPerformed += unitObserveData.HideBar;

                assignedDataCount++;
            }

            for (var i = assignedDataCount; i < teamData.Count; i++)
            {
                var monsterInfoUI = teamData[i];
                monsterInfoUI.SetBlank();
            }
        }

        public void OnRevive(GridUnit unit)
        {
            var uiMonsterBattleBar = battleMonsterPool.Pool.Get();
            uiMonsterBattleBar.SetCanvasMonster(uiCamera, enviromentCanvas, unit.transform, unit.GetTeam());
        }
    }
}