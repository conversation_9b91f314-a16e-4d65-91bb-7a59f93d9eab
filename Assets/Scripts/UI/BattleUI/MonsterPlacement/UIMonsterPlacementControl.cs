using System;
using Assets.Scripts.UI.BattleUI.BattleViewDetail;
using Assets.Scripts.Managers;
using System.Collections.Generic;
using System.Linq;
using CustomBattle;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using Cysharp.Threading.Tasks;
using Network;
using Assets.Scripts.UI.BattleUI.StartUI;
using UI.BattleUI.UIBattleAbility;
using Assets.Scripts.UI.BattleUI.PvP;
using Assets.Scripts.Helper;

namespace Assets.Scripts.UI.BattleUI.MonsterPlacement
{
    public class UIMonsterPlacementControl : MonoBehaviour
    {
        [SerializeField] private GameObject monsterPlaceItemPrefab;
        [SerializeField] private Transform holder;
        [SerializeField] private UIAbilitySetMenu skillSetmenu;

        [Header("Action Buttons")]
        [SerializeField]
        private TextMeshProUGUI placementCountText;

        [SerializeField] private UIButtonExtention startBtn;
        [SerializeField] private Button cancelSpawnBtn;
        [SerializeField] private Button backToHomeBtn;
        [SerializeField] private TextMeshProUGUI backToHomeText;
        [SerializeField] private GameObject returnHomePopup;
        [SerializeField] private UIMonsterBattleDetail detailBattleData;
        [SerializeField] private GameObject[] disableObjectForLoadScene;
        [SerializeField] private RectTransform uiContainer;
        [SerializeField] private RectTransform monsterPlaceContainer;
        [SerializeField] private RectTransform settingsControlContainer;

        private readonly List<UIMonsterPlacementItem> _monsterPlacementItemList = new();
        private UIMonsterPlacementItem _currentSpawnUI;

        [SerializeField] private UIBattleStart gameStartUI;
        public UIBattleStart GameStartUI => gameStartUI;

        private void Awake()
        {
            backToHomeBtn.onClick.AddListener(() => { returnHomePopup.SetActive(true); });
            backToHomeText.text = CustomPvpModeAI.Instance.IsTournamentMode ? "Decline" : "Home";
        }

        private void Start()
        {
            CustomGameManager.Instance.ShowCountdownClock(false);

            if (CustomNetworkManager.Instance.GetRoom() != null)
            {
                if (CustomNetworkManager.Instance.IsReconnected || CustomNetworkManager.Instance.IsSpectator())
                {
                    CustomGameManager.Instance.ShowCountdownClock(true);
                    CustomGameManager.Instance.UpdateCountdownUIPosForPlacementPhase();
                }
            }
            else
            {
                if (CustomGameManager.Instance.PlayerSide == GameTeam.Blue)
                {
                    monsterPlaceContainer.ApplyAnchorPreset(TextAnchor.MiddleLeft, new Vector3(20, 0, 0), true);
                    settingsControlContainer.ApplyAnchorPreset(TextAnchor.LowerRight, new Vector3(200, -95, 0),
                        true);
                }
                else
                {
                    monsterPlaceContainer.ApplyAnchorPreset(TextAnchor.MiddleRight, new Vector3(-10, 0, 0), true);
                    settingsControlContainer.ApplyAnchorPreset(TextAnchor.LowerLeft, new Vector3(-200, -95, 0),
                        true);
                }

                uiContainer.sizeDelta = new Vector2(5000, 5000);
                uiContainer.DOSizeDelta(new Vector2(1920, 1080), 1f)
                    .SetDelay(5f)
                    .SetEase(Ease.OutQuad)
                    .OnComplete(() =>
                    {
                        if (!CustomPvpModeAI.Instance.IsPvpModeAI) return;
                        CustomGameManager.Instance.ShowCountdownClock(true);
                        CustomGameManager.Instance.UpdateCountdownUIPosForPlacementPhase();
                        CustomOnlineBattle.Instance.OnCountDown(60, false);
                    });
                LoadMonsterPrefab();
            }

            cancelSpawnBtn.onClick.AddListener(() =>
            {
                CustomGameManager.OnRemoveSpawnMonster?.Invoke();
                UpdateView();
            });
            startBtn.Button.onClick.AddListener(SetPlayerReady);
            cancelSpawnBtn.gameObject.SetActive(false);
            backToHomeBtn.gameObject.SetActive(true);
            startBtn.gameObject.SetActive(false);

        }

        public void ReturnToHome(bool isPvp)
        {
            CustomPvpModeAI.Instance.ResetData();
            if (isPvp)
            {
                foreach (var obj in disableObjectForLoadScene)
                {
                    obj.SetActive(false);
                }
                CustomNetworkManager.Instance.ClearReconnectionToken();
            }
            else
            {
                if (CustomNetworkManager.Instance.GetRoom() != null && CustomNetworkManager.Instance.IsSpectator() == false )
                {
                    CustomNetworkManager.Instance.ClearReconnectionToken();
                    CustomOnlineBattle.Instance.OnSurrenderMessage().GetAwaiter();
                    LeaveRoom();
                }
                foreach (var obj in disableObjectForLoadScene)
                {
                    obj.SetActive(false);
                }
                TournamentDataManager.Instance.CancelTournament();
            }

            SceneHelper.Instance.LoadScene(1, 2);
        }

        private async void LeaveRoom()
        {
            try
            {
                var currentRoom = CustomNetworkManager.Instance.GetCurrentRoom();
                if (currentRoom != null)
                {
                    await CustomNetworkManager.Instance.LeaveRoom();
                }
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private void OnEnable()
        {
            GameDataManager.OnMonsterDataUpdated += UpdateView;
            GameDataManager.OnFarmDataUpdated += UpdateView;
            CustomGameManager.OnMonsterEdit += EditMonster;
            CustomGameManager.OnSelectSpawnMonster += SpawnMonster;
            CustomGameManager.OnMonsterSpawnLocked += ClearCurrentSelected;
            var currentRoom = CustomNetworkManager.Instance.GetCurrentRoom();
            if (currentRoom != null)
            {
                CustomOnlineBattle.Instance.OnStartCountDownEnd += OnCountDownEnd;
            }
            else
            {
                if (CustomPvpModeAI.Instance.IsPvpModeAI)
                {
                    CustomOnlineBattle.Instance.OnStartCountDownEnd += OnCountDownEnd;
                }
            }
        }

        private void OnDisable()
        {
            GameDataManager.OnMonsterDataUpdated -= UpdateView;
            GameDataManager.OnFarmDataUpdated -= UpdateView;
            CustomGameManager.OnMonsterEdit -= EditMonster;
            CustomGameManager.OnSelectSpawnMonster -= SpawnMonster;
            CustomGameManager.OnMonsterSpawnLocked -= ClearCurrentSelected;
            if (CustomNetworkManager.Instance.GetRoom()!= null)
            {
                CustomOnlineBattle.Instance.OnStartCountDownEnd -= OnCountDownEnd;
            }
            else
            {
                if (CustomPvpModeAI.Instance != null && CustomPvpModeAI.Instance.IsPvpModeAI)
                {
                    CustomOnlineBattle.Instance.OnStartCountDownEnd -= OnCountDownEnd;
                }
            }
        }

        private void OnCountDownEnd()
        {
            // Automatically place monsters if not already placed
            if (CustomGameManager.Instance.FriendlySpawnListInfo.Count < 1)
            {
                ForcePlaceMonster();
            }

            // Start the battle
            SetPlayerReady();
        }

        public void SetToPlay()
        {
            if (CustomGameManager.Instance.PlayerSide == GameTeam.Blue)
            {
                monsterPlaceContainer.ApplyAnchorPreset(TextAnchor.MiddleLeft, new Vector3(20, 0, 0), true);
                settingsControlContainer.ApplyAnchorPreset(TextAnchor.LowerRight, new Vector3(200, -95, 0),
                    true);
            }
            else
            {
                monsterPlaceContainer.ApplyAnchorPreset(TextAnchor.MiddleRight, new Vector3(-10, 0, 0), true);
                settingsControlContainer.ApplyAnchorPreset(TextAnchor.LowerLeft, new Vector3(-200, -95, 0),
                    true);
            }

            uiContainer.sizeDelta = new Vector2(5000, 5000);
            uiContainer.DOSizeDelta(new Vector2(1920, 1080), 1f)
                .SetDelay(3.5f)
                .SetEase(Ease.OutQuad)
                .OnComplete(() =>
                {
                    if (CustomNetworkManager.Instance.GetRoom() == null) return;
                    CustomGameManager.Instance.ShowCountdownClock(true);
                    CustomGameManager.Instance.UpdateCountdownUIPosForPlacementPhase();
                });
            LoadOnlineMonsterPrefab();
        }
        private void SpawnMonster(SpawnListInfo spawnInfo)
        {
            //lock others spawn buttons
            foreach (var t in _monsterPlacementItemList)
            {
                if (t.MonsterInfo.MonsterId == spawnInfo.MonsterInfo.MonsterId)
                {
                    _currentSpawnUI = t;
                    continue;
                }

                t.SetLock(true);
            }

            cancelSpawnBtn.gameObject.SetActive(true);
            backToHomeBtn.gameObject.SetActive(false);
            startBtn.gameObject.SetActive(false);
        }

        private void EditMonster(ILevelCell selectCell)
        {
            if (_currentSpawnUI) return;
            var selectMonster = CustomGameManager.Instance.FriendlySpawnListInfo.FirstOrDefault(spawnInfo => spawnInfo.SpawnCell == selectCell);

            if (selectMonster == null) return;
            foreach (var t in _monsterPlacementItemList.Where(t => t.MonsterInfo.MonsterId == selectMonster.MonsterInfo.MonsterId))
            {
                SetCurrentSpawnUI(t);
                t.CheckPlacementStatus(false);
                break;
            }

            cancelSpawnBtn.gameObject.SetActive(true);
            startBtn.gameObject.SetActive(false);
        }

        private void LoadMonsterPrefab()
        {
            if (CustomPvpModeAI.Instance.IsPvpModeAI)
            {
                if (!GameDataManager.Instance.LoadedPlayerData.PlayerPartyGroupDict.TryGetValue(
                        CustomPvpModeAI.Instance.PartyReferenceId, out var partyGroup)) return;
                foreach (var party in partyGroup.PartySetup)
                {
                    if (!GameDataManager.Instance.LoadedPlayerData.PlayerFarmDict.TryGetValue(party.FarmId,
                            out var farm)) continue;
                    if (string.IsNullOrEmpty(party.MonsterId)) continue;
                    if (!GameDataManager.Instance.LoadedPlayerData.PlayerMonsterDict.TryGetValue(party.MonsterId,
                            out var monsterData)) continue;
                    if (string.IsNullOrEmpty(monsterData.MonsterId) || !monsterData.MonsterScriptableData) continue;
                    var innateGameObject = Instantiate(monsterPlaceItemPrefab, holder);
                    var placementItem = innateGameObject.GetComponent<UIMonsterPlacementItem>();
                    placementItem.SetData(monsterData, farm);
                    placementItem.SetSpawnControl(this);
                    foreach (var skillHolder in placementItem.SkillHolders)
                    {
                        skillHolder.ControlButton.interactable = true;
                    }

                    placementItem.AddMonsterDetailMenu(detailBattleData);
                    _monsterPlacementItemList.Add(placementItem);
                }
            }
            else if (CustomPvpModeAI.Instance.IsPveMode)
            {
                if (!GameDataManager.Instance.LoadedPlayerData.PlayerPartyGroupDict.TryGetValue(
                        CustomPvpModeAI.Instance.PartyReferenceId, out var partyGroup)) return;
                foreach (var t in CustomPvpModeAI.Instance.ListMonsterInParty)
                {
                    foreach (var party in partyGroup.PartySetup)
                    {
                        if (!GameDataManager.Instance.LoadedPlayerData.PlayerFarmDict.TryGetValue(party.FarmId,
                                out var farm)) continue;
                        if (string.IsNullOrEmpty(party.MonsterId)) continue;
                        if (!GameDataManager.Instance.LoadedPlayerData.PlayerMonsterDict.TryGetValue(party.MonsterId,
                                out var monsterData)) continue;
                        if (string.IsNullOrEmpty(monsterData.MonsterId) || !monsterData.MonsterScriptableData) continue;
                        if (monsterData.MonsterId != t) continue;
                        var innateGameObject = Instantiate(monsterPlaceItemPrefab, holder);
                        var placementItem = innateGameObject.GetComponent<UIMonsterPlacementItem>();
                        placementItem.SetData(monsterData, farm);
                        placementItem.SetSpawnControl(this);
                        foreach (var skillHolder in placementItem.SkillHolders)
                        {
                            var holder1 = skillHolder;
                            skillHolder.ControlButton.interactable = true;
                            skillHolder.ControlButton.onClick.AddListener(() =>
                                skillSetmenu.SetSkillEdit(monsterData, holder1.SkillData));
                        }

                        placementItem.AddMonsterDetailMenu(detailBattleData);
                        _monsterPlacementItemList.Add(placementItem);
                        break;
                    }
                }
            }
            else if (CustomPvpModeAI.Instance.IsTournamentMode)
            {
                foreach (var monsterInFarm in CustomPvpModeAI.Instance.ListMonsterInFarm)
                {
                    if (!GameDataManager.Instance.LoadedPlayerData.PlayerMonsterDict.TryGetValue(
                            monsterInFarm.MonsterInfo.MonsterId,
                            out var monsterData)) continue;
                    var innateGameObject = Instantiate(monsterPlaceItemPrefab, holder);
                    var placementItem = innateGameObject.GetComponent<UIMonsterPlacementItem>();
                    if (!GameDataManager.Instance.LoadedPlayerData.PlayerFarmDict.TryGetValue(
                            monsterInFarm.FarmID, out var farm)) continue;
                    placementItem.SetData(monsterData, farm);
                    placementItem.SetSpawnControl(this);
                    foreach (var skillHolder in placementItem.SkillHolders)
                    {
                        var holder1 = skillHolder;
                        skillHolder.ControlButton.interactable = true;
                        skillHolder.ControlButton.onClick.AddListener(() =>
                            skillSetmenu.SetSkillEdit(monsterData, holder1.SkillData));
                    }

                    placementItem.AddMonsterDetailMenu(detailBattleData);
                    _monsterPlacementItemList.Add(placementItem);
                }
            }
            else
            {
                var tournamentDetail = TournamentDataManager.Instance.SelectedTournament;
                var playerTour = TournamentDataManager.Instance.GetPlayerTour(tournamentDetail);
                var farmSetup =
                    GameDataManager.Instance.LoadedPlayerData.PlayerFarmSetupList.FirstOrDefault(x => x.IsSelected);
                if (farmSetup == null) return;
                foreach (var setup in farmSetup.FarmSet.Where(setup =>
                             !string.IsNullOrEmpty(setup.MonsterId) && (tournamentDetail == null ||
                                                                        playerTour == null ||
                                                                        playerTour.TournamentParticipateList.Contains(
                                                                            setup.MonsterId))))
                {
                    if (!GameDataManager.Instance.LoadedPlayerData.PlayerMonsterDict.TryGetValue(setup.MonsterId,
                            out var monsterData)) continue;
                    var innateGameObject = Instantiate(monsterPlaceItemPrefab, holder);
                    var placementItem = innateGameObject.GetComponent<UIMonsterPlacementItem>();
                    if (!GameDataManager.Instance.LoadedPlayerData.PlayerFarmDict.TryGetValue(setup.FarmId,
                            out var farm)) continue;
                    placementItem.SetData(monsterData, farm);
                    placementItem.SetSpawnControl(this);
                    foreach (var skillHolder in placementItem.SkillHolders)
                    {
                        var holder1 = skillHolder;
                        skillHolder.ControlButton.interactable = true;
                        skillHolder.ControlButton.onClick.AddListener(() =>
                            skillSetmenu.SetSkillEdit(monsterData, holder1.SkillData));
                    }

                    placementItem.AddMonsterDetailMenu(detailBattleData);
                    _monsterPlacementItemList.Add(placementItem);
                }
            }
        }

        private void UpdateView()
        {
            startBtn.Button.interactable = true;

            foreach (var t in _monsterPlacementItemList)
            {
                t.UpdateView();
                if (!t.IsMonsterPlacement) continue;
                var hasSkillData = t.SkillHolders.Any(skillHolder => skillHolder.SkillData);
                if (hasSkillData && !t.IsCostOverLimit) continue;
                startBtn.Button.interactable = false;
                break;
            }
        }

        internal void SetCurrentSpawnUI(UIMonsterPlacementItem currentSpawnUI)
        {
            UpdateView();
            if (_currentSpawnUI) return;
            _currentSpawnUI = currentSpawnUI;

            var isMonsterSpawned = false;

            foreach (var spawnInfo in CustomGameManager.Instance.FriendlySpawnListInfo.Where(spawnInfo => currentSpawnUI.MonsterInfo.MonsterId == spawnInfo.MonsterInfo.MonsterId))
            {
                isMonsterSpawned = true;
                LockSpawnButtons(currentSpawnUI);
                cancelSpawnBtn.gameObject.SetActive(true);
                startBtn.gameObject.SetActive(false);
                CustomGameManager.OnMonsterEdit?.Invoke(spawnInfo.SpawnCell);
                break;
            }

            if (isMonsterSpawned) return;
            CustomGameManager.OnSelectSpawnMonster?.Invoke(new SpawnListInfo()
            {
                MonsterInfo = currentSpawnUI.MonsterInfo,
            });

            LockSpawnButtons(currentSpawnUI);
       
        }

        private void LockSpawnButtons(UIMonsterPlacementItem currentSpawnUI)
        {
            //lock others spawn buttons
            foreach (var t in _monsterPlacementItemList.Where(t => t != currentSpawnUI))
            {
                t.SetLock(true);
            }
        }

        private void ClearCurrentSelected()
        {
            if (CustomGameManager.Instance.FriendlySpawnListInfo.Count > 0 &&
                CustomGameManager.Instance.FriendlySpawnListInfo != null)
            {
                startBtn.gameObject.SetActive(true);
                AssistantManager.Instance.ContinueCurrentTutorial();
                cancelSpawnBtn.gameObject.SetActive(false);
                var isMonsterSetted = false;
                foreach (var unused in CustomGameManager.Instance.FriendlySpawnListInfo.Where(t => t.MonsterInfo.MonsterId ==
                             _currentSpawnUI.MonsterInfo.MonsterId))
                {
                    isMonsterSetted = true;
                }

                _currentSpawnUI.CheckPlacementStatus(isMonsterSetted);
            }
            else
            {
                startBtn.gameObject.SetActive(false);
                cancelSpawnBtn.gameObject.SetActive(false);
                backToHomeBtn.gameObject.SetActive(true);
            }

            placementCountText.text = $"{CustomGameManager.Instance.FriendlySpawnListInfo.Count}/3";

            _currentSpawnUI = null;

            foreach (var uiPlacement in _monsterPlacementItemList)
            {
                uiPlacement.SetLock(!(CustomGameManager.Instance.FriendlySpawnListInfo.Count < 3));
            }
        }

        public void SetPlayerReady()
        {
            if (CustomNetworkManager.Instance.GetRoom() != null)
            {
                if (CustomNetworkManager.Instance.IsReconnected || CustomNetworkManager.Instance.IsSpectator())
                {
                    gameObject.SetActive(false);
                    CustomGameManager.Instance.WaitForPlacement.SetActive(false);
                }
                else
                {
                    OnSendPartyMessage().GetAwaiter();
                    gameObject.SetActive(false);
                    CustomGameManager.Instance.WaitForPlacement.SetActive(true);
                }
            }
            else
            {
                gameObject.SetActive(false);
                CustomOnlineBattle.Instance.UpdatePlayerPve();
                gameStartUI.gameObject.SetActive(true);
                SetToPlayPve();
            }
        }

        private void SetToPlayPve()
        {
            var customGameManager = CustomGameManager.Instance;
            
            var enemyTeam = CustomGameManager.GetEnemyTeam(customGameManager.PlayerSide);

            customGameManager.SetAIActiveForBot(enemyTeam, true);

            if (customGameManager.IsAIActivated())
            {
                customGameManager.SetAIActiveForBot(customGameManager.PlayerSide, true);
            }
            AssistantManager.Instance.ContinueCurrentTutorial();
        }

        #region PVP

        private async UniTask OnSendPartyMessage()
        {
            var updateList = CustomGameManager.Instance.FriendlySpawnListInfo.Select(
                spawnListInfo => new Dictionary<string, object>
                {
                    { "playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId },
                    { "monsterId", spawnListInfo.MonsterInfo.MonsterId },
                    { "farmId", spawnListInfo.FarmInfo.FarmId },
                    {
                        "position",
                        new Dictionary<string, float>()
                        {
                            { "x", spawnListInfo.SpawnCell.GetIndex().x },
                            { "y", spawnListInfo.SpawnCell.GetIndex().y }
                        }
                    }
                }).ToList();

            await CustomNetworkManager.Instance.GetRoom().Send("place_monster", updateList);
        }

        private int _monsterNum;

        private void LoadOnlineMonsterPrefab()
        {
            _monsterNum = int.TryParse(CustomNetworkManager.Instance.GetGameState().metadata.monsterNum, out var monsterNum) ? monsterNum : 3;

            if (!GameDataManager.Instance.LoadedPlayerData.PlayerPartyGroupDict.TryGetValue(
                    CustomNetworkManager.Instance.partyReferenceId, out var partyGroup))
            {
                partyGroup = new(partyGroup.ServerId, CustomNetworkManager.Instance.partyReferenceId,
                    $"Party {CustomNetworkManager.Instance.partyReferenceId}", true, partyGroup.PartySetup);
            }

            var monsterInParty = partyGroup.PartySetup.Count < _monsterNum ? partyGroup.PartySetup.Count : _monsterNum;
            for (var i = 0; i < monsterInParty; i++)
            {
                if (partyGroup.PartyId != CustomNetworkManager.Instance.partyReferenceId) continue;
                var set = partyGroup.PartySetup[i];
                if (!GameDataManager.Instance.LoadedPlayerData.PlayerFarmDict.TryGetValue(set.FarmId, out var farm))
                    continue;
                if (string.IsNullOrEmpty(set.MonsterId)) continue;
                if (!GameDataManager.Instance.LoadedPlayerData.PlayerMonsterDict.TryGetValue(set.MonsterId,
                        out var monsterData)) continue;
                if (string.IsNullOrEmpty(monsterData.MonsterId) ||
                    monsterData.MonsterScriptableData == null) continue;
                var innateGameObject = Instantiate(monsterPlaceItemPrefab, holder);
                var placementItem =
                    innateGameObject.GetComponent<UIMonsterPlacementItem>();
                placementItem.SetData(monsterData, farm);
                placementItem.SetSpawnControl(this);
                foreach (var skillHolder in placementItem.SkillHolders)
                {
                    skillHolder.ControlButton.interactable = true;
                }

                placementItem.AddMonsterDetailMenu(detailBattleData);
                _monsterPlacementItemList.Add(placementItem);
                // Update PlacementItem when on RedSide
                if (CustomGameManager.Instance.PlayerSide != GameTeam.Red) continue;
                placementItem.background.localEulerAngles = new Vector3(0, 180, 0);
                placementItem.MonsterPlacementBtn.transform.localEulerAngles =
                    new Vector3(0, 0, -90);
                placementItem.MonsterPlacementBtn.GetComponent<RectTransform>()
                    .ApplyAnchorPreset(TextAnchor.MiddleLeft, new Vector3(-40, 50, 0));
                placementItem.MonsterPlacementBtn.transform.Find("Icon").transform
                        .localEulerAngles =
                    new Vector3(0, 0, 90);
                placementItem.MonsterPlacementBtn.transform.Find("Icon")
                    .GetComponent<RectTransform>().ApplyAnchorPreset(TextAnchor.MiddleCenter,
                        new Vector3(0, 0, 0));
            }
        }

        public void ForcePlaceMonster()
        {
            CustomGameManager.OnRemoveSpawnMonster?.Invoke();
            foreach (var t in _monsterPlacementItemList)
            {
                SetCurrentSpawnUI(t);
            }
        }

        #endregion
    }
}