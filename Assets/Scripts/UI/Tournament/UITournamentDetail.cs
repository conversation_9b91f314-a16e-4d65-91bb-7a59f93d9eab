using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.Controllers;
using Assets.Scripts.Helper;
using Assets.Scripts.Managers;
using Assets.Scripts.Scriptables;
using Assets.Scripts.SerializeDataStruct;
using Assets.Scripts.UI.BattleUI.PvP;
using Assets.Scripts.UI.Utilities;
using Network;
using TMPro;
using UI.Utilities;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI.Tournament
{
    public class UITournamentDetail : UIMenuStruct
    {
        [SerializeField] private TextMeshProUGUI tournamentRankText;
        [SerializeField] private TextMeshProUGUI tournamentName;
        [SerializeField] private TextMeshProUGUI tournamentYearText;
        [SerializeField] private TextMeshProUGUI tournamentMonthText;
        [SerializeField] private TextMeshProUGUI tournamentWeekText;
        [SerializeField] private Image tournamentRankBackground;
        [SerializeField] private Toggle ticketToggle;
        [SerializeField] private TextMeshProUGUI ticketLeftText;
        [Space(5)]
        [SerializeField] private Button scheduleButton;
        [SerializeField] private Button cancelButton;
        [SerializeField] private UIButtonExtention participateButton;
        [SerializeField] private GameObject endedButton;
        [Space(5)]
        [SerializeField] private Image cupImage;
        [SerializeField] private Image scheduleImage;
        [SerializeField] private List<UITournamentMonsterSlot> tournamentMonsterSlot = new();
        [SerializeField] private TextMeshProUGUI tournamentConditionText;
        [Header("Basic prize")]
        [SerializeField] private GameObject emptyPrize;
        [SerializeField] private Image itemPrizeIcon;
        [SerializeField] private Image itemRank;
        [SerializeField] private Image itemRankBackground;
        [SerializeField] private TextMeshProUGUI basicBitPrize;
        [SerializeField] private TextMeshProUGUI basicItemNameText;
        [SerializeField] private TextMeshProUGUI basicItemDescriptionText;
        [Header("Ticket prize")]
        [SerializeField] private GameObject emptyTicketPrize;
        [SerializeField] private TextMeshProUGUI ticketItemNameText;
        [SerializeField] private TextMeshProUGUI ticketBitPrize;
        [SerializeField] private TextMeshProUGUI ticketItemDescriptionText;
        [SerializeField] private Image itemTicketRank;
        [SerializeField] private Image itemTicketPrizeIcon;
        [SerializeField] private Image itemTicketRankBackground;
        [SerializeField] private float rewardResetTime = 3;
        [Space(5)]
        [SerializeField] private GameObject[] disableObjectForLoadScene;

        private ScriptableTournamentDetail _tournamentDetailData;
        private int _targetYear;
        private SerializeTournament _referenceTournamentData;

        private void Awake()
        {
            GameDataManager.OnTournamentDataUpdated += ReloadData;
            participateButton.Button.onClick.AddListener(ParticipateTournament);
            OnMenuOpen.AddListener(GetTicket);
            OnMenuOpen.AddListener(RegisterButton);
            OnMenuClose.AddListener(CancelParticipate);
            OnMenuClose.AddListener(RemoveRegisterButton);
            GetTicket();
            RegisterButton();
        }

        private void OnDestroy()
        {
            GameDataManager.OnTournamentDataUpdated -= ReloadData;
        }

        private void RegisterButton()
        {
            scheduleButton.onClick.AddListener(SetSchedule);
            cancelButton.onClick.AddListener(SetCancel);
        }

        private void RemoveRegisterButton()
        {
            scheduleButton.onClick.RemoveListener(SetSchedule);
            cancelButton.onClick.RemoveListener(SetCancel);
        }

        private void CancelParticipate()
        {
            if (_referenceTournamentData == null) return;
            if (_referenceTournamentData.IsTournamentParticipated) return;
            _referenceTournamentData.CancelJoinTournament();
            GameDataManager.OnTournamentDataChanged?.Invoke();
        }

        #region Tournament data
        public void SetTournamentData(ScriptableTournamentDetail tournamentDetailData, int targetYear, bool increaseByTime = true)
        {
            _tournamentDetailData = tournamentDetailData;
            _targetYear = targetYear;
            var tournamentScheduled = GameDataManager.Instance.LoadedPlayerData.PlayerTournamentList.FirstOrDefault(x => x.TournamentDetail.Equals(tournamentDetailData) && x.TournamentYear == _targetYear) ??
                                      new(string.Empty, tournamentDetailData, new List<string>(), _targetYear, false, false, false, true);
            // Set/unset schedule or cancel a schedule in player data
            foreach (var t in from t in GameDataManager.Instance.LoadedPlayerData.PlayerTournamentList
                     let playerTournament = t
                     where playerTournament.TournamentYear == _targetYear &&
                           playerTournament.TournamentDetail == tournamentDetailData
                     select t)
            {
                tournamentScheduled = t;
                scheduleImage.gameObject.SetActive(tournamentScheduled.IsTournamentScheduled);
                cupImage.gameObject.SetActive(tournamentScheduled.IsTournamentWon);
            }

            _referenceTournamentData = tournamentScheduled;

            participateButton.Button.interactable = true;
            _targetYear = GameProgressManager.Instance.CurrentGameYear;
            if (tournamentDetailData.PerformMonth < GameProgressManager.Instance.CurrentGameMonth && increaseByTime)
            {
                _targetYear++;
            }
            tournamentName.text = tournamentDetailData.TournamentName;
            tournamentYearText.text = $" {_targetYear}";
            tournamentMonthText.text = $" {tournamentDetailData.PerformMonth}";
            tournamentWeekText.text = $" {tournamentDetailData.PerformWeek}";
            tournamentRankText.text = tournamentDetailData.TournamentRank.ToString();
            tournamentRankBackground.color = GameComponentsSettingManager.Instance.GetRankColor(tournamentDetailData.TournamentRank);
            basicBitPrize.text = $"{Mathf.RoundToInt(tournamentDetailData.BasicBitPrize):N0}";
            ticketBitPrize.text = $"{Mathf.RoundToInt(tournamentDetailData.TicketBitPrize):N0}";

            scheduleImage.gameObject.SetActive(false);
            cupImage.gameObject.SetActive(false);

            if (tournamentDetailData.BasicRewardItem != null)
            {
                var itemName = tournamentDetailData.BasicRewardItem.ItemName;
                basicItemNameText.text = $"{itemName}";
                itemPrizeIcon.sprite = tournamentDetailData.BasicRewardItem.ItemGraphic;
                itemRank.sprite = GameComponentsSettingManager.Instance.GetItemQualityIcon(tournamentDetailData.BasicRewardItem.ItemRarity);
                itemRankBackground.sprite = GameComponentsSettingManager.Instance.GetItemQualityBackground(tournamentDetailData.BasicRewardItem.ItemRarity);
                System.Text.StringBuilder description = new();
                description.AppendLine($"{tournamentDetailData.BasicRewardItem.ItemDescription}");
                description.AppendLine(tournamentDetailData.BasicRewardItem.SupplyAmountString);
                basicItemDescriptionText.text = description.ToString();

                emptyPrize.SetActive(false);
            }
            else
            {
                emptyPrize.SetActive(true);
            }

            if (tournamentDetailData.TicketRewardItem != null)
            {
                var itemName = tournamentDetailData.TicketRewardItem.ItemName;
                ticketItemNameText.text = $"{itemName}";
                itemTicketPrizeIcon.sprite = tournamentDetailData.TicketRewardItem.ItemGraphic;
                itemTicketRank.sprite = GameComponentsSettingManager.Instance.GetItemQualityIcon(tournamentDetailData.TicketRewardItem.ItemRarity);
                itemTicketRankBackground.sprite = GameComponentsSettingManager.Instance.GetItemQualityBackground(tournamentDetailData.TicketRewardItem.ItemRarity);
                System.Text.StringBuilder description = new();
                description.AppendLine($"{tournamentDetailData.TicketRewardItem.ItemDescription}");
                description.AppendLine(tournamentDetailData.TicketRewardItem.SupplyAmountString);
                ticketItemDescriptionText.text = description.ToString();

                emptyTicketPrize.SetActive(false);
            }
            else
            {
                emptyTicketPrize.SetActive(true);
            }
            ValidateParticipant();

            if (!string.IsNullOrEmpty(tournamentDetailData.TournamentConditionDescription))
                if (I2.Loc.LocalizationManager.TryGetTranslation(tournamentDetailData.TournamentConditionDescription, out var trans))
                {
                    tournamentConditionText.text = trans;
                    return;
                }
            tournamentConditionText.text = "-";
        }

        private void ReloadData()
        {
            SetTournamentData(_tournamentDetailData, _targetYear);
        }

        private void GetTicket()
        {
            var totalTicketCount = 0;
            if (GameDataManager.Instance.LoadedPlayerData.PlayerItemDict.TryGetValue("TOURNAMENT_TICKET_UC", out var highQualityTicket))
            {
                totalTicketCount += highQualityTicket.Quantity;
            }
            if (GameDataManager.Instance.LoadedPlayerData.PlayerItemDict.TryGetValue("TOURNAMENT_TICKET_B", out var lowQualityTicket))
            {
                totalTicketCount += lowQualityTicket.Quantity;
            }
            ticketLeftText.text = $"1/{totalTicketCount}";
            ticketToggle.interactable = totalTicketCount > 0;
            ticketToggle.isOn = false;
        }

        private void SetSchedule()
        {
            foreach (var tournamentData in GameDataManager.Instance.LoadedPlayerData.PlayerTournamentList.AsSpan())
            {
                if (_targetYear != tournamentData.TournamentYear || tournamentData.TournamentDetail != _tournamentDetailData) continue;
                tournamentData.TournamentParticipateList.Clear();
                tournamentData.ScheduleTournament(_tournamentDetailData, _targetYear);
                GameDataManager.OnTournamentDataChanged?.Invoke();
                return;
            }
            SerializeTournament scheduleTournament = new();
            scheduleTournament.ScheduleTournament(_tournamentDetailData, _targetYear);
            scheduleTournament.TournamentParticipateList.Clear();
            GameDataController.AddTournament(scheduleTournament);
        }

        private void SetCancel()
        {
            foreach (var tournamentData in GameDataManager.Instance.LoadedPlayerData.PlayerTournamentList.AsSpan())
            {
                if (_targetYear != tournamentData.TournamentYear ||
                    tournamentData.TournamentDetail != _tournamentDetailData) continue;
                tournamentData.CancelTournamentSchedule();
                GameDataManager.OnTournamentDataChanged?.Invoke();
                return;
            }
        }
        #endregion

        private void ParticipateTournament()
        {
            TournamentDataManager.Instance.SelectTournament(_tournamentDetailData, ticketToggle.isOn);
            GameProgressManager.OnPvETournamentUpdatedEvent?.Invoke();
            _referenceTournamentData.ParticipateTournament(_tournamentDetailData, _targetYear);
            SwitchScene();
        }

        private async void SwitchScene()
        {
            var currentRoom = CustomNetworkManager.Instance.GetCurrentRoom();
            if (currentRoom != null)
            {
                await CustomNetworkManager.Instance.LeaveRoom();
            }
            
            var existTournament = GameDataManager.Instance.LoadedPlayerData.PlayerTournamentList.Where(x => x.TournamentDetail == _referenceTournamentData.TournamentDetail && x.TournamentYear == _referenceTournamentData.TournamentYear).ToList();

            if (!existTournament.Any())
            {
                GameDataManager.Instance.LoadedPlayerData.PlayerTournamentList.Add(_referenceTournamentData);
            }
            GameDataManager.OnTournamentDataChanged?.Invoke();
            var tournamentDetail = TournamentDataManager.Instance.SelectedTournament;
            CustomPvpModeAI.Instance.ResetData();
            CustomPvpModeAI.Instance.BattleRank = tournamentDetail.TournamentRank.ToString();
            CustomPvpModeAI.Instance.IsTournamentMode = true;
            CustomPvpModeAI.Instance.ListMonsterInFarm = new List<MonsterInFarm>();
            
            foreach (var tournamentMonster in tournamentMonsterSlot.Where(tournamentMonster => tournamentMonster.IsAvailable && tournamentMonster.MonsterInfo != null))
            {
                CustomPvpModeAI.Instance.ListMonsterInFarm.Add(tournamentMonster.MonsterInfo);
            }

            if (CustomPvpModeAI.Instance.ListMonsterInFarm.Count > 0)
            {
                SceneHelper.Instance.LoadScene(2, 1);
            }
            else
            {
                UIPopupPanel.Instance.SetupConfirm("Tournament Warning", "No monsters participate in the tournament!",UIPopupPanel.Instance.CloseMenu);
                UIPopupPanel.Instance.OpenMenu();
            }
        }

        #region Menu mode
        private void ScheduledTournamentMode()
        {
            scheduleButton.gameObject.SetActive(true);
            cancelButton.gameObject.SetActive(false);
            participateButton.gameObject.SetActive(false);
            participateButton.Button.interactable = false;
            endedButton.SetActive(false);
            foreach (var t in tournamentMonsterSlot)
            {
                t.HideSlotBtn();
            }
        }

        private void CancelTournamentMode()
        {
            scheduleButton.gameObject.SetActive(false);
            cancelButton.gameObject.SetActive(true);
            participateButton.gameObject.SetActive(false);
            endedButton.SetActive(false);
            foreach (var t in tournamentMonsterSlot)
            {
                t.HideSlotBtn();
            }
        }

        private void ParticipateTournamentMode()
        {
            scheduleButton.gameObject.SetActive(false);
            cancelButton.gameObject.SetActive(false);
            participateButton.gameObject.SetActive(true);
            endedButton.SetActive(false);
        }

        private List<MonsterInFarm> _listMonsterInFarm ;
        
        public void ValidateParticipant()
        {
            var farmSetup = GameDataManager.Instance.LoadedPlayerData.PlayerFarmSetupList.FirstOrDefault(x => x.IsSelected);
            var monsterSetList = GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList.Where(x => farmSetup != null && farmSetup.FarmSet.Any(y => y.MonsterId == x.MonsterId)).ToList();
           
            _listMonsterInFarm = new List<MonsterInFarm>();
            
            if (farmSetup != null)
            {
                foreach (var monsterInFarm in monsterSetList.Select(monster => new MonsterInFarm(farmSetup.FarmSet.FirstOrDefault(y => y.MonsterId == monster.MonsterId)?.FarmId,monster)))
                {
                    _listMonsterInFarm.Add(monsterInFarm);
                }
            }

            var existTournamentWeeks = GameDataManager.Instance.LoadedPlayerData.PlayerTournamentList.Where(x => x.TournamentDetail.PerformWeek == _referenceTournamentData.TournamentDetail.PerformWeek && x.TournamentDetail.PerformMonth == _referenceTournamentData.TournamentDetail.PerformMonth && x.TournamentYear == _referenceTournamentData.TournamentYear).ToList();
            _listMonsterInFarm = ValidateMonsterRank(_listMonsterInFarm, _referenceTournamentData);
            _listMonsterInFarm = ValidateParticipateList(_listMonsterInFarm, existTournamentWeeks);
            _listMonsterInFarm = ValidateSpecialTournamentList(_listMonsterInFarm, _referenceTournamentData);
            ValidateMonsterSlot(_listMonsterInFarm, _referenceTournamentData);
            ValidateTournamentButton();
        }

        private List<MonsterInFarm> ValidateMonsterRank(List<MonsterInFarm> monsterList, SerializeTournament referenceTournament)
        {
            return monsterList.Where(monster => monster.MonsterInfo.MonsterRank == referenceTournament.TournamentDetail.TournamentRank).ToList();
        }

        private List<MonsterInFarm> ValidateParticipateList(List<MonsterInFarm> monsterList, List<SerializeTournament> tournaments)
        {
            List<MonsterInFarm> participateList = new();
            foreach (var monster in monsterList.Where(monster => !(monster.MonsterInfo.ComplexLifespan <= 0) && !monster.MonsterInfo.IsMemory))
            {
                if (tournaments.Count > 0)
                {
                    foreach (var unused in tournaments.Where(tournament => !tournament.IsTournamentParticipated || !tournament.TournamentParticipateList.Exists(x => x == monster.MonsterInfo.MonsterId)).Where(tournament => !participateList.Exists(x => x.MonsterInfo.MonsterId == monster.MonsterInfo.MonsterId)))
                    {
                        participateList.Add(monster);
                    }
                }
                else
                {
                    participateList.Add(monster);
                }
            }
            return participateList;
        }

        private List<MonsterInFarm> ValidateSpecialTournamentList(List<MonsterInFarm> monsters, SerializeTournament tournament)
        {
            List<MonsterInFarm> participateList = new();
            foreach (var monster in monsters)
            {
                switch (tournament.TournamentDetail.TournamentId)
                {
                    case "1_4_S":
                        if (monster.MonsterInfo.IsMonsterGrandPrixClear && monster.MonsterInfo.IsMonsterCrownCupClear && monster.MonsterInfo.IsSantuaryCupClear && monster.MonsterInfo.IsWinnerCupClear)
                            participateList.Add(monster);
                        break;
                    case "2_3_S":
                    case "5_3_S":
                    case "8_3_S":
                    case "11_3_S":
                        if (monster.MonsterInfo.IsRankSOfficalClear)
                            participateList.Add(monster);
                        break;
                    default:
                        participateList.Add(monster);
                        break;
                }
            }
            return participateList;
        }

        private void TournamentEndedMode()
        {
            scheduleButton.gameObject.SetActive(false);
            cancelButton.gameObject.SetActive(false);
            participateButton.gameObject.SetActive(false);
            endedButton.SetActive(true);
            foreach (var t in tournamentMonsterSlot)
            {
                t.HideSlotBtn();
            }
        }

        private void ValidateTournamentButton()
        {
            if (_referenceTournamentData.TournamentDetail.PerformMonth < GameProgressManager.Instance.CurrentGameMonth)
            {
                if (_referenceTournamentData.IsTournamentScheduled)
                {
                    CancelTournamentMode();
                    return;
                }
                ScheduledTournamentMode();
                return;
            }
            if (_referenceTournamentData.TournamentDetail.PerformMonth == GameProgressManager.Instance.CurrentGameMonth)
            {
                if (_referenceTournamentData.TournamentDetail.PerformWeek < GameProgressManager.Instance.CurrentGameWeek)
                {
                    TournamentEndedMode();
                    return;
                }
                if (_referenceTournamentData.TournamentDetail.PerformWeek == GameProgressManager.Instance.CurrentGameWeek)
                {
                    ParticipateTournamentMode();
                    if (_referenceTournamentData.IsTournamentParticipated)
                    {
                        TournamentEndedMode();
                    }
                    else
                    {
                        participateButton.Button.interactable = _referenceTournamentData.TournamentParticipateList.Count > 0;
                    }
                    return;
                }
                if (_referenceTournamentData.TournamentDetail.PerformWeek > GameProgressManager.Instance.CurrentGameWeek)
                {
                    if (_referenceTournamentData.IsTournamentScheduled)
                    {
                        CancelTournamentMode();
                        return;
                    }
                    ScheduledTournamentMode();
                }
            }

            if (_referenceTournamentData.TournamentDetail.PerformMonth <= GameProgressManager.Instance.CurrentGameMonth) return;
            if (_referenceTournamentData.IsTournamentScheduled)
            {
                CancelTournamentMode();
                return;
            }
            ScheduledTournamentMode();
        }

        private void ValidateMonsterSlot(List<MonsterInFarm> qualifyMonsters , SerializeTournament tournament)
        {
            var slotCount = 0;
            foreach (var monsterData in qualifyMonsters.TakeWhile(t => slotCount < tournamentMonsterSlot.Count))
            {
                tournamentMonsterSlot[slotCount].SetSlotData(FarmAreaManager.Instance.GetFullscreenSprite(monsterData.MonsterInfo.MonsterScriptableData.MonsterMainTerrainComp), monsterData, tournament, this);
                slotCount++;
            }
            for (var i = slotCount; i < tournamentMonsterSlot.Count; i++)
            {
                tournamentMonsterSlot[i].SetSlotData();
            }
        }
        #endregion
    }
}