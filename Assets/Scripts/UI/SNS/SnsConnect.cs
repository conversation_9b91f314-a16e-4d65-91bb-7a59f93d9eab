using System;
using System.Collections;
using System.Runtime.InteropServices;
using System.Xml.Linq;
using Facebook.Unity;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.UI;

public class SnsConnect : MonoBehaviour
{
    [SerializeField] private string _tweetMessage;
    [SerializeField] private string _imgurClientId = "2e40d5b7333c39c";
    [SerializeField] private Button fbBtn;
    [SerializeField] private Button xBtn;
#if !UNITY_EDITOR && UNITY_WEBGL
    [DllImport("__Internal")]
    private static extern string TweetFromUnity(string rawMessage);
    [DllImport("__Internal")]
    private static extern void ImageDownloader(string str, string fn);
#endif
    [DllImport("__Internal")]
    public static extern void DownloadFile(byte[] array, int byteLength, string fileName);
 
    private IEnumerator RecordUpscaledFrame(int screenshotUpscale)
    {
        yield return new WaitForEndOfFrame();
        int resWidthN = Camera.main is not null ? Camera.main.pixelWidth * screenshotUpscale : 1920;
        int resHeightN = Camera.main is not null ? Camera.main.pixelHeight * screenshotUpscale : 1080;
        string dateFormat = "yyyy-MM-dd-HH-mm-ss";
        string filename = resWidthN + "x" + resHeightN + "px_" + System.DateTime.Now.ToString(dateFormat);
        Texture2D screenShot = ScreenCapture.CaptureScreenshotAsTexture(screenshotUpscale);
        byte[] texture = screenShot.EncodeToPNG();
        DownloadFile(texture, texture.Length, filename + ".png");
        Destroy(screenShot);
    }

 
    private string imageFilename;
    private string _imgUrl;

    private void OnEnable()
    {
        fbBtn.onClick.AddListener(ShareFb);
        xBtn.onClick.AddListener(TweetWithScreenshot);
    }

    private void OnDisable()
    {
        fbBtn.onClick.RemoveListener(ShareFb);
        xBtn.onClick.RemoveListener(TweetWithScreenshot);
    }

    private void Awake()
    {
        if (!FB.IsInitialized)
        {
            // Initialize the Facebook SDK
            FB.Init(InitCallback, OnHideUnity);
        }
        else
        {
            // Already initialized, signal an app activation App Event
            FB.ActivateApp();
        }
    }

    private void Tweet()
    {
        _tweetMessage += "%0a%0a%23remonster%0ahttps://remonster.world";
        StartCoroutine(RecordUpscaledFrame(1));
#if !UNITY_EDITOR && UNITY_WEBGL
        TweetFromUnity(_tweetMessage);        
        return;
        var tex = ScreenCapture.CaptureScreenshotAsTexture();
        imageFilename = $"remonster{DateTime.UtcNow.ToString("MMddyyHHmmss")}";
        if (tex == null) return;
        Debug.Log("Downloading..."+imageFilename);
        ImageDownloader(Convert.ToBase64String(tex.GetRawTextureData()), imageFilename);
        TweetFromUnity(_tweetMessage);
        return;
#endif
        Application.OpenURL($"https://twitter.com/intent/tweet?text={_tweetMessage}");
    }

    public void TweetWithScreenshot() => Tweet();

    private IEnumerator TweetWithScreenshotCo()
    {
        yield return ScreenShot();
        _imgUrl = "https://remonster.world";
#if !UNITY_EDITOR && UNITY_WEBGL
            TweetFromUnity($"{_tweetMessage}%0a{_imgUrl}");
            yield break;
#endif
        Application.OpenURL($"https://twitter.com/intent/tweet?text={_tweetMessage}%0a{_imgUrl}");
    }

    private IEnumerator ScreenShot()
    {
        _tweetMessage += "%0a%0a%23remonster";
        yield return new WaitForEndOfFrame();
        var tex = ScreenCapture.CaptureScreenshotAsTexture();
        var wwwForm = new WWWForm();
        wwwForm.AddField("image", Convert.ToBase64String(tex.EncodeToJPG()));
        wwwForm.AddField("type", "base64");
        // Upload to Imgur
        var www = UnityWebRequest.Post("https://api.imgur.com/3/image.xml", wwwForm);
        www.SetRequestHeader("AUTHORIZATION", "Client-ID " + _imgurClientId);
        yield return www.SendWebRequest();
        var uri = "";
        if (www.result == UnityWebRequest.Result.ConnectionError) yield break;
        var xDoc = XDocument.Parse(www.downloadHandler.text);
        uri = xDoc.Element("data")?.Element("link")?.Value;
        // Remove Ext
        uri = uri?.Remove(uri.Length - 4, 4);
        _imgUrl = uri;
#if !UNITY_EDITOR && UNITY_WEBGL
        imageFilename = $"remonster{DateTime.UtcNow.ToString("MMddyyHHmmss")}";
        if (tex == null) yield break;
        Debug.Log("Downloading..."+imageFilename);
        ImageDownloader(Convert.ToBase64String(tex.GetRawTextureData()), imageFilename);
#endif
    }


    public void DownloadScreenshot(Texture2D imageData)
    {
        
    }

    public void ShareFb() => StartCoroutine(ShareFacebook());

    private IEnumerator ShareFacebook()
    {
        // yield return ScreenShot();
        yield return RecordUpscaledFrame(1);
        _imgUrl = "https://remonster.world";
        FB.ShareLink(new Uri(_imgUrl), callback: ShareCallback);
        FB.FeedShare();
    }

    private void InitCallback()
    {
        if (FB.IsInitialized)
        {
            // Signal an app activation App Event
            FB.ActivateApp();
            // Continue with Facebook SDK
            // ...
        }
        else
        {
            Debug.Log("Failed to Initialize the Facebook SDK");
        }
    }

    private void OnHideUnity(bool isGameShown)
    {
        // Pause the game - we will need to hide  // Resume the game - we're getting focus again
        Time.timeScale = !isGameShown ? 0 : 1;
    }

    
    private void ShareCallback (IShareResult result) 
    {
        if (result.Cancelled || !String.IsNullOrEmpty(result.Error)) {
            Debug.Log("ShareLink Error: "+result.Error);
        } else if (!string.IsNullOrEmpty(result.PostId)) {
            // Print post identifier of the shared content
            Debug.Log(result.PostId);
        } else {
            // Share succeeded without postID
            Debug.Log("ShareLink success!");
        }
    }
}