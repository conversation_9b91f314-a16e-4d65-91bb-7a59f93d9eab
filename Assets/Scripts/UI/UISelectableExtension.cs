using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.UI
{
    public class UISelectableExtension : MonoBehaviour
    {
        private Selectable selectableObject;
        [SerializeField] private List<TextMeshProUGUI> buttonTextList = new();
        [SerializeField] private List<Image> subImageList = new();
        [SerializeField] private List<Image> textImage = new();
        [SerializeField] private Color enableColorText;
        [SerializeField] private Color disabledColorText;
        [SerializeField] private Color enableColorOutline;
        [SerializeField] private Color disabledColorOutline;

        bool isInteractable = true;
        public Selectable SelectableObject
        {
            get
            {
                if (selectableObject == null)
                {
                    selectableObject = GetComponent<Selectable>();
                }
                return selectableObject;
            }
        }

        private void Awake()
        {
            selectableObject = GetComponent<Selectable>();
        }

        private void Update()
        {
            if (isInteractable != selectableObject.interactable)
            {
                isInteractable = selectableObject.interactable;
                UpdateComponent();
            }
        }

        private void UpdateComponent()
        {
            for (int i = 0; i < buttonTextList.Count; i++)
            {
                buttonTextList[i].color = isInteractable ? enableColorText : disabledColorText;
            }
            for (int i = 0; i < subImageList.Count; i++)
            {
                subImageList[i].color = isInteractable ? enableColorOutline : disabledColorOutline;
            }
            for (int i = 0; i < textImage.Count; i++)
            {
                textImage[i].color = isInteractable ? enableColorText : disabledColorText;
            }
        }
    }
}