using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Utilities
{
    public class UIToggleSelectableExtension : MonoBehaviour
    {
        private Toggle _selectableObject;
        [SerializeField] private List<TextMeshProUGUI> buttonTextList = new();
        [SerializeField] private List<Image> subImageList = new();
        [SerializeField] private List<Image> textImage = new();

        [SerializeField] private Color toggleOnColorText;
        [SerializeField] private Color toggleOffColorText;
        [SerializeField] private Color toggleOnColorOutline;
        [SerializeField] private Color toggleOffColorOutline;
        
        private void OnEnable()
        {
            _selectableObject = GetComponent<Toggle>();
            _selectableObject.onValueChanged.AddListener(OnToggleValueChanged);
            UpdateComponent();
        }

        private void OnToggleValueChanged(bool isOn)
        {
            UpdateComponent();
        }

        private void UpdateComponent()
        {
            var textColor = _selectableObject.isOn ? toggleOnColorText : toggleOffColorText;
            var outlineColor = _selectableObject.isOn ? toggleOnColorOutline : toggleOffColorOutline;

            if (_selectableObject.interactable)
            {
                foreach (var t in buttonTextList)
                {
                    t.color = textColor ;
                }
                foreach (var t in subImageList)
                {
                    t.color = outlineColor ;
                }
                foreach (var t in textImage)
                {
                    t.color = textColor;
                }
            }
            else
            {
                foreach (var t in buttonTextList)
                {
                    t.color = toggleOffColorText ;
                }
                foreach (var t in subImageList)
                {
                    t.color = toggleOffColorOutline ;
                }
                foreach (var t in textImage)
                {
                    t.color = toggleOffColorText;
                }
            }
       
        }

        private void OnDisable()
        {
            _selectableObject.onValueChanged.RemoveListener(OnToggleValueChanged);
        }
    }
}