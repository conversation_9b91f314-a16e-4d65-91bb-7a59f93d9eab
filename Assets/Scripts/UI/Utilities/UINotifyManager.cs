using Cysharp.Threading.Tasks;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Assets.Scripts.UI.Utilities
{
    public class UINotifyManager
    {
        private static bool isInit = false;
        private static NoticeQueueProcessor noticeQueue;

        public static void InitManager()
        {
            if (!isInit)
            {
                isInit = true;
                noticeQueue = new NoticeQueueProcessor();
            }
        }

        public static void ClearInit()
        {
            if (isInit)
            {
                isInit = false;
                noticeQueue = new NoticeQueueProcessor();
            }
        }

        public static void ClearAll()
        {
            noticeQueue = new();
        }

        public static void AddNotifyResponse(string noticeTitle, Action onCall, NotifyType type, int insertOrder = 0)
        {
            noticeQueue ??= new();
            noticeQueue.EnqueueNotice(noticeTitle, onCall, (int)type, insertOrder);
        }

        public static bool FindQueue(string noticeTitle)
        {
            if (noticeQueue == null)
                return false;
            return noticeQueue.FindQueue(noticeTitle);
        }

        public static void ProcessNotify()
        {
            noticeQueue.ProcessNextNotice();
        }

        public static async void ProcessNotifyAsync(float delayTime)
        {
            await UniTask.WaitForSeconds(delayTime);
            noticeQueue.ProcessNextNotice();
        }

        public static void RemoveNotify(string notify)
        {
            noticeQueue.RemoveNotice(notify);
        }

        internal static void OrderDebug()
        {
            foreach (var notice in noticeQueue.NoticeQueue)
            {
                Debug.Log(notice.Key + " " + notice.Value);
            }
        }

        public enum NotifyType
        {
            ActionCallBack = 0,
            Tutorial = 1,
            System = 2,
            TraitNotice = 3,
            RaiseNotice = 4,
            TournamentNotice = 5,
            DefaultNotify = 6,
        }

        internal struct Notice : IComparable<Notice>
        {
            public string NoticeTitle { get; set; }
            public Action CallAction { get; set; }
            public int Priority { get; set; }
            public int InsertOrder { get; set; }

            public int CompareTo(Notice other)
            {
                if (Priority != other.Priority)
                    return other.Priority.CompareTo(Priority);
                else
                    return other.InsertOrder.CompareTo(InsertOrder);
            }
        }

        internal class NoticeQueueProcessor
        {
            private SortedDictionary<Notice, string> noticeQueue = new SortedDictionary<Notice, string>();

            public SortedDictionary<Notice, string> NoticeQueue { get => noticeQueue; set => noticeQueue = value; }

            // Add a notice to the queue with specified priority
            public void EnqueueNotice(string noticeTitle, Action callAction, int priority, int insertOrder)
            {
                Notice notice = new Notice
                {
                    NoticeTitle = noticeTitle,
                    CallAction = callAction,
                    Priority = priority,
                    InsertOrder = insertOrder
                };
                Debug.Log("Queue notify " + noticeTitle);
                try
                {
                    noticeQueue.Add(notice, noticeTitle);
                }
                catch (Exception e)
                {
                    Debug.LogWarning(e);
                }
            }

            // Process notices in the queue
            public void ProcessNextNotice()
            {
                if (noticeQueue.Count > 0)
                {
                    Notice noticeEntry = noticeQueue.Keys.LastOrDefault();
                    Debug.Log(noticeEntry.NoticeTitle);
                    Notice notice = noticeEntry;
                    if (!string.IsNullOrEmpty(notice.NoticeTitle))
                    {
                        notice.CallAction?.Invoke();
                    }

                    // Remove the processed notice from the queue
                    noticeQueue.Remove(notice);
                }
                else
                {
                    Debug.Log("No notices to process.");
                }
            }

            public void RemoveNotice(string noticeTitle)
            {
                Notice noticeEntry = noticeQueue.Keys.FirstOrDefault(x => x.NoticeTitle == noticeTitle);
                Notice notice = noticeEntry;
                if (!string.IsNullOrEmpty(notice.NoticeTitle))
                {
                    // Remove the processed notice from the queue
                    noticeQueue.Remove(notice);
                }
                else
                {
                    Debug.Log("No notices to process.");
                }
            }

            public bool FindQueue(string noticeTitle)
            {
                Notice noticeEntry = noticeQueue.Keys.FirstOrDefault(x => x.NoticeTitle.Equals(noticeTitle));
                return !string.IsNullOrEmpty(noticeEntry.NoticeTitle);
            }
        }
    }
}