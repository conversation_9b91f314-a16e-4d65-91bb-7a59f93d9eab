using System;
using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.DataStruct;
using Assets.Scripts.Tournament;
using DG.Tweening;
using Network.TournamentState;
using Sponsored_Tournament;
using TMPro;
using UI.BattleUI;
using UnityEngine;
using UnityEngine.UI;

namespace UI.UI_Sponsored_Tournament
{
    public class SponsorTournamentEl : MonoBehaviour
    {
        [SerializeField] private RectTransform winnerCup;
        [SerializeField] private List<EliminationUiData> eliminationUiDatas;

        [SerializeField] private Toggle rightButton;
        [SerializeField] private Toggle leftButton;
        [SerializeField] private RectTransform containerPos;
        [SerializeField] private Vector2 leftPosition;
        [SerializeField] private Vector2 rightPosition;
        [SerializeField] private TextMeshProUGUI roundText;
        [SerializeField] private TournamentInProgressBottom progressStatus;
        [SerializeField] private Vector3[] scales;

        private List<EliminationMatch> _matches = new();

        private RectTransform _scaleThis;

        private void OnEnable()
        {
            _scaleThis = gameObject.GetComponent<RectTransform>();
            rightButton.onValueChanged.AddListener(value =>
            {
                leftButton.interactable = value;
                rightButton.interactable = !value;
                containerPos.DOAnchorPos(rightPosition, 0.5f).SetEase(Ease.OutBack);
            });
            leftButton.onValueChanged.AddListener(value =>
            {
                leftButton.interactable = !value;
                rightButton.interactable = value;
                containerPos.DOAnchorPos(leftPosition, 0.5f).SetEase(Ease.OutBack);
            });

            ActiveObject(3, false);

            foreach (var data in eliminationUiDatas) data.winLines.gameObject.SetActive(false);
        }

        private void OnDisable()
        {
            rightButton.onValueChanged.RemoveAllListeners();
            leftButton.onValueChanged.RemoveAllListeners();
        }

        public void InitItem(SerializeSponsoredTournament tournament, TournamentState tournamentState,
            SponsorTournamentResult result, SponsoredTournamentInfo info)
        {
            leftButton.isOn = true;
            rightButton.gameObject.SetActive(false);
            leftButton.gameObject.SetActive(false);
            progressStatus.ActiveFalse();
            var playerCount = tournamentState is null ? tournament.entry_list.Count : tournamentState.entry_list.Count;
            var participantNumber = playerCount switch
            {
                2 => 0,
                3 or 4 => 1,
                5 or 6 or 7 or 8 => 2,
                _ => 3
            };
            _scaleThis.localScale = participantNumber switch
            {
                0 => scales[0],
                1 => scales[1],
                _ => scales[2]
            };
            winnerCup.anchoredPosition = eliminationUiDatas[participantNumber].cupPosition;
            foreach (var linePos in eliminationUiDatas[participantNumber].lines) linePos.gameObject.SetActive(true);
            if (playerCount > 8)
            {
                rightButton.gameObject.SetActive(true);
                leftButton.gameObject.SetActive(true);
            }

            var round0Count = tournamentState is null
                ? tournament.match_list.Where(l => l.round_id == 0).ToList().Count
                : tournamentState.match_list.items.Values.Where(l => l.round_id == 0).ToList().Count;
            for (var i = 0; i < round0Count; i++)
            {
                eliminationUiDatas[participantNumber].players[2 * i].gameObject.SetActive(true);
                eliminationUiDatas[participantNumber].players[2 * i].SetData(tournamentState is null
                    ? tournament.match_list[i].player_1
                    : tournamentState.match_list[i].player_1);
                eliminationUiDatas[participantNumber].players[2 * i + 1].gameObject.SetActive(true);
                eliminationUiDatas[participantNumber].players[2 * i + 1].SetData(tournamentState is null
                    ? tournament.match_list[i].player_2
                    : tournamentState.match_list[i].player_2);
            }

            if (tournamentState is null)
            {
                progressStatus.TournamentEnded(tournament, null, this, null, result, info);
                foreach (var match in tournament.match_list)
                {
                    var matchData = new EliminationMatch(match.player_1, match.player_2, match.match_id, match.state,
                        eliminationUiDatas[participantNumber].inBattle[match.match_id].GetComponent<Button>(),
                        eliminationUiDatas[participantNumber].lines[2 * match.match_id],
                        eliminationUiDatas[participantNumber].lines[2 * match.match_id + 1],
                        eliminationUiDatas[participantNumber].winLines,
                        eliminationUiDatas[participantNumber].losss[2 * match.match_id],
                        eliminationUiDatas[participantNumber].losss[2 * match.match_id + 1]);
                    SetMatchStateOffline(match, matchData);
                    if (match.state > 1) roundText.text = $"Round {match.round_id + 1}";
                }
            }
            else
            {
                foreach (var match in tournamentState.match_list.items.Values)
                {
                    var matchData = new EliminationMatch(match.player_1, match.player_2, match.match_id, match.state,
                        eliminationUiDatas[participantNumber].inBattle[match.match_id].GetComponent<Button>(),
                        eliminationUiDatas[participantNumber].lines[2 * match.match_id],
                        eliminationUiDatas[participantNumber].lines[2 * match.match_id + 1],
                        eliminationUiDatas[participantNumber].winLines,
                        eliminationUiDatas[participantNumber].losss[2 * match.match_id],
                        eliminationUiDatas[participantNumber].losss[2 * match.match_id + 1]);
                    SetMatchStateOnline(match, matchData);
                    if (match.state > 1) roundText.text = $"Round {match.round_id + 1}";
                }
                if(tournamentState.state == 5) progressStatus.TournamentEnded(tournament, tournamentState, this, null, result, info);
            }
        }

        private void SetMatchStateOnline(TournamentMatch match, EliminationMatch matchData)
        {
            matchData.MatchState = match.state;
            switch (match.state)
            {
                case 0:
                    matchData.Player1Line.color =new Color(0.047f, 0.784f, 0.996f);
                    matchData.WinLine.gameObject.SetActive(true);
                    matchData.WinLine.color = new Color(0.047f, 0.784f, 0.996f);
                    break;
                case 2 or 3:
                    matchData.InprogressBtn.gameObject.SetActive(true);
                    matchData.InprogressBtn.onClick.AddListener(() =>
                    {
                        progressStatus.InitInprogressImage(match.player_1, match.player_2, match.room_id);
                    });
                    matchData.WinLine.gameObject.SetActive(false);
                    matchData.WinLine.color = Color.white;
                    break;
                case 4 or 5:
                    matchData.Player1Loss.gameObject.SetActive(match.winner == match.player_2_id);
                    matchData.Player2Loss.gameObject.SetActive(match.winner == match.player_1_id);
                    matchData.Player1Line.color =
                        match.winner == match.player_2_id ? Color.white : new Color(0.047f, 0.784f, 0.996f);
                    matchData.Player2Line.color =
                        match.winner == match.player_1_id ? Color.white : new Color(0.047f, 0.784f, 0.996f);
                    matchData.InprogressBtn.gameObject.SetActive(false);
                    matchData.WinLine.gameObject.SetActive(true);
                    matchData.WinLine.color = new Color(0.047f, 0.784f, 0.996f);
                    break;
            }
        }

        private void SetMatchStateOffline(TournamentMatchData match, EliminationMatch matchData)
        {
            switch (match.state)
            {
                case 2 or 3:
                    matchData.InprogressBtn.gameObject.SetActive(true);
                    matchData.InprogressBtn.onClick.AddListener(() =>
                    {
                        // Do sth
                    });
                    matchData.WinLine.color = Color.white;
                    matchData.WinLine.gameObject.SetActive(false);
                    break;
                case 4 or 5:
                    matchData.Player1Loss.gameObject.SetActive(match.winner == match.player_2_id);
                    matchData.Player2Loss.gameObject.SetActive(match.winner == match.player_1_id);
                    matchData.Player1Line.color =
                        match.winner == match.player_2_id ? Color.white : new Color(0.047f, 0.784f, 0.996f);
                    matchData.Player2Line.color =
                        match.winner == match.player_1_id ? Color.white : new Color(0.047f, 0.784f, 0.996f);
                    matchData.InprogressBtn.gameObject.SetActive(false);
                    matchData.WinLine.gameObject.SetActive(true);
                    matchData.WinLine.color = new Color(0.047f, 0.784f, 0.996f);
                    break;
            }
        }


        private void ActiveObject(int number, bool active)
        {
            foreach (var linePos in eliminationUiDatas[number].lines) linePos.gameObject.SetActive(active);
            foreach (var uiPlayerInfoInBattle in eliminationUiDatas[number].players)
                uiPlayerInfoInBattle.gameObject.SetActive(active);
            foreach (var icon in eliminationUiDatas[number].inBattle) icon.gameObject.SetActive(active);
            foreach (var icon in eliminationUiDatas[number].losss) icon.gameObject.SetActive(active);
        }

        [Serializable]
        private class EliminationUiData
        {
            public int playersCount;
            public Vector2 cupPosition;
            public CreatLineTournament[] lines;
            public CreatLineTournament winLines;
            public GameObject[] inBattle;
            public UserSetup[] players;
            public GameObject[] losss;
        }

        private class EliminationMatch
        {
            public readonly Button InprogressBtn;
            public readonly CreatLineTournament Player1Line;
            public readonly GameObject Player1Loss;
            public readonly CreatLineTournament Player2Line;
            public readonly GameObject Player2Loss;
            public readonly CreatLineTournament WinLine;
            public int MatchId;
            public string MatchLobbyId;
            public int MatchState;
            public string Player1;
            public string Player2;

            public EliminationMatch(string player1, string player2, int matchId, int matchState, Button inprogressBtn,
                CreatLineTournament player1Line, CreatLineTournament player2Line, CreatLineTournament winLine,
                GameObject player1Loss, GameObject player2Loss)
            {
                Player1 = player1;
                Player2 = player2;
                MatchId = matchId;
                MatchState = matchState;
                InprogressBtn = inprogressBtn;
                Player1Line = player1Line;
                Player2Line = player2Line;
                WinLine = winLine;
                Player1Loss = player1Loss;
                Player2Loss = player2Loss;
            }
        }
    }
}