using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(fileName = "New pve player data", menuName = "ScriptableObjects/Create pve player")]
public class PVEPlayerScriptableObject : ScriptableObject
{
    //[SerializeField] bool getData = false;

    [SerializeField] private string playerId;
    [SerializeField] private string playerName;
    [SerializeField] private Sprite playerAvatar;
    [SerializeField] RankTypeEnums rank;
    [SerializeField] private List<PVEMonsterScriptableObject> pveMonsters;

    public string PlayerId { get => playerId; set => playerId = value; }
    public RankTypeEnums Rank { get => rank; set => rank = value; }
    public string PlayerName { get => playerName; set => playerName = value; }
    public Sprite PlayerAvatar { get => playerAvatar; }
    public List<PVEMonsterScriptableObject> PVEMonsters { get => pveMonsters; set => pveMonsters = value; }

    void GetData()
    {
        PVEMonsters.Clear();

        PVEMonsterScriptableObject[] monsterDatas = Resources.LoadAll<PVEMonsterScriptableObject>("");

        Debug.Log("monsterDatas count = " + monsterDatas.Length);

        foreach(PVEMonsterScriptableObject data in monsterDatas)
        {
            if (data.name.ToLower().Contains(playerName.ToLower()) && data.SerializeMonster.MonsterRank == rank)
            {
                PVEMonsters.Add(data);
            }
        }
    }

#if UNITY_EDITOR
    private void OnValidate()
    {
        //if (getData)
        //{
        //    getData = false;

        //    GetData();
        //}

        playerId = playerName + "_" + rank.ToString().ToUpper();

        //string thisFileNewName = $"{playerId}";

        ////if (playerName != string.Empty)
        ////if (generateID)
        //{
        //    //generateID = false;

        //    string assetPath = UnityEditor.AssetDatabase.GetAssetPath(this.GetInstanceID());
        //    UnityEditor.AssetDatabase.RenameAsset(assetPath, thisFileNewName);
        //}
    }
#endif
}
