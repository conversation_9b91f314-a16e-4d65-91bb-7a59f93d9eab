using UnityEngine;

namespace Assets.Scripts.Scriptables.MonsterNFTLogo
{
    [CreateAssetMenu(fileName = "New NFT Logo", menuName = "ScriptableObjects/Nft Logo")]
    public class ScriptableMonsterNFTLogo : ScriptableObject
    {
        [SerializeField] private Enums.SeedsEnum seed;
        [SerializeField] private Sprite logo;
        [SerializeField] private string seedCollectionName;

        public Enums.SeedsEnum Seed => seed;
        public Sprite Logo => logo;
        public string SeedCollectionName => seedCollectionName;
    }
}