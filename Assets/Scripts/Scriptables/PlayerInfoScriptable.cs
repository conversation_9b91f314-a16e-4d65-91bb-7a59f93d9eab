using Assets.Scripts.Managers;
using Assets.Scripts.Scriptables.Traits;
using Assets.Scripts.SerializeDataStruct;
using Assets.Scripts.SerializeDataStruct.Data;
using Assets.Scripts.SerializeDataStruct.ItemData;
using Assets.Scripts.SerializeDataStruct.Traits;
using System;
using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.Enums;
using UnityEngine;
using Assets.Scripts.DataStruct;
using Random = UnityEngine.Random;
using Assets.Scripts.DataStruct.Data;
using Cysharp.Threading.Tasks;

namespace Assets.Scripts.Scriptables
{
    [CreateAssetMenu(fileName = "Player Info", menuName = "ScriptableObjects/Data/Player Info")]
    public class PlayerInfoScriptable : ScriptableObject
    {
        #region Private variables

        [SerializeField] private string m_playerId;
        [SerializeField] private string m_userId;
        [SerializeField] private string m_playerName;
        [SerializeField] private string m_playerWallet;
        [SerializeField] private string m_token;
        [SerializeField, Multiline] private string m_playerBio;
        [SerializeField] private float m_playerBit;
        [SerializeField] private float m_playerMaPoint;
        [SerializeField] private float m_playerOasFloat;
        [SerializeField] private string m_playerOas;
        [SerializeField] private string m_playerAvatar;
        [SerializeField] private string m_playerPhone;
        [SerializeField] private string m_playerEmail;
        [SerializeField] private string m_playerX;
        [SerializeField] private bool m_isPlayerEmailVerified;
        [SerializeField] private bool m_isPlayerPhoneVerified;
        [SerializeField] private int m_total_login_day;
        [SerializeField] private int m_player_time;
        [SerializeField] private string m_player_language;
        [SerializeField] private DateTime m_crt_dt;
        [SerializeField] private Texture2D m_avatarImage;
        [SerializeField] private bool m_bannedChat;
        [SerializeField] private Sprite nullAvaSprite;

        [SerializeField] private List<SerializeMonster> m_playerMonsterList;
        [SerializeField] private List<SerializeFarm> m_playerFarmList;
        [SerializeField] private List<SerializeItem> m_playerItemList;
        [SerializeField] private List<SerializeTournament> m_playerTournamentList = new();
        [SerializeField] private List<SerializeFarmSetup> m_playerFarmSetupList = new();
        [SerializeField] private List<SerializePartyGroup> m_playerPartyGroupList = new();
        [SerializeField] private List<MonsterScriptable> m_monsterScriptableList; // for edit only not data
        [SerializeField] private List<SerializeCoachItem> m_coachItemList;
        [SerializeField] private List<SerializeCrystalizeItem> m_crystalItemList;
        [SerializeField] private List<SerializeItem> m_hashItemList;
        [SerializeField] private List<SerializeItem> m_otherItemList;
        [SerializeField]
        private MonsterBagFilterData m_monsterBagFilterTags = new()
        {
            m_growthTypeFilter = new int[0],
            m_mainSeedFilter = new int[0],
            m_subSeedFilter = new int[0],
            m_bornTypeFilter = new int[0],
            m_rankFilter = new int[0],
            m_skillFilter = new string[0],
            m_traitFilter = new string[0],
            m_cycleFilter = new int[0],
            m_terrainFilter = new int[0]
        };
        [SerializeField]
        private FarmListFilterData m_farmListFilterTags = new()
        {
            m_terrainFilter = new int[0],
            m_boostHpRankFilter = new int[0],
            m_boostStrRankFilter = new int[0],
            m_boostIntRankFilter = new int[0],
            m_boostDexRankFilter = new int[0],
            m_boostAgiRankFilter = new int[0],
            m_boostVitRankFilter = new int[0],
            m_boostStressRankFilter = new int[0],
            m_boostFatigueRankFilter = new int[0],
            m_boostRestRankFilter = new int[0],
            m_potencialHpRankFilter = new int[0],
            m_potencialStrRankFilter = new int[0],
            m_potencialIntRankFilter = new int[0],
            m_potencialDexRankFilter = new int[0],
            m_potencialAgiRankFilter = new int[0],
            m_potencialVitRankFilter = new int[0],
            m_potencialStressRankFilter = new int[0],
            m_potencialFatigueRankFilter = new int[0],
            m_potencialRestRankFilter = new int[0],
            m_specialMainRankFilter = new int[0],
            m_specialSubRankFilter = new int[0],
            m_traitFilter = new string[0]
        };
        [SerializeField]
        private FarmItemInventoryFilterData m_farmItemListFilterTags = new()
        {
            m_itemEffect = new string[0],
            m_itemQuality = new int[0]
        };
        [SerializeField]
        private RaiseItemInventoryFilter m_raiseInventoryFilterTags = new()
        {
            m_itemEffect = new string[0],
            m_itemQuality = new int[0]
        };
        [SerializeField]
        private GeneralItemInventoryFilter m_generalItemInventory = new()
        {
            m_itemEffect = new string[0],
            m_itemQuality = new int[0]
        };
        private Dictionary<string, SerializeMonster> m_playerMonsterDict;
        private Dictionary<string, SerializeFarm> m_playerFarmDict;
        private Dictionary<string, SerializeItem> m_playerItemDict;
        private Dictionary<string, SerializeTournament> m_playerTournamentDict;
        private Dictionary<int, SerializePartyGroup> m_playerPartyGroupDict;
        private Dictionary<int, SerializeFarmSetup> m_playerFarmSetupDict;

        #endregion

        #region Public variables
        public string PlayerId => m_playerId;
        public string UserId => m_userId;
        public string PlayerName => m_playerName;
        public string PlayerWallet => m_playerWallet;
        public string Token => m_token;
        public string PlayerBio => m_playerBio;
        public float PlayerBit => m_playerBit;
        public float PlayerMaPoint => m_playerMaPoint;
        public string PlayerOas => m_playerOas;
        public float PlayerOasFloat => m_playerOasFloat;
        public string PlayerAvatar => m_playerAvatar;
        public string PlayerPhone => m_playerPhone;
        public string PlayerEmail => m_playerEmail;
        public string PlayerX => m_playerX;
        public string PlayerLanguage => m_player_language;
        public int TotalLoginDay => m_total_login_day;
        public int PlayerTime => m_player_time;
        public DateTime CreatedDate => m_crt_dt;
        public Texture2D AvatarImage => m_avatarImage;
        public bool IsPlayerEmailVerified => m_isPlayerEmailVerified;
        public bool IsPlayerPhoneVerified => m_isPlayerPhoneVerified;
        public bool IsBannedChat => m_bannedChat;
        public List<SerializeMonster> PlayerMonsterList => m_playerMonsterList;
        public List<SerializeFarm> PlayerFarmList => m_playerFarmList;
        public List<SerializeItem> PlayerItemList => m_playerItemList;
        public List<SerializeTournament> PlayerTournamentList => m_playerTournamentList;
        public List<SerializeFarmSetup> PlayerFarmSetupList => m_playerFarmSetupList;
        public List<SerializePartyGroup> PlayerPartyGroupList => m_playerPartyGroupList;
        public List<SerializeCoachItem> PlayerCoachItemList => m_coachItemList;
        public List<SerializeCrystalizeItem> PlayerCrystalItemList => m_crystalItemList;
        public List<SerializeItem> PlayerHashItemList => m_hashItemList;
        public List<SerializeItem> PlayerOtherItemList => m_otherItemList;

        #region Dictionaries
        public Dictionary<string, SerializeMonster> PlayerMonsterDict
        {
            get
            {
                if (m_playerMonsterDict == null || m_playerMonsterDict.Count <= 0)
                {
                    m_playerMonsterDict = new Dictionary<string, SerializeMonster>();
                    m_playerMonsterDict = m_playerMonsterList.ToDictionary(x => x.MonsterId);
                }

                return m_playerMonsterDict;
            }
        }

        public Dictionary<string, SerializeFarm> PlayerFarmDict
        {
            get
            {
                if (m_playerFarmDict == null || m_playerFarmDict.Count <= 0)
                {
                    m_playerFarmDict = new Dictionary<string, SerializeFarm>();
                    m_playerFarmDict = m_playerFarmList.ToDictionary(x => x.FarmId);
                }

                return m_playerFarmDict;
            }
        }

        public Dictionary<string, SerializeItem> PlayerItemDict
        {
            get
            {
                if (m_playerItemDict == null || m_playerItemDict.Count <= 0)
                {
                    m_playerItemDict = new Dictionary<string, SerializeItem>();
                    m_playerItemDict = m_playerItemList.ToDictionary(x => x.Item.ItemId);
                }

                return m_playerItemDict;
            }
        }

        public Dictionary<string, SerializeTournament> PlayerTournamentDict
        {
            get
            {
                if (m_playerTournamentDict == null || m_playerTournamentDict.Count <= 0)
                {
                    m_playerTournamentDict = new Dictionary<string, SerializeTournament>();
                    m_playerTournamentDict = m_playerTournamentList.ToDictionary(x => x.TournamentServerId);
                }

                return m_playerTournamentDict;
            }
        }

        public Dictionary<int, SerializePartyGroup> PlayerPartyGroupDict
        {
            get
            {
                if (m_playerPartyGroupDict == null || m_playerPartyGroupDict.Count <= 0)
                {
                    m_playerPartyGroupDict = new Dictionary<int, SerializePartyGroup>();
                    foreach (var party in m_playerPartyGroupList.AsSpan())
                    {
                        if (!m_playerPartyGroupDict.TryAdd(party.PartyId, party))
                        {
                            Debug.LogWarning($"Key party {party.PartyId} add failed");
                        }
                    }
                }

                return m_playerPartyGroupDict;
            }
        }

        public Dictionary<int, SerializeFarmSetup> PlayerFarmSetupDict
        {
            get
            {
                if (m_playerFarmSetupDict == null || m_playerFarmSetupDict.Count <= 0)
                {
                    m_playerFarmSetupDict = new Dictionary<int, SerializeFarmSetup>();
                    foreach (var set in m_playerFarmSetupList.AsSpan())
                    {
                        if (!m_playerFarmSetupDict.TryAdd(set.FarmSetupId, set))
                        {
                            Debug.LogWarning($"Key setup {set.FarmSetupId} add failed");
                        }
                    }
                }

                return m_playerFarmSetupDict;
            }
        }

        public MonsterBagFilterData MonsterBagFilterTags { get => m_monsterBagFilterTags; set => m_monsterBagFilterTags = value; }
        public FarmListFilterData FarmListFilterTags { get => m_farmListFilterTags; set => m_farmListFilterTags = value; }
        public FarmItemInventoryFilterData FarmItemListFilterTags { get => m_farmItemListFilterTags; set => m_farmItemListFilterTags = value; }
        public RaiseItemInventoryFilter RaiseInventoryFilterTags { get => m_raiseInventoryFilterTags; set => m_raiseInventoryFilterTags = value; }
        public GeneralItemInventoryFilter GeneralItemInventory { get => m_generalItemInventory; set => m_generalItemInventory = value; }
        #endregion

        #endregion

        #region Data refresh

        /// <summary>
        /// Clear dict and make new data from list
        /// </summary>
        public void RefreshMonsterData()
        {
            m_playerMonsterDict ??= new Dictionary<string, SerializeMonster>();
            m_playerMonsterDict.Clear();

            foreach (var monster in m_playerMonsterList.Where(monster =>
                         monster.CurrentCycleName != CycleManager.Instance.CurrentCycle.CycleName))
            {
                monster.SetCurrentCycle(CycleManager.Instance.CurrentCycle.CycleName);
            }

            m_playerMonsterDict = m_playerMonsterList.ToDictionary(x => x.MonsterId);
        }

        /// <summary>
        /// Clear dict and make new data from list
        /// </summary>
        public void RefreshMonsterData(List<SerializeMonster> playerMonsterList)
        {
            m_playerMonsterList.Clear();
            m_playerMonsterList.AddRange(playerMonsterList);
            m_playerMonsterDict ??= new Dictionary<string, SerializeMonster>();
            m_playerMonsterDict.Clear();
            foreach (var monster in m_playerMonsterList.AsSpan())
            {
                try
                {
                    m_playerMonsterDict.TryAdd(monster.MonsterId, monster);
                }
                catch (Exception e)
                {
                    Debug.LogWarning(e + "\n" + monster.MonsterName);
                }
            }
        }

        /// <summary>
        /// Clear dict and make new data from list
        /// </summary>
        public void RefreshFarmData()
        {
            m_playerFarmDict = new Dictionary<string, SerializeFarm>();
            m_playerFarmDict.Clear();
            foreach (var farm in m_playerFarmList.AsSpan())
            {
                m_playerFarmDict.TryAdd(farm.FarmId, farm);
            }
        }

        /// <summary>
        /// Clear dict and make new data from list
        /// </summary>
        public void RefreshFarmData(List<SerializeFarm> playerFarmList)
        {
            m_playerFarmList = new List<SerializeFarm>(playerFarmList);
            m_playerFarmDict = new Dictionary<string, SerializeFarm>();
            m_playerFarmList.Clear();
            m_playerFarmDict.Clear();
            foreach (var farm in m_playerFarmList.AsSpan())
            {
                m_playerFarmDict.TryAdd(farm.FarmId, farm);
            }
        }

        /// <summary>
        /// Clear dict and make new data from list
        /// </summary>
        public void RefreshItemData()
        {
            m_playerItemDict = new Dictionary<string, SerializeItem>();
            m_playerItemDict.Clear();
            m_playerItemDict = m_playerItemList.ToDictionary(x => x.Item.ItemId);
        }

        /// <summary>
        /// Clear dict and make new data from list
        /// </summary>
        public void RefreshTournamentData()
        {
            m_playerTournamentDict = new Dictionary<string, SerializeTournament>();
            m_playerTournamentDict.Clear();
            m_playerTournamentDict = m_playerTournamentList.ToDictionary(x => x.TournamentServerId);
        }

        public void RefreshFarmSetupData()
        {
            m_playerFarmSetupDict ??= new Dictionary<int, SerializeFarmSetup>();
            m_playerFarmSetupDict.Clear();
            foreach (var setup in m_playerFarmSetupList.AsSpan())
            {
                if (!m_playerFarmSetupDict.TryAdd(setup.FarmSetupId, setup))
                {
                    Debug.LogWarning($"Key farm setup {setup.FarmSetupId} already added");
                }
            }
        }

        public void RefreshFarmSetupData(List<SerializeFarmSetup> farmSetupList)
        {
            m_playerFarmSetupList = new List<SerializeFarmSetup>(farmSetupList);
            m_playerFarmSetupDict ??= new Dictionary<int, SerializeFarmSetup>();
            m_playerFarmSetupList.Clear();
            m_playerFarmSetupDict.Clear();
            foreach (var setup in m_playerFarmSetupList.AsSpan())
            {
                if (!m_playerFarmSetupDict.TryAdd(setup.FarmSetupId, setup))
                {
                    Debug.LogWarning($"Can't add setup with id {setup.FarmSetupId}");
                }
            }
        }

        public void RefreshTournamentData(List<SerializeTournament> playerTournamentList)
        {
            m_playerTournamentList.Clear();
            m_playerTournamentList = new List<SerializeTournament>(playerTournamentList);
            m_playerTournamentDict = new Dictionary<string, SerializeTournament>();
            m_playerTournamentDict.Clear();
            m_playerTournamentDict = m_playerTournamentList.ToDictionary(x => x.TournamentServerId);
        }

        public void ConvertPartySave(List<SerializePartyGroupSaving> partyGroupsList)
        {
            if (partyGroupsList == null) return;
            m_playerPartyGroupList ??= new List<SerializePartyGroup>();
            m_playerPartyGroupList.Clear();
            foreach (var group in partyGroupsList)
            {
                var partySetup = group.party_setup.Select(set => new SerializePartyGroup.SerializePartySetup(set.position, set.farm_id, set.monster_id)).ToList();
                m_playerPartyGroupList.Add(new SerializePartyGroup(group._id, group.party_id, group.party_name, false, partySetup));
            }

            RefreshParty();
        }

        public void RefreshParty()
        {
            m_playerPartyGroupDict = new Dictionary<int, SerializePartyGroup>();
            m_playerPartyGroupDict.Clear();
            foreach (var party in m_playerPartyGroupList.AsSpan())
            {
                if (!m_playerPartyGroupDict.TryAdd(party.PartyId, party))
                {
                    Debug.LogWarning($"Party key {party.PartyId} add failed or already exist");
                }
            }
        }

        public void RefreshParty(List<SerializePartyGroup> newSetup)
        {
            m_playerPartyGroupList = new List<SerializePartyGroup>(newSetup);
            m_playerPartyGroupDict = new Dictionary<int, SerializePartyGroup>();
            m_playerPartyGroupList.Clear();
            m_playerPartyGroupDict.Clear();
            foreach (var party in m_playerPartyGroupList.AsSpan())
            {
                if (!m_playerPartyGroupDict.TryAdd(party.PartyId, party))
                {
                    Debug.LogWarning($"Party key {party.PartyId} add failed or already exist");
                }
            }
        }

        #endregion

        public void ClearData()
        {
            m_playerTournamentList.Clear();
            m_playerMonsterList.Clear();
            m_playerItemList.Clear();
            m_playerFarmList.Clear();
            m_playerTournamentList.Clear();
            m_playerFarmSetupList.Clear();
            m_playerPartyGroupList.Clear();
            m_playerId = string.Empty;
            m_userId = string.Empty;
            m_playerName = string.Empty;
            m_playerWallet = string.Empty;
            m_token = string.Empty;
            m_playerBit = 0;
            m_playerOas = "0";
            m_playerAvatar = string.Empty;
            m_playerPhone = string.Empty;
            m_playerEmail = string.Empty;
            m_isPlayerEmailVerified = false;
            m_isPlayerPhoneVerified = false;
            m_playerMaPoint = 0;
            m_playerBio = string.Empty;
            m_avatarImage = null;
        }

        public async void SetPlayerData(string id, string user_id, string user_name, string address_wallet,
            string playerBio, int bit_balance, int mapoint_balance, string avatar, string email, string playerX,
            string player_phone, bool email_verified, bool phone_verified, int total_login_day, int player_time,
            string player_language, string crt_dt)
        {
            var playerAvatar = string.IsNullOrEmpty(avatar) ? "" : $"{avatar}?v={Random.Range(0, 100)}";
            m_playerId = id;
            m_userId = user_id;
            SetPlayerName(user_name);
            m_playerWallet = address_wallet;
            SetBit(bit_balance);
            SetMaPoint(mapoint_balance);
            m_playerAvatar = playerAvatar;
            m_playerEmail = email;
            m_playerPhone = player_phone;
            m_isPlayerEmailVerified = email_verified;
            m_isPlayerPhoneVerified = phone_verified;
            m_playerX = playerX;
            SetPlayerBio(playerBio);
            m_total_login_day = total_login_day;
            m_player_time = player_time;
            m_player_language = player_language;
            try
            {
                m_avatarImage = await Helpers.GetTexture(playerAvatar) ?? nullAvaSprite.texture;
            }
            catch (Exception e)
            {
                Debug.LogError(e.Message);
            }

            m_crt_dt = string.IsNullOrEmpty(crt_dt) ? DateTime.UtcNow : DateTime.Parse(crt_dt);
        }

        public void ChangeAvatar(Texture2D image)
        {
            m_avatarImage = image;
        }

        public void SetOas(string value, float valueFloat)
        {
            m_playerOas = value;
            m_playerOasFloat = valueFloat;
        }

        public async UniTask SetAvatarString(string value)
        {
            m_playerAvatar = value;
            try
            {
                m_avatarImage = await Helpers.GetTexture(value) ?? nullAvaSprite.texture;
            }
            catch (Exception e)
            {
                Debug.LogError(e.Message);
            }
        }

        public void SetBannedChat(bool value)
        {
            m_bannedChat = value;
        }

        internal void AddBit(float amount)
        {
            m_playerBit += amount;
        }

        internal void SetBit(float amount)
        {
            m_playerBit = amount;
        }

        internal void SetMaPoint(float amount)
        {
            m_playerMaPoint = amount;
        }

        internal void SetPlayerName(string playerName)
        {
            m_playerName = playerName;
        }

        internal void SetPlayerBio(string bio)
        {
            m_playerBio = bio;
        }

        internal void SetPlayerX(string x)
        {
            m_playerX = x;
        }

        #region Data to json

        public List<SerializePlayerMonster> MonsterJsonList()
        {
            List<SerializePlayerMonster> monsterListSave = new();
            foreach (var monster in m_playerMonsterList.AsSpan())
            {
                monsterListSave.Add(new SerializePlayerMonster(
                    _id: monster.MonsterId, monster_nft_id: monster.MonsterNFTId, monster_name: monster.MonsterName,
                    monster_script_id: monster.MonsterScriptableData.MonsterScriptableId,
                    monster_personality_id: monster.MonsterPersonality.PersonalityId, likes: monster.LikeMeal.FoodName,
                    dislikes: monster.DislikeMeal.FoodName, monster_age: monster.MonsterAge,
                    rank_battle_count: monster.RankBattleCount, is_crystal_used: monster.IsCrystalUsed,
                    is_life_potion_used: monster.IsLifePotionUsed, is_item_used: monster.IsItemUsed,
                    is_memory: monster.IsMemory, is_alive: monster.IsMonsterAlive,
                    monster_rank: (int)monster.MonsterRank, alter_basic_p: monster.MonsterAlteredBasicP.SaveParameter,
                    alter_training_p: monster.MonsterAlteredTrainingP.SaveParameter,
                    innate_trait_id_list: monster.InnateTraits.Select(x => new SerializeInnateTrait(x.TraitId)).ToList(),
                    skill_detail: monster.SkillSave,
                    monster_acquired_traits: monster.MonsterAcquireTraitLevel.Select(x => new SerializeAcquireTraitSaving(x.TraitScriptable.TraitId, x.Level)).ToList(),
                    monster_injury: monster.MonsterInjuryCondition, monster_disease: monster.MonsterDiseasesCondition,
                    minter: monster.Minter, mint_type: monster.MintType, cycle: monster.Cycle,
                    born_of_1_address: monster.BornOf1Address, born_of_1_id: monster.BornOf1Id,
                    born_of_1_chain: monster.BornOf1Chain, born_of_1_token_id: monster.BornOf1TokenId,
                    born_of_2_address: monster.BornOf2Address, born_of_2_id: monster.BornOf2Id,
                    born_of_2_chain: monster.BornOf2Chain, born_of_2_token_id: monster.BornOf2TokenId,
                    is_favorite: monster.IsFavorite, is_rank_s_offical_clear: monster.IsRankSOfficalClear,
                    is_monster_grand_prix_clear: monster.IsMonsterGrandPrixClear,
                    is_monster_crown_cup_clear: monster.IsMonsterCrownCupClear,
                    is_santuary_cup_clear: monster.IsSantuaryCupClear, is_winner_cup_clear: monster.IsWinnerCupClear,
                    is_re_master_cup_clear: monster.IsReMasterCupClear, is_free_monster: monster.IsFree,
                    overall: monster.OverallValue, lifespan_fusion: monster.MonsterLifespanFusion,
                    main_seed: (int)monster.MonsterScriptableData.MonsterMainSeed,
                    sub_seed: (int)monster.MonsterScriptableData.MonsterSubSeed, lifespan: monster.ComplexLifespan,
                    crystal_rank_use: (int)monster.CrystalRankUse, crystal_parameter_use: (int)monster.CrystalParameter,
                    nft_image_url: monster.NftImageUrl, training_count: monster.TrainingCount,
                    current_cycle_name: monster.CurrentCycleName, final_hp: monster.ComplexHealth,
                    final_str: monster.ComplexStrength, final_int: monster.ComplexIntelligent,
                    final_dex: monster.ComplexAgility, final_agi: monster.ComplexDexterity,
                    final_vit: monster.ComplexVitality, life_span_end_count: monster.LifeSpanEndCount,
                    is_coached: monster.IsCoached, is_crystalized: monster.IsCrystalized));
            }

            return monsterListSave;
        }

        public List<SerializePlayerFarm> FarmJsonList()
        {
            List<SerializePlayerFarm> farmListSave = new();
            foreach (var farm in m_playerFarmList.AsSpan())
            {
                farmListSave.Add(new SerializePlayerFarm(
                    farm.FarmId, farm.FarmNftId, farm.FarmName, farm.FarmTrait.FarmTraitId,
                    farm.FarmTerrain, farm.EnhanceValues.SaveParameter,
                    farm.MaxEnhanceValues.SaveParameter, farm.StressEnhanceValue, farm.FatigueEnhanceValue,
                    farm.RestEnhanceValue, farm.MaxStressEnhanceValue, farm.MaxFatigueEnhanceValue,
                    farm.MaxRestEnhanceValue, farm.SpecialTrainingValue.SaveParameter, farm.IsFavorite, farm.IsFree));
            }

            return farmListSave;
        }

        public List<SerializePlayerItem> ItemJsonList()
        {
            List<SerializePlayerItem> itemListSave = new();
            foreach (var item in m_playerItemList.AsSpan())
            {
                itemListSave.Add(new SerializePlayerItem(item.Item.ItemId, item.Quantity));
            }

            return itemListSave;
        }

        public List<SerializeTournamentSaving> TournamentJsonList()
        {
            List<SerializeTournamentSaving> tournamentSavingsList = new();
            foreach (var tournament in m_playerTournamentList.AsSpan())
            {
                tournamentSavingsList.Add(new SerializeTournamentSaving(
                    tournament.TournamentID, tournament.TournamentDetail.TournamentId,
                    tournament.TournamentParticipateList, tournament.TournamentYear, tournament.IsTournamentScheduled,
                    tournament.IsTournamentWon, tournament.IsTournamentParticipated));
            }

            return tournamentSavingsList;
        }

        public List<SerializeFarmSetupSaving> FarmSavingSetJsonList()
        {
            List<SerializeFarmSetupSaving> farmSetupSavingsList = new();
            foreach (var farmSetup in m_playerFarmSetupList.AsSpan())
            {
                farmSetupSavingsList.Add(new SerializeFarmSetupSaving()
                {
                    farm_setup_id = farmSetup.FarmSetupId,
                    is_selected = farmSetup.IsSelected,
                    //farm_saving_setup = farmSetup.FarmSavingSet.ToArray(),
                });
            }

            return farmSetupSavingsList;
        }

        public List<SerializePartyGroupSaving> PartySavingJsonList()
        {
            List<SerializePartyGroupSaving> partyJsonSavingList = new();
            for (var i = 0; i < m_playerPartyGroupList.Count; i++)
            {
                var party = m_playerPartyGroupList[i];
                var partyJsonSetupList = new List<SerializePartySetupSaving>();
                for (var j = 0; j < party.PartySetup.Count; j++)
                {
                    var setup = party.PartySetup[j];
                    partyJsonSetupList.Add(new SerializePartySetupSaving()
                    {
                        farm_id = setup.FarmId,
                        position = setup.Position,
                        monster_id = setup.MonsterId
                    });
                }

                partyJsonSavingList.Add(new SerializePartyGroupSaving()
                {
                    _id = party.ServerId,
                    party_id = party.PartyId,
                    party_name = party.PartyName,
                    party_setup = partyJsonSetupList,
                });
            }

            return partyJsonSavingList;
        }

        #endregion

        public void Burn_Monster(string monsterId, bool turnMemory)
        {
            var monster = m_playerMonsterList.FirstOrDefault(x => x.MonsterId == monsterId);
            if (monster != null)
            {
                if (turnMemory)
                    monster.SetAsMemory();
                else
                    m_playerMonsterList.Remove(monster);
                GameDataManager.OnMonsterDataChanged?.Invoke();
            }
        }

        public void Burn_Monster(List<(string monsterId, bool turnMemory)> monsterIdList)
        {
            for (int i = 0; i < monsterIdList.Count; i++)
            {
                var existMonster = m_playerMonsterList.FirstOrDefault(x => x.MonsterId == monsterIdList[i].monsterId);
                if (existMonster == default) continue;
                if (monsterIdList[i].turnMemory)
                    existMonster.SetAsMemory();
                else
                    m_playerMonsterList.RemoveAll(x => x.MonsterId == monsterIdList[i].monsterId);
            }
            GameDataManager.OnMonsterDataChanged?.Invoke();
        }


        #region Editor functions

        public void SortMonster()
        {
            m_playerMonsterList.OrderBy(x => x.MonsterScriptableData.MonsterPublicNumber).ToList();
        }
        public void AddMonsterFromList()
        {
            foreach (var monsterData in m_monsterScriptableList.AsSpan())
            {
                m_playerMonsterList.Add(new SerializeMonster(
                    monsterName: monsterData.MonsterName, monsterAge: 0, rankBattleCount: 0, monsterData: monsterData,
                    monsterAlteredBasicP: new SerializeBasicParameters(),
                    monsterAlteredTrainingP: new SerializeGrowthParameters(),
                    monsterDiseasesCondition: Enums.DiseasesTypesEnum.None,
                    monsterInjuryCondition: Enums.InjuryTypesEnum.None,
                    monsterSkillLevel: new List<SerializeMonsterSkillLevel>(), monsterRank: RankTypeEnums.f,
                    monsterInnateTraitList: new List<MonsterInnateTrait>(),
                    acquireTraitLevelList: new List<SerializeAcquireTraitLevel>(), monsterPersonality: null,
                    isCrystalUsed: false, isFavorite: false, isItemUsed: false, isLifePotionUsed: false,
                    isMemory: false,
                    monsterLikeFoodScriptable: null, monsterDisikeFoodScriptable: null, isRankSOfficalClear: false,
                    isMonsterGrandPrixClear: false, isMonsterCrownCupClear: false, isSantuaryCupClear: false,
                    isWinnerCupClear: false, isReMasterCupClear: false, overallValue: 0, minter: string.Empty,
                    mintType: 2, cycle: string.Empty, bornOf1Address: string.Empty, bornOf1Id: 0,
                    bornOf1Chain: 0, bornOf2Address: string.Empty, bornOf2Id: 0, bornOf2Chain: 0,
                    bornOf1TokenId: 0, bornOf2TokenId: 0, isFree: false, monsterLifespanFusion: 100,
                    crystalParameter: BasicBattleParameterEnum.HP, crystalRankUse: RankTypeEnums.f,
                    nftImageUrl: string.Empty, lifeSpanEndCount: 0));
            }
        }
        #endregion
    }
}