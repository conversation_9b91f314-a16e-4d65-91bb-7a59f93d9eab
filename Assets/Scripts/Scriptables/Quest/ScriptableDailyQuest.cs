using Assets.Scripts.Scriptables.Item;
using UnityEngine;

namespace Assets.Scripts.Scriptables.Quest
{
    [CreateAssetMenu(fileName = "New Daily Quest", menuName = "ScriptableObjects/Quest/Create daily quest")]
    public class ScriptableDailyQuest : ScriptableQuest
    {
        [SerializeField] private ScriptableItem rewardItem;
        [SerializeField] private int rewardRequirement = 1;
        [SerializeField] private float rewardBit;
        [SerializeField] private Sprite rewardImage;

        public ScriptableItem RewardItem => rewardItem;
        public int RewardRequirement => rewardRequirement;
        public float RewardBit => rewardBit;
        public Sprite RewardImage => rewardImage;
    }
}