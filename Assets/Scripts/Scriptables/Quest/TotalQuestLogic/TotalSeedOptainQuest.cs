using Assets.Scripts.DataStruct.Data;
using Assets.Scripts.Managers;
using System.Linq;
using UnityEngine;

namespace Assets.Scripts.Scriptables.Quest.TotalQuestLogic
{
    [CreateAssetMenu(fileName = "Total Seed Quest Logic", menuName = "ScriptableObjects/Total Quest/Seed Quest")]
    public class TotalSeedOptainQuest : ScriptableQuestLogic
    {
        private readonly int rewardCycle = 4;
        private readonly int rewardInterval = 5;
        private readonly int requestIncreasement = 5;

        public override float GetQuestRequirement(string questId)
        {
            var claimedProgress = GameProgressManager.Instance.GameProgress.ClaimedProgress.FirstOrDefault(x => x.QuestId == questId);
            var quest = GameProgressManager.Instance.TotalQuestList.FirstOrDefault(quest => quest.QuestId == questId);
            if (claimedProgress != null)
            {
                int nextRequirement = 0;
                while (nextRequirement <= claimedProgress.ClaimedProgress * requestIncreasement)
                {
                    nextRequirement += requestIncreasement;
                }
                return nextRequirement;
            }
            return 1;
        }

        public override QuestCycleReward GetReward(ScriptableTotalQuest totalQuest)
        {
            var claimedProgress = GameProgressManager.Instance.GameProgress.ClaimedProgress.FirstOrDefault(x => x.QuestId == totalQuest.QuestId);
            int claimProgress = 0;
            while (claimProgress + rewardInterval <= claimedProgress.ClaimedProgress * rewardInterval)
            {
                claimProgress += rewardInterval;
            }
            int rewardIndex = claimProgress / rewardInterval % rewardCycle;
            return totalQuest.QuestCycleReward[rewardIndex];
        }

        public override float GetCurrentProgress()
        {
            return GameProgressManager.Instance.GameProgress.ObtainedMonsterList.Count;
        }
    }
}
