using Assets.Scripts.Tournament;
using Network.TournamentState;
using TMPro;
using UI.UIFriend;
using UnityEngine;
using UnityEngine.UI;

namespace Sponsored_Tournament
{
    public class ItemRoundRobin : MonoBehaviour
    {
        [SerializeField] private Image playerAvatar;
        [SerializeField] private GameObject avatar;
        [SerializeField] private GameObject outline;
        [SerializeField] private GameObject lineDash;
        [SerializeField] private Sprite[] statusIcon;
        [SerializeField] private Image statusImage;
        [SerializeField] private TextMeshProUGUI winText;
        [SerializeField] private Button _progressButton;
        
        private void OnEnable()
        {
            EmptyData();
        }
    
        public void SetOutline(bool show)
        {
            outline.gameObject.SetActive(show);
        }

        public void EmptyData()
        {
            avatar.SetActive(false);
            lineDash.SetActive(false);
            statusImage.gameObject.SetActive(false);
            winText.gameObject.SetActive(false);
        }
    
        public async void SetAvatar(string wallet)
        {
            var user = await FriendDataManager.Instance.GetUserData(wallet);
            avatar.SetActive(true);
            playerAvatar.sprite =  user.userIcon;
            lineDash.SetActive(false);
            statusImage.gameObject.SetActive(false);
            winText.gameObject.SetActive(false);
        }
    
        public void SetLineDash()
        {
            lineDash.SetActive(true);
            avatar.SetActive(false);
            statusImage.gameObject.SetActive(false);
            winText.gameObject.SetActive(false);
        }

        public void SetRoundRobinStatus(RoundRobinStatus status)
        {
            statusImage.gameObject.SetActive(true);
            statusImage.sprite = statusIcon[(int)status];
            avatar.SetActive(false);
            lineDash.SetActive(false);
            winText.gameObject.SetActive(false);
        }

        public void SetWinCount(int count)
        {
            winText.gameObject.SetActive(true);
            winText.text = count.ToString();
            avatar.SetActive(false);
            lineDash.SetActive(false);
            statusImage.gameObject.SetActive(false);
        }

        public void SetMatchOnline(TournamentMatch match, int stateRound, int player1Id, int player2Id, TournamentInProgressBottom progressBottom, SponsoredTournamentInfo info)
        {
            switch (match.state)
            {
                case 2 or 3:
                    if (match.round_id == stateRound)
                    {
                        SetRoundRobinStatus(RoundRobinStatus.InProgress);
                        _progressButton.gameObject.SetActive(true);
                        _progressButton.interactable = true;
                        _progressButton.onClick.AddListener(() =>
                        {
                            progressBottom.InitInprogressImage(match.player_1, match.player_2, match.room_id);
                        });
                    }
                    break;
                case 4 or 5:
                    SetRoundRobinStatus(player1Id == match.winner ? RoundRobinStatus.Win : RoundRobinStatus.Lose);
                    break;
            }
        }
    
        public void SetMatchResult(int player1Id, int player2Id, int winner)
        {
            SetRoundRobinStatus(player1Id == winner ? RoundRobinStatus.Win : RoundRobinStatus.Lose);
        }

        public void TestLog()
        {
            Debug.Log("This is test log!");
        }

        public enum RoundRobinStatus
        {
            InProgress = 0,
            Win = 1,
            Lose = 2
        }
    
    }
}
