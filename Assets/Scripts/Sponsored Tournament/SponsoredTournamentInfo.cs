using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Assets.Scripts.Controllers;
using Assets.Scripts.DataStruct;
using Assets.Scripts.Managers;
using Assets.Scripts.UI;
using Assets.Scripts.UI.Utilities;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using I2.Loc;
using Network;
using Network.TournamentState;
using Sponsored_Tournament;
using TMPro;
using UI.UI_Sponsored_Tournament;
using UI.UIFriend;
using UI.Utilities;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.Tournament
{
    public class SponsoredTournamentInfo : UIMenuStruct
    {
        [SerializeField] private UIPvPTournamentMenu menu;
        [SerializeField] private SponsoredTournamentCreate editTournament;
        [SerializeField] private Image bannerImage;
        [SerializeField] private GameObject rewardObjectIndicator;
        [SerializeField] private TextMeshProUGUI tournamentName;
        [SerializeField] private TextMeshProUGUI date;
        [SerializeField] private TextMeshProUGUI registrationDate;
        [SerializeField] private TextMeshProUGUI entryPeriod;
        [SerializeField] private TextMeshProUGUI jumpInPeriod;
        [SerializeField] private TextMeshProUGUI sponsorName;
        [SerializeField] private TextMeshProUGUI sponsorId;
        [SerializeField] private TextMeshProUGUI type;
        [SerializeField] private TextMeshProUGUI participants;
        [SerializeField] private TextMeshProUGUI registrations;
        [SerializeField] private TextMeshProUGUI allowList;
        [SerializeField] private TextMeshProUGUI jumpIn;
        [SerializeField] private TextMeshProUGUI prize;
        [SerializeField] private TextMeshProUGUI rank;
        [SerializeField] private TextMeshProUGUI monsters;
        [SerializeField] private TextMeshProUGUI turns;
        [SerializeField] private TextMeshProUGUI specialHex;
        [SerializeField] private TextMeshProUGUI memory;
        [SerializeField] private TextMeshProUGUI sms;
        [SerializeField] private TextMeshProUGUI playerRank;
        [SerializeField] private TextMeshProUGUI cycleScore;
        [SerializeField] private TextMeshProUGUI battles;
        [SerializeField] private TextMeshProUGUI playingDays;
        [SerializeField] private MonsterRequirementUI monsterRequirementUI;
        [SerializeField] private Button registrationBtn;
        [SerializeField] private Button watchBtn;
        [SerializeField] private Button jumpInBtn;
        [SerializeField] private Button entryBtn;
        [SerializeField] private Button countdownBtn;
        [SerializeField] private Button editBtn;
        [SerializeField] private Button deleteBtn;
        [SerializeField] private Button resultBtn;
        [SerializeField] private Button redeemBtn;
        [SerializeField] private Button editALBtn;
        [SerializeField] private Button cancelRegistrationBtn;
        [SerializeField] private Button cancelOpenedBtn;
        [SerializeField] private TextMeshProUGUI countdownText;
        [SerializeField] private TextMeshProUGUI redeemBtnText;
        [SerializeField] private GameObject bottomButtonContainer;
        [SerializeField] private GameObject statusObject;
        [SerializeField] private TextMeshProUGUI statusManageText;
        [SerializeField] private Button closeDetailBtn;
        [SerializeField] private Button openDetailBtn;
        [SerializeField] private RectTransform tournamentInProgress;
        [SerializeField] private RectTransform leftContainer;
        [SerializeField] private GameObject rightContainer;
        [SerializeField] private SponsorWaitForEntry sponsorWaitForEntry;
        [SerializeField] private SponsorTournamentResult sponsorTournamentResult;
        [SerializeField] private SponsorTournamentRR roundRobinResult;
        [SerializeField] private SponsorTournamentEl eliminationResult;
        [SerializeField] private TournamentInProgressBottom progressStatus;
        [SerializeField] private Vector2 achorPosOpenDetail = new(0, 0);
        [SerializeField] private Vector2 achorPosCloseDetail = new(-557, 0);
        [SerializeField] private Vector2 sizeDeltaOpenDetail = new(1370, 818);
        [SerializeField] private Vector2 sizeDeltaCloseDetail = new(684, 818);
        [SerializeField] private Vector2 achorPosOpenTournament = new(1200, 0);
        [SerializeField] private Vector2 achorPosCloseTournament = new(0, 0);
        [SerializeField] private Vector2 leftContainerOpenTournament = new(0, 0);
        [SerializeField] private Vector2 leftContainerCloseTournament = new(0, 0);

        public string CountdownText;
        private readonly List<string> _conditionMessage = new();
        private int _conditionCount;
        private int _entryCount;
        private CancellationTokenSource _cancellationTokenSource;
        private SerializeSponsoredTournament _tournamentData;

        private void Awake()
        {
            watchBtn.onClick.RemoveAllListeners();
            watchBtn.onClick.AddListener(() => { JoinTournamentRoom(watchBtn, true).Forget(); });


            entryBtn.onClick.RemoveAllListeners();
            entryBtn.onClick.AddListener(() =>
            {
                CanJoinTournament();
                JoinTournamentRoom(entryBtn).Forget();
            });

            jumpInBtn.onClick.RemoveAllListeners();
            jumpInBtn.onClick.AddListener(() =>
            {
                CanJoinTournament();
                JoinTournamentRoom(jumpInBtn).Forget();
            });

            editBtn.onClick.RemoveAllListeners();
            editBtn.onClick.AddListener(() =>
            {
                CloseMenu();
                editTournament.OpenMenu();
                editTournament.SetDataEdit(_tournamentData, false);
            });

            editALBtn.onClick.RemoveAllListeners();
            editALBtn.onClick.AddListener(() =>
            {
                CloseMenu();
                editTournament.OpenMenu();
                editTournament.SetDataEdit(_tournamentData, true);
            });

            cancelOpenedBtn.onClick.RemoveAllListeners();
            cancelOpenedBtn.onClick.AddListener(CancelOpened);

            cancelRegistrationBtn.onClick.RemoveAllListeners();
            cancelRegistrationBtn.onClick.AddListener(() => RegistrationTournament(true));

            registrationBtn.onClick.RemoveAllListeners();
            registrationBtn.onClick.AddListener(() =>
            {
                CanJoinTournament();
                RegistrationTournament(false);
            });

            deleteBtn.onClick.RemoveAllListeners();
            deleteBtn.onClick.AddListener(DeleteTournament);

            resultBtn.onClick.RemoveAllListeners();
            resultBtn.onClick.AddListener(() =>
            {
                OpenDetail(false); // turn off right container detail
                if (_tournamentData.type == TournamentType.RoundRobin)
                {
                    roundRobinResult.gameObject.SetActive(true);
                    roundRobinResult.InitItem(_tournamentData, null, sponsorTournamentResult, this);
                }
                else
                {
                    eliminationResult.gameObject.SetActive(true);
                    eliminationResult.InitItem(_tournamentData, null, sponsorTournamentResult, this);
                }

                OpenTournamentDetails(true);
            });

            closeDetailBtn.onClick.RemoveAllListeners();
            closeDetailBtn.onClick.AddListener(() =>
            {
                OpenDetail(false);
                bottomButtonContainer.SetActive(false);
                openDetailBtn.gameObject.SetActive(true);
                closeDetailBtn.gameObject.SetActive(false);
            });

            openDetailBtn.onClick.RemoveAllListeners();
            openDetailBtn.onClick.AddListener(() =>
            {
                OpenDetail(true);
                bottomButtonContainer.SetActive(false);
                closeDetailBtn.gameObject.SetActive(true);
                openDetailBtn.gameObject.SetActive(false);
            });
        }

        private void OnEnable()
        {
            rightContainer.SetActive(true);
            statusObject.SetActive(false);
            sponsorWaitForEntry.gameObject.SetActive(false);
            sponsorTournamentResult.gameObject.SetActive(false);
            roundRobinResult.gameObject.SetActive(false);
            eliminationResult.gameObject.SetActive(false);
            TournamentManager.Instance.TournamentStateChange += ChangeTournamentState;
            TournamentManager.Instance.TournamentError += TournamentError;
            // TournamentManager.Instance.TournamentMatchListChange += ChangeMatchList;
            _cancellationTokenSource = new CancellationTokenSource();
        }

        private void OnDisable()
        {
            TournamentManager.Instance.TournamentStateChange -= ChangeTournamentState;
            TournamentManager.Instance.TournamentError -= TournamentError;
            // TournamentManager.Instance.TournamentMatchListChange -= ChangeMatchList;
            _cancellationTokenSource.Cancel();
            _cancellationTokenSource.Dispose();
            _cancellationTokenSource = null;
        }

        public override void CloseMenu()
        {
            base.CloseMenu();
            bottomButtonContainer.SetActive(true);
            tournamentInProgress.DOScale(Vector3.zero, 0.3f);
            tournamentInProgress.DOAnchorPos(new Vector2(1200, 0), 0.3f);
        }

        private void TournamentError(int code, string message)
        {
            Debug.LogError($"{code} {message}");
            switch (code)
            {
                case 400:
                    UIPopupNotify.Instance.SetNotify("Error", message);
                    CloseMenu();
                    break;
            }
        }

        public async void ChangeTournamentState(TournamentState tournamentState)
        {
            try
            {
                _tournamentData.state = (TournamentStateEnum)tournamentState.state;
                switch (_tournamentData.state)
                {
                    case TournamentStateEnum.Entry:
                    case TournamentStateEnum.JumpIn:
                        sponsorWaitForEntry.gameObject.SetActive(true);
                        await FriendDataManager.Instance.GetListUser(tournamentState.entry_list.items.Values.ToList());
                        sponsorWaitForEntry.AddNewPlayer(_tournamentData.participants, tournamentState.entry_list,
                            ExitEntry);
                        break;
                    case TournamentStateEnum.InProgress:
                        sponsorWaitForEntry.gameObject.SetActive(false);
                        _tournamentData.type = (TournamentType)tournamentState.metadata.type;
                        switch (tournamentState.metadata.type)
                        {
                            case (int)TournamentType.Elimination:
                                eliminationResult.gameObject.SetActive(true);
                                eliminationResult.InitItem(_tournamentData, tournamentState, sponsorTournamentResult, this);
                                break;
                            case (int)TournamentType.RoundRobin:
                                roundRobinResult.gameObject.SetActive(true);
                                roundRobinResult.InitItem(_tournamentData, tournamentState, sponsorTournamentResult, this);
                                break;
                        }

                        foreach (var match in tournamentState.match_list.items.Values.Where(match => match.round_id == tournamentState.round))
                            await JoinBattleRoom(match, tournamentState.round);
                        break;
                    case TournamentStateEnum.Ended:
                        roundRobinResult.gameObject.SetActive(false);
                        eliminationResult.gameObject.SetActive(false);
                        sponsorTournamentResult.gameObject.SetActive(true);
                        sponsorTournamentResult.AddResultItem(_tournamentData, tournamentState, eliminationResult,
                            roundRobinResult, this);
                        break;
                    default:
                        Debug.LogError("Invalid state");
                        break;
                }
            }
            catch (Exception e)
            {
                Debug.LogError(e.Message);
            }
        }

        private async UniTask JoinBattleRoom(TournamentMatch match, int round)
        {
            try
            {
                string roomId = $"{_tournamentData._id}_{match.match_id}";
                string currentPlayerWallet = GameDataManager.Instance.LoadedPlayerData.PlayerWallet;

                Debug.Log($"[JoinBattleRoom] Processing match {match.match_id} for round {round}. Room ID: {roomId}");
                Debug.Log($"[JoinBattleRoom] Player 1: {match.player_1}, Player 2: {match.player_2}, Current Player: {currentPlayerWallet}");

                // Set current tournament match
                TournamentManager.Instance.CurrentTournamentMatch = new TournamentMatch
                {
                    match_id = match.match_id,
                    round_id = match.round_id,
                    player_1 = match.player_1,
                    player_1_id = match.player_1_id,
                    player_2 = match.player_2,
                    player_2_id = match.player_2_id,
                    winner = -1,
                    room_id = roomId
                };

                // Player 1 creates the room
                if (match.player_1 == currentPlayerWallet && match.player_2_id != -1 && round == match.round_id)
                {
                    Debug.Log($"[JoinBattleRoom] Player 1 creating room: {roomId}");

                    var roomOptions = new Dictionary<string, object>
                    {
                        ["roomName"] = roomId,
                        ["password"] = "",
                        ["private"] = false,
                        ["metadata"] = new Dictionary<string, object>
                        {
                            ["firstPlayer"] = match.player_1,
                            ["secondPlayer"] = match.player_2,
                            ["audience"] = true,
                            ["opponent"] = true,
                            ["monsterRank"] = _tournamentData.monster_rank.ToString().ToUpper(),
                            ["monsterNum"] = _tournamentData.monsters.ToString(),
                            ["turnNum"] = _tournamentData.turns.ToString(),
                            ["specialHex"] = _tournamentData.special_hex switch
                            {
                                SpecialHexType.Random => "Random",
                                SpecialHexType.Unmovable => "Unmovable",
                                SpecialHexType.Damage => "Damage",
                                _ => "None"
                            },
                            ["memoryAllow"] = _tournamentData.memory,
                            ["hostId"] = _tournamentData._id
                        }
                    };

                    var room = await CustomNetworkManager.Instance.CreateTournamentRoom(roomOptions);
                    if (room != null)
                    {
                        Debug.Log($"[JoinBattleRoom] Player 1 successfully created room: {roomId}");
                        await TournamentManager.Instance.SendMatchStart();
                        CustomNetworkManager.onGameStateChange += () =>
                        {
                            if (room.State.players[1] is null || room.State.players[1].state != "NotReady") return;
                            progressStatus.InitInprogress(match.player_1, match.player_2, this).Forget();
                        };
                    }
                    else
                    {
                        Debug.LogError($"[JoinBattleRoom] Player 1 failed to create room: {roomId}");
                    }
                }

                // Player 2 joins the room
                if (match.player_2 == currentPlayerWallet && round == match.round_id)
                {
                    Debug.Log($"[JoinBattleRoom] Player 2 attempting to join room: {roomId}");

                    // Add a delay to ensure room is created first
                    await UniTask.Delay(1000);

                    if (string.IsNullOrEmpty(match.room_id))
                        match.room_id = roomId;

                    var room = await CustomNetworkManager.Instance.JoinTournamentRoom(match.room_id);
                    if (room != null)
                    {
                        Debug.Log($"[JoinBattleRoom] Player 2 successfully joined room: {roomId}");
                        await room.Send("player_enter", new
                        {
                            playerId = GameDataManager.Instance.LoadedPlayerData.PlayerId,
                            playerName = GameDataManager.Instance.LoadedPlayerData.PlayerName,
                            walletId = GameDataManager.Instance.LoadedPlayerData.PlayerWallet
                        });
                        await progressStatus.InitInprogress(match.player_1, match.player_2, this);
                    }
                    else
                    {
                        Debug.LogError($"[JoinBattleRoom] Player 2 failed to join room: {roomId}");
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[JoinBattleRoom] Error in match {match.match_id}: {e.Message}");
                Debug.LogError($"[JoinBattleRoom] Stack trace: {e.StackTrace}");
            }
        }


        private void OpenDetail(bool value)
        {
            bottomButtonContainer.SetActive(value);
            var anchoredPos = value ? achorPosOpenDetail : achorPosCloseDetail;
            var sizeDelta = value ? sizeDeltaOpenDetail : sizeDeltaCloseDetail;
            ControlContainer.DOAnchorPos(anchoredPos, 0.5f).SetEase(Ease.OutBack);
            ControlContainer.DOSizeDelta(sizeDelta, 0.5f).SetEase(Ease.OutBack);
            rightContainer.SetActive(value);
            tournamentInProgress.localScale = value ? Vector3.one : Vector3.zero;
            var mySequence = DOTween.Sequence();
            mySequence.Append(tournamentInProgress.DOScale(!value ? Vector3.one : Vector3.zero, 0.5f)
                    .SetEase(Ease.OutBack))
                .PrependInterval(0.3f)
                .Insert(0,
                    tournamentInProgress.DOAnchorPos(value ? achorPosOpenTournament : achorPosCloseTournament, 0.3f))
                .Insert(0,
                    leftContainer.DOAnchorPos(value ? leftContainerOpenTournament : leftContainerCloseTournament,
                        0.3f));
        }


        private void CancelOpened()
        {
            LocalizedString S_T_Warning1 = "SponsorTournament/S_T_Warning1";
            UIPopupPanel.Instance.OpenMenu();
            UIPopupPanel.Instance.Setup("Warning!!", S_T_Warning1, CancelOpenedInBackend, null);
        }

        private void CancelOpenedInBackend()
        {
            _tournamentData.is_draft = true;
            _tournamentData.state = TournamentStateEnum.Draft;
            _tournamentData.register_list.Clear();
            statusManageText.text = nameof(ManageStatus.Draft);
            // menu._currentFilter = 5;
            TurnOffAllBottomBtn();
            redeemBtn.gameObject.SetActive(true);
            redeemBtn.onClick.RemoveAllListeners();
            redeemBtn.onClick.AddListener(() => RedeemBlockchain().GetAwaiter());
            TournamentManager.Instance.UpdateSponsoredTournament(_tournamentData);
        }

        private async UniTask RedeemBlockchain()
        {
            var loading = BackendLoadData.Instance.LoadingCanvas("RedeemBlockchain");
            try
            {
                var result = await SmartContractInteract.Instance.CloseTournament(_tournamentData.blockchain_id);
                if (result.receipt.status == 1)
                {
                    TurnOffAllBottomBtn();
                    menu._currentFilter = 5;
                    _tournamentData.is_deposit_oas = false;
                    resultBtn.gameObject.SetActive(true);
                    redeemBtn.gameObject.SetActive(true);
                    redeemBtn.interactable = false;
                    redeemBtnText.text = "Redeemed";
                    BackendLoadData.Instance.LoadOasBalance().Forget();
                }
                TournamentManager.Instance.UpdateSponsoredTournament(_tournamentData);
                Destroy(loading);
            }
            catch (Exception e)
            {
                Destroy(loading);
                Debug.LogError(e.Message);
            }
        }

        private void DeleteTournament()
        {
            LocalizedString S_T_Warning2 = "SponsorTournament/S_T_Warning2";
            UIPopupPanel.Instance.OpenMenu();
            UIPopupPanel.Instance.Setup("Warning!!", S_T_Warning2,
                () =>
                {
                    TournamentManager.Instance.DeleteSponsoredTournament(_tournamentData._id).GetAwaiter()
                        .OnCompleted(CloseMenu);
                }, null);
        }

        public void SetTournament(SerializeSponsoredTournament tournament)
        {
            _tournamentData = tournament;
        }

        private async UniTask LoadImages()
        {
            if (!string.IsNullOrEmpty(_tournamentData.banner_url))
            {
                var img = await Helpers.GetTextureToSprite(_tournamentData.banner_url, _cancellationTokenSource.Token);
                _tournamentData.SetImage(img, SponsorImageType.Banner);
            }
            if (!string.IsNullOrEmpty(_tournamentData.loading_url))
            {
                var img = await Helpers.GetTextureToSprite(_tournamentData.loading_url, _cancellationTokenSource.Token);
                _tournamentData.SetImage(img, SponsorImageType.Loading);
            }
        }
        public void InitData()
        {
            LocalizedString notAllowed = "Not Allowed";
            LocalizedString allowed = "Allowed";
            LocalizedString required = "Required";
            LocalizedString roundRobin = "Round Robin";
            LocalizedString elimination = "Elimination";
            LocalizedString random = "Random";
            LocalizedString unmovable = "Unmovable";
            LocalizedString damage = "Damage";
            LocalizedString none = "None";
            LocalizedString alOnly = "SponsorTournament/AL only";
            LocalizedString priority = "SponsorTournament/Priority";

            LockDownMode(true);
            OpenMenu();
            OpenDetail(true);
            openDetailBtn.gameObject.SetActive(false);
            closeDetailBtn.gameObject.SetActive(false);
            countdownText.text = CountdownText;
            bannerImage.gameObject.SetActive(true);
            bannerImage.sprite = _tournamentData.ThumbnailImage;
            rewardObjectIndicator.SetActive(_tournamentData.has_reward);
            tournamentName.text = _tournamentData.tournament_name;
            date.text = $"{_tournamentData.date_string} UTC";
            registrationDate.text =
                $"{_tournamentData.date_format.AddMinutes(-1441):MMM. dd, yyyy HH:mm} UTC";
            entryPeriod.text =
                $"{_tournamentData.date_format.AddMinutes(-30):MMM. dd, yyyy HH:mm} ~ {_tournamentData.date_format.AddMinutes(-10):HH:mm} UTC";
            jumpInPeriod.text =
                $"{_tournamentData.date_format.AddMinutes(-10):MMM. dd, yyyy HH:mm} ~ {_tournamentData.date_format.AddMinutes(-1):HH:mm} UTC";
            sponsorName.text = _tournamentData.sponsor_name;
            sponsorId.text = _tournamentData._id[^5..];
            type.text = _tournamentData.type == TournamentType.Elimination ? elimination : roundRobin;
            participants.text = _tournamentData.participants.ToString();
            registrations.text = _tournamentData.register_list is null
                ? "0"
                : _tournamentData.register_list.Count.ToString();
            allowList.text = !_tournamentData.use_allow_list ? none :
                _tournamentData.allow_list_only ? alOnly : priority;
            jumpIn.text = _tournamentData.allow_list_only ? notAllowed : _tournamentData.jump_in ? allowed : notAllowed;
            prize.text = _tournamentData.prize?.Where(t => t > 0).Select((t, i) => $"#{i + 1} <sprite=0> {t}, ")
                .Aggregate("", (current, text) => current + text);
            if (prize.text?.Length > 0)
                prize.text = prize.text.Remove(prize.text.Length - 2);
            rank.text = _tournamentData.monster_rank.ToString();
            monsters.text = _tournamentData.monsters.ToString();
            turns.text = _tournamentData.turns.ToString();
            specialHex.text = _tournamentData.special_hex switch
            {
                SpecialHexType.Random => random,
                SpecialHexType.Unmovable => unmovable,
                SpecialHexType.Damage => damage,
                _ => none
            };
            memory.text = _tournamentData.memory ? allowed : notAllowed;
            sms.text = _tournamentData.sms ? required : "N/R";
            playerRank.text = _tournamentData.player_rank.ToString().ConvertStringPlus();
            cycleScore.text = $"{_tournamentData.cycle_score_min.ToString()}~";
            battles.text = $"{_tournamentData.battles_min.ToString()}~";
            playingDays.text = $"{_tournamentData.playing_days_min.ToString()}~";
            monsterRequirementUI.SetData(_tournamentData);
            LoadImages().Forget();
        }

        public void UpdateTournamentState(ManageStatus manageStatus)
        {
            TurnOffAllBottomBtn();
            switch (_tournamentData.state)
            {
                case TournamentStateEnum.Registration:
                    registrationBtn.gameObject.SetActive(true);
                    registrationBtn.interactable = false;
                    if (_tournamentData.use_allow_list)
                    {
                        if (_tournamentData.allow_list is not null &&
                            _tournamentData.allow_list.Contains(GameDataManager.Instance.LoadedPlayerData.PlayerWallet))
                            registrationBtn.interactable = true;
                    }
                    else
                    {
                        registrationBtn.interactable = true;
                    }

                    if (_tournamentData.register_list is not null)
                    {
                        var registered =
                            _tournamentData.register_list.Contains(GameDataManager.Instance.LoadedPlayerData
                                .PlayerWallet);
                        registrationBtn.gameObject.SetActive(!registered);
                        cancelRegistrationBtn.gameObject.SetActive(registered);
                    }

                    break;
                case TournamentStateEnum.Upcoming:
                    countdownBtn.gameObject.SetActive(true);
                    break;
                case TournamentStateEnum.Entry:
                    entryBtn.gameObject.SetActive(true);
                    if (_tournamentData.register_list.Contains(GameDataManager.Instance.LoadedPlayerData.PlayerWallet))
                    {
                        entryBtn.interactable = true;
                    }
                    else
                    {
                        entryBtn.interactable = false;
                        watchBtn.gameObject.SetActive(true);
                    }
                    break;
                case TournamentStateEnum.JumpIn:
                    jumpInBtn.gameObject.SetActive(true);
                    watchBtn.gameObject.SetActive(true);
                    if (_tournamentData.allow_list_only)
                    {
                        jumpInBtn.interactable = _tournamentData.allow_list.Contains(GameDataManager.Instance.LoadedPlayerData.PlayerWallet);
                    }
                    break;
                case TournamentStateEnum.InProgress:
                    watchBtn.gameObject.SetActive(true);
                    entryBtn.gameObject.SetActive(
                        _tournamentData.entry_list.Contains(GameDataManager.Instance.LoadedPlayerData.PlayerWallet));
                    break;
                case TournamentStateEnum.Ended:
                    resultBtn.gameObject.SetActive(true);
                    resultBtn.interactable = _tournamentData.final_standings is not null &&
                                             _tournamentData.final_standings.Count > 0;
                    break;
                case TournamentStateEnum.Draft:
                    editBtn.gameObject.SetActive(true);
                    editBtn.interactable = !(_tournamentData.blockchain_id != 0 && !_tournamentData.is_deposit_oas);
                    deleteBtn.gameObject.SetActive(true);
                    statusManageText.text = nameof(ManageStatus.Draft);
                    break;
                default:
                    Debug.LogError("No state found");
                    break;
            }

            switch (manageStatus)
            {
                case ManageStatus.Opened:
                    statusObject.SetActive(true);
                    LocalizedString S_T_Opened = "SponsorTournament/S_T_Opened";
                    statusManageText.text = S_T_Opened;
                    cancelRegistrationBtn.gameObject.SetActive(false);
                    countdownBtn.gameObject.SetActive(false);
                    registrationBtn.gameObject.SetActive(false);
                    editALBtn.gameObject.SetActive(true);
                    cancelOpenedBtn.gameObject.SetActive(true);
                    break;
                case ManageStatus.Draft:
                    statusObject.SetActive(true);
                    LocalizedString S_T_Draft = "SponsorTournament/S_T_Draft";
                    statusManageText.text = S_T_Draft;
                    registrationBtn.gameObject.SetActive(false);
                    editBtn.gameObject.SetActive(true);
                    editBtn.interactable = !(_tournamentData.blockchain_id != 0 && !_tournamentData.is_deposit_oas);
                    deleteBtn.gameObject.SetActive(true);
                    if (_tournamentData.is_deposit_oas)
                    {
                        editBtn.gameObject.SetActive(false);
                        deleteBtn.gameObject.SetActive(false);
                        redeemBtn.gameObject.SetActive(true);
                        redeemBtn.onClick.RemoveAllListeners();
                        redeemBtn.onClick.AddListener(() => RedeemBlockchain().GetAwaiter());
                    }

                    break;
                case ManageStatus.Past:
                    statusObject.SetActive(true);
                    LocalizedString S_T_Past = "SponsorTournament/S_T_Past";
                    statusManageText.text = S_T_Past;
                    resultBtn.gameObject.SetActive(true);
                    redeemBtn.gameObject.SetActive(true);
                    resultBtn.interactable = _tournamentData.final_standings is not null &&
                                             _tournamentData.final_standings.Count > 0;
                    redeemBtn.interactable = _tournamentData.is_deposit_oas;
                    redeemBtnText.text = _tournamentData.is_deposit_oas ? "Redeem" : "Redeemed";
                    if (_tournamentData.is_deposit_oas)
                    {
                        redeemBtn.onClick.RemoveAllListeners();
                        redeemBtn.onClick.AddListener(() => RedeemBlockchain().GetAwaiter());
                    }

                    break;
                case ManageStatus.None:
                default:
                    statusObject.SetActive(false);
                    break;
            }
        }

        private void TurnOffAllBottomBtn()
        {
            registrationBtn.gameObject.SetActive(false);
            watchBtn.gameObject.SetActive(false);
            jumpInBtn.gameObject.SetActive(false);
            entryBtn.gameObject.SetActive(false);
            countdownBtn.gameObject.SetActive(false);
            editBtn.gameObject.SetActive(false);
            deleteBtn.gameObject.SetActive(false);
            resultBtn.gameObject.SetActive(false);
            redeemBtn.gameObject.SetActive(false);
            editALBtn.gameObject.SetActive(false);
            cancelOpenedBtn.gameObject.SetActive(false);
            cancelRegistrationBtn.gameObject.SetActive(false);
            registrationBtn.interactable = true;
            watchBtn.interactable = true;
            jumpInBtn.interactable = true;
            entryBtn.interactable = true;
            countdownBtn.interactable = true;
            editBtn.interactable = true;
            deleteBtn.interactable = true;
            resultBtn.interactable = true;
            redeemBtn.interactable = true;
            editALBtn.interactable = true;
            cancelOpenedBtn.interactable = true;
            cancelRegistrationBtn.interactable = true;
            redeemBtnText.text = "Redeem";
        }

        private async void RegistrationTournament(bool isCancel)
        {
            if (TournamentManager.Instance.blacklist.Contains(GameDataManager.Instance.LoadedPlayerData.PlayerWallet))
            {
                UIPopupNotify.Instance.SetNotify("Error!", "You were ban from sponsored tournament.");
                return;
            }
            
            if (_conditionCount > 0)
                return;

            if (_tournamentData.allow_list_only &&
                !_tournamentData.allow_list.Contains(GameDataManager.Instance.LoadedPlayerData.PlayerWallet))
                return;


            var loading = BackendLoadData.Instance.LoadingCanvas("RegistrationTournament");
            try
            {
                await TournamentManager.Instance.RegisterTournament(_tournamentData._id);
                registrationBtn.gameObject.SetActive(isCancel);
                cancelRegistrationBtn.gameObject.SetActive(!isCancel);
                Destroy(loading);
            }
            catch (Exception e)
            {
                registrationBtn.gameObject.SetActive(!isCancel);
                cancelRegistrationBtn.gameObject.SetActive(isCancel);
                Debug.LogError(e.Message);
                Destroy(loading);
            }
        }

        public void OpenTournamentDetails(bool allowQuit = false)
        {
            LockDownMode(allowQuit);
            OpenDetail(false);
            openDetailBtn.gameObject.SetActive(true);
        }

        private async UniTask JoinTournamentRoom(Button button, bool isWatch = false)
        {
            if (TournamentManager.Instance.blacklist.Contains(GameDataManager.Instance.LoadedPlayerData.PlayerWallet))
                return;
            
            if (TournamentManager.Instance.Room is not null) OpenTournamentDetails();

            if (_conditionCount > 0)
                return;

            var loading = BackendLoadData.Instance.LoadingCanvas("JoinTournamentRoom");
            try
            {
                button.interactable = false;
                await TournamentManager.Instance.JoinRoom(_tournamentData, isWatch);
                OpenTournamentDetails();
                Destroy(loading);
            }
            catch (Exception e)
            {
                Debug.LogError(e.Message);
                Destroy(loading);
                button.interactable = true;
            }
        }

        private void CanJoinTournament()
        {
            if (TournamentManager.Instance.blacklist.Contains(GameDataManager.Instance.LoadedPlayerData.PlayerWallet))
            {
                UIPopupNotify.Instance.SetNotify("Error!", "You were ban from sponsored tournament.");
                return;
            }
            
            LocalizedString sms = "SMS Verification";
            LocalizedString playerRank = "Player Rank";
            LocalizedString cycleScore = "Cycle Score";
            LocalizedString battles = "Battles";
            LocalizedString playingDays = "Playing Days";
            LocalizedString regeneration = "Regeneration";
            LocalizedString fusion = "Fusion";
            LocalizedString free = "Free";
            LocalizedString gift = "Gift";
            LocalizedString monster = "Monster";
            LocalizedString cycle = "Cycle";
            LocalizedString age = "Age";
            LocalizedString terrain = "Terrain";
            LocalizedString mainSeed = "Main Seed";
            LocalizedString subSeed = "Sub Seed";

            _conditionCount = 0;
            _conditionMessage.Clear();
            var monstersData = GameDataManager.Instance.LoadedPlayerData.PlayerMonsterList;
            if (_tournamentData.sms != GameDataManager.Instance.LoadedPlayerData.IsPlayerPhoneVerified)
            {
                _conditionCount++;
                _conditionMessage.Add($"{sms}. ");
            }

            if (GameProgressManager.Instance.GameProgress.PlayerRank < _tournamentData.player_rank)
            {
                _conditionCount++;
                _conditionMessage.Add($"{playerRank}. ");
            }
            float userScore = 0;
            var userSectorScore = ScoreManager.Instance.ScoreSnapshot.FirstOrDefault(x => x.ScoreUser == GameDataManager.Instance.LoadedPlayerData.PlayerWallet && x.FromDate == CycleManager.Instance.CurrentCycle.CycleStartTime && x.ToDate == CycleManager.Instance.CurrentCycle.CycleEndTime && x.ScoreType == ScoreController.SECTOR_SNAPSHOT);
            if (userSectorScore != null)
            {
                userScore = userSectorScore.ScoreValue;
            }
            if (_tournamentData.cycle_score_specified && _tournamentData.cycle_score_min > Mathf.FloorToInt(userScore))
            {
                _conditionCount++;
                _conditionMessage.Add($"{cycleScore}. ");
            }

            if (_tournamentData.battles_specified && _tournamentData.battles_min > CustomNetworkManager.Instance.GetTotalWin() + CustomNetworkManager.Instance.GetTotalLose())
            {
                _conditionCount++;
                _conditionMessage.Add($"{battles}. ");
            }

            if (_tournamentData.playing_days_specified && _tournamentData.playing_days_min > GameDataManager.Instance.LoadedPlayerData.TotalLoginDay)
            {
                _conditionCount++;
                _conditionMessage.Add($"{playingDays}. ");
            }
            
            if (_tournamentData.cycle is not null && _tournamentData.cycle.Count > 0)
            {
                if (monstersData.All(m => m.CycleNumber < _tournamentData.cycle[0] || m.CycleNumber > _tournamentData.cycle[1]))
                {
                    _conditionCount++;
                    _conditionMessage.Add($"{monster} {regeneration}. ");
                }
            }
            if (_tournamentData.monster_type is not null && _tournamentData.monster_type.Count < 4)
            {
                foreach (var allowMonsterType in _tournamentData.monster_type)
                {
                    var success = false;
                    switch (allowMonsterType)
                    {
                        case 0 when monstersData.Any(m => m.IsFree):
                        case 1 when monstersData.Any(m => m.MintType != 10):
                        case 2 when monstersData.Any(m => m.MintType == 10):
                        case 3 when monstersData.Any(m => m.IsFree):
                            success = true;
                            break;
                        default:
                            _conditionCount++;
                            _conditionMessage.Add("Monster type");
                            break;
                    }
                    if (success) break;
                }
            }
            //
            // if (monstersData.All(m => m.MintType == 10) && _tournamentData.monster_type.Contains(1))
            // {
            //     _conditionCount++;
            //     _conditionMessage.Add($"{monster} {regeneration}. ");
            // }
            //
            // if (monstersData.All(m => m.MintType != 10) && _tournamentData.monster_type.Contains(2))
            // {
            //     _conditionCount++;
            //     _conditionMessage.Add($"{monster} {fusion}. ");
            // }
            //
            // if (monstersData.All(m => m.IsFree) && _tournamentData.monster_type.Contains(0))
            // {
            //     _conditionCount++;
            //     _conditionMessage.Add($"{monster} {free}. ");
            // }
            //
            // if (monstersData.All(m => m.IsFree) && _tournamentData.monster_type.Contains(3))
            // {
            //     _conditionCount++;
            //     _conditionMessage.Add($"{monster} {gift}. ");
            // }

            if (monstersData.All(m => 
                    m.MonsterAge < _tournamentData.age_min || _tournamentData.age_max < m.MonsterAge))
            {
                _conditionCount++;
                _conditionMessage.Add($"{monster} {age}. ");
            }

            if (_tournamentData.terrain_ban is not null && _tournamentData.terrain_ban.Count > 0)
            {
                var listMonsterMainTerrainComp =
                    monstersData.Select(m => (int)m.MonsterScriptableData.MonsterMainTerrainComp).Distinct()
                        .Except(_tournamentData.terrain_ban);
                var listMonsterSubTerrainComp =
                    monstersData.Select(m => (int)m.MonsterScriptableData.MonsterSubTerrainComp).Distinct()
                        .Except(_tournamentData.terrain_ban);
                if (!listMonsterMainTerrainComp.Any() || !listMonsterSubTerrainComp.Any())
                {
                    _conditionCount++;
                    _conditionMessage.Add($"{monster} {terrain}. ");
                }
            }

            if (_tournamentData.main_seed_ban is not null && _tournamentData.main_seed_ban.Count > 0)
            {
                var listMainSeed =
                    monstersData.Select(m => (int)m.MonsterScriptableData.MonsterMainSeed).Distinct()
                        .Except(_tournamentData.main_seed_ban);
                if (!listMainSeed.Any())
                {
                    _conditionCount++;
                    _conditionMessage.Add($"{monster} {mainSeed}. ");
                }
            }

            if (_tournamentData.sub_seed_ban is not null && _tournamentData.sub_seed_ban.Count > 0)
            {
                var listSubSeed =
                    monstersData.Select(m => (int)m.MonsterScriptableData.MonsterSubSeed).ToList()
                        .Except(_tournamentData.sub_seed_ban);
                if (!listSubSeed.Any())
                {
                    _conditionCount++;
                    _conditionMessage.Add($"{monster} {subSeed}. ");
                }
            }


            if (_conditionCount == 0)
                return;

            LocalizedString warning3 = "SponsorTournament/S_T_Warning3";
            UIPopupNotify.Instance.OpenMenu();
            UIPopupNotify.Instance.SetNotify("Warning!!",
                $"{warning3} " + _conditionMessage.Aggregate("", (current, text) => current + text));
        }

        private void LockDownMode(bool allowQuit)
        {
            // backBtn.interactable = allowQuit;
            BackgroundCloseBtn.interactable = allowQuit;
        }

        public void ExitEntry()
        {
            OpenDetail(true);
            LockDownMode(true);
        }

        // [ContextMenu("TestOpenFalse")]
        // public void TestOpenFalse()
        // {
        //     OpenDetail(false);
        // }
        // [ContextMenu("TestOpenTrue")]
        // public void TestOpenTrue()
        // {
        //     OpenDetail(true);
        // }
    }

    public enum ManageStatus
    {
        None,
        Draft,
        Opened,
        Past
    }
}