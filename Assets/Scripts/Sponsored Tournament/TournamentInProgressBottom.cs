using System;
using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.DataStruct;
using Assets.Scripts.Helper;
using Assets.Scripts.Managers;
using Assets.Scripts.Tournament;
using Assets.Scripts.UI;
using Assets.Scripts.UI.BattleUI.PvP;
using Assets.Scripts.UI.Utilities;
using Colyseus;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using Network;
using Network.RoomState;
using Network.TournamentState;
using TMPro;
using UI.BattleUI.ItemReward;
using UI.HomeMenu;
using UI.UI_Sponsored_Tournament;
using UI.UIFriend;
using UI.Utilities;
using UnityEngine;
using UnityEngine.UI;

namespace Sponsored_Tournament
{
    public class TournamentInProgressBottom : MonoBehaviour
    {
        [SerializeField] private Button readyButton;
        [SerializeField] private Button exitButton;
        [SerializeField] private Button watchButton;
        [SerializeField] private Button resultButton;
        [SerializeField] private Button progressButton;
        [SerializeField] private Button claimButton;

        [SerializeField] private GameObject waiting;
        [SerializeField] private GameObject vs;

        [SerializeField] private GameObject claimGameObject;
        [SerializeField] private TextMeshProUGUI claimPrize;

        [SerializeField] private GameObject countDownObject;
        [SerializeField] private TextMeshProUGUI countDownText;
        [SerializeField] private Slider slider;

        [SerializeField] private TextMeshProUGUI player1Text;
        [SerializeField] private Image player1avatar;
        [SerializeField] private TextMeshProUGUI player2Text;
        [SerializeField] private Image player2avatar;

        [SerializeField] private UIMonsterPartySettingMultiplayer partySettingMenu;
        [SerializeField] private UIItemRewardMenu rewardMenu;


        private double _prizeValue;
        private string _roomId;
        private SerializeSponsoredTournament _tournament;
        private SponsoredTournamentInfo _info;
        private ColyseusRoom<GameState> _room;
        private TournamentState _tournamentState;
        private SponsorTournamentEl _tournamentEl;
        private SponsorTournamentRR _tournamentRr;
        private SponsorTournamentResult _result;
        private void Start()
        {
            partySettingMenu = transform.root.GetComponentInChildren<UIMonsterPartySettingMultiplayer>(true);
            progressButton.onClick.AddListener(() =>
            {
                if (_tournamentState is null)
                {
                    progressButton.gameObject.SetActive(false);
                    resultButton.gameObject.SetActive(true);
                    _result.gameObject.SetActive(false);
                    switch (_tournament.type)
                    {
                        case TournamentType.Elimination:
                            _tournamentEl.gameObject.SetActive(true);
                            _tournamentEl.InitItem(_tournament, null, _result, _info);
                            break;
                        case TournamentType.RoundRobin:
                            _tournamentRr.gameObject.SetActive(true);
                            _tournamentRr.InitItem(_tournament, null, _result, _info);
                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }
                
                else
                {
                    progressButton.gameObject.SetActive(false);
                    resultButton.gameObject.SetActive(true);
                    _result.gameObject.SetActive(false);
                    switch (_tournamentState.metadata.type)
                    {
                        case 0:
                            _tournamentEl.gameObject.SetActive(true);
                            _tournamentEl.InitItem(_tournament, _tournamentState, _result, _info);
                            break;
                        case 1:
                            _tournamentRr.gameObject.SetActive(true);
                            _tournamentRr.InitItem(_tournament, _tournamentState, _result, _info);
                            break;
                    }
                }
            });
        }

        public void InitDataResult(SerializeSponsoredTournament tournament, TournamentState tournamentState,
            SponsorTournamentEl tournamentEl, SponsorTournamentRR tournamentRr, SponsorTournamentResult result,
            SponsoredTournamentInfo info)
        {
            _info = info;
            _tournament = tournament;
            _tournamentEl = tournamentEl;
            _tournamentRr = tournamentRr;
            _result = result;
            _tournamentState = tournamentState;
            
            ActiveFalse();

            progressButton.gameObject.SetActive(true);
            exitButton.gameObject.SetActive(true);
            exitButton.onClick.RemoveAllListeners();
            exitButton.onClick.AddListener(() =>
            {
                ExitAction(info);
                CustomNetworkManager.Instance?.LeaveRoom();
            });

            var rank1Count = 0;
            var rank2Count = 0;
            var rank3Count = 0;
            
            if (tournamentState is null)
            {
                if (tournament.state != TournamentStateEnum.Ended) return;
                
                if (tournament.prize is null || tournament.prize.Count == 0)
                    return;
                foreach (var finalStanding in tournament.final_standings)
                {
                    switch (finalStanding.rank)
                    {
                        case 1:
                            rank1Count++;
                            break;
                        case 2:
                            rank2Count++;
                            break;
                        case 3:
                            rank3Count++;
                            break;
                    }
                }

                foreach (var finalStanding in tournament.final_standings
                             .Where(finalStanding =>
                                 finalStanding.name == GameDataManager.Instance.LoadedPlayerData.PlayerWallet))
                {
                    switch (finalStanding.rank)
                    {
                        case 1 when tournament.prize[0] > 0:
                            _prizeValue = tournament.prize[0] / (rank1Count == 0 ? 1 : rank1Count);
                            ClaimRewardButton(1).Forget();
                            claimPrize.text = $"<sprite=0> {_prizeValue}";
                            break;
                        case 2 when tournament.prize[1] > 0:
                            _prizeValue = tournament.prize[1] / (rank2Count == 0 ? 1 : rank2Count);
                            ClaimRewardButton(2).Forget();
                            claimPrize.text = $"<sprite=0> {_prizeValue}";
                            break;
                        case 3 when tournament.prize[2] > 0:
                            _prizeValue = tournament.prize[2] / (rank3Count == 0 ? 1 : rank3Count);
                            ClaimRewardButton(3).Forget();
                            claimPrize.text = $"<sprite=0> {_prizeValue}";
                            break;
                    }
                }
            }
            else
            {
                if (tournamentState.state != 5) return;

                if (tournament.prize is null || tournament.prize.Count == 0)
                    return;
                foreach (var finalStanding in tournamentState.final_standings.items.Values)
                {
                    switch (finalStanding.rank)
                    {
                        case 1:
                            rank1Count++;
                            break;
                        case 2:
                            rank2Count++;
                            break;
                        case 3:
                            rank3Count++;
                            break;
                    }
                }

                foreach (var finalStanding in tournamentState.final_standings.items.Values.Where(finalStanding =>
                             finalStanding.name == GameDataManager.Instance.LoadedPlayerData.PlayerWallet))
                {
                    switch (finalStanding.rank)
                    {
                        case 1 when tournament.prize[0] > 0:
                            _prizeValue = tournament.prize[0] / (rank1Count == 0 ? 1 : rank1Count);
                            ClaimRewardButton(1).Forget();
                            claimPrize.text = $"<sprite=0> {_prizeValue}";
                            break;
                        case 2 when tournament.prize[1] > 0:
                            _prizeValue = tournament.prize[1] / (rank2Count == 0 ? 1 : rank2Count);
                            ClaimRewardButton(2).Forget();
                            claimPrize.text = $"<sprite=0> {_prizeValue}";
                            break;
                        case 3 when tournament.prize[2] > 0:
                            _prizeValue = tournament.prize[2] / (rank3Count == 0 ? 1 : rank3Count);
                            ClaimRewardButton(3).Forget();
                            claimPrize.text = $"<sprite=0> {_prizeValue}";
                            break;
                    }
                }
            }
        }

        private static void ExitAction(SponsoredTournamentInfo info)
        {
            if (TournamentManager.Instance.Room is null)
            {
                info.CloseMenu();
                TournamentManager.Instance.UpdateTournament.Invoke(-1);
                return;
            }

            TournamentManager.Instance.LeaveRoom().GetAwaiter().OnCompleted(() =>
            {
                info.CloseMenu();
                TournamentManager.Instance.UpdateTournament.Invoke(-1);
            });
        }

        private async UniTask ClaimRewardButton(int rank)
        {
            try
            {
                claimGameObject.gameObject.SetActive(true);
                claimPrize.gameObject.SetActive(true);
                claimButton.gameObject.SetActive(true);

                var blockchainTournament = await TournamentManager.Instance.GetListTournamentBlockchain();
                var tournamentBlockchainData =
                    blockchainTournament.listTournament.FirstOrDefault(t => t.id == _tournament.blockchain_id);

                if (tournamentBlockchainData is not null)
                    switch (rank)
                    {
                        case 1:
                            if (float.Parse(tournamentBlockchainData.rankingReward.ranking1.remaining) > 0)
                                claimButton.interactable = true;
                            break;
                        case 2:
                            if (float.Parse(tournamentBlockchainData.rankingReward.ranking2.remaining) > 0)
                                claimButton.interactable = true;
                            break;
                        case 3:
                            if (float.Parse(tournamentBlockchainData.rankingReward.ranking3.remaining) > 0)
                                claimButton.interactable = true;
                            break;
                        default:
                            claimButton.interactable = false;
                            break;
                    }
                claimButton.onClick.RemoveAllListeners();
                claimButton.onClick.AddListener(() => ClaimRewardBlockchain().Forget());
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private bool _tryClaim;
        public async UniTask ClaimRewardBlockchain()
        {
            if (_tryClaim)
                return;
            var loading = BackendLoadData.Instance.LoadingCanvas("ClaimRewardBlockchain");
            claimButton.interactable = false;
            try
            {
                _tryClaim = true;
                var blockchainSign =
                    await TournamentManager.Instance.SignTournamentBlockchain(_tournament.blockchain_id,
                        GameDataManager.Instance.LoadedPlayerData.PlayerWallet);
                if (blockchainSign.statusCode == 400)
                {
                    UIPopupNotify.Instance.SetNotify("Error", $"{blockchainSign.message}");
                    return;
                }

                await SmartContractInteract.Instance.ClaimTournamentReward(blockchainSign.data.typeRanking,
                    blockchainSign.data.id, blockchainSign.data.account, blockchainSign.data.reward.ToString(),
                    blockchainSign.data.deadline, blockchainSign.data.signature);
                BackendLoadData.Instance.LoadOasBalance().Forget();
            }
            catch (Exception e)
            {
                UIPopupNotify.Instance.SetNotify("Error", $"{e.Message}");
                Debug.LogError(e);
            }
            finally
            {
                Destroy(loading);
                rewardMenu.OpenMenu();
                rewardMenu.ClearItemsList();
                rewardMenu.SetOas((float)_prizeValue);
            }
        }

        public async void InitInprogressImage(string player1, string player2, string roomId)
        {
            // ActiveFalse();
            _roomId = roomId;
            player1Text.gameObject.SetActive(true);
            player1avatar.gameObject.SetActive(true);
            vs.gameObject.SetActive(true);
            player2Text.gameObject.SetActive(true);
            player2avatar.gameObject.SetActive(true);
            player1Text.text = player1;
            player2Text.text = player2;
            var user1 = await FriendDataManager.Instance.GetUserData(player1);
            var user2 = await FriendDataManager.Instance.GetUserData(player2);
            player1Text.text = user1.user_name;
            player2Text.text = user2.user_name;
            player1avatar.sprite = user1.userIcon;
            player2avatar.sprite = user2.userIcon;
            if (player1 == GameDataManager.Instance.LoadedPlayerData.PlayerWallet ||
                player2 == GameDataManager.Instance.LoadedPlayerData.PlayerWallet) return;
            WatchAction();
        }

        private void WatchAction()
        {
            watchButton.gameObject.SetActive(true);
            watchButton.interactable = !string.IsNullOrEmpty(_roomId);
            watchButton.onClick.AddListener(WatchMatchProgress);
            exitButton.gameObject.SetActive(true);
            exitButton.onClick.RemoveAllListeners();
            exitButton.onClick.AddListener(() => ExitAction(_info));
        }

        public async UniTask InitInprogress(string player1, string player2, SponsoredTournamentInfo info)
        {
            if (player1 != GameDataManager.Instance.LoadedPlayerData.PlayerWallet && 
                player2 != GameDataManager.Instance.LoadedPlayerData.PlayerWallet)
                return;
            
            try
            {
                ActiveFalse();
                player1Text.gameObject.SetActive(true);
                player1avatar.gameObject.SetActive(true);
                vs.gameObject.SetActive(true);
                player2Text.gameObject.SetActive(true);
                player2avatar.gameObject.SetActive(true);
                player1Text.text = player1;
                player2Text.text = player2;
                var user1 = await FriendDataManager.Instance.GetUserData(player1);
                var user2 = await FriendDataManager.Instance.GetUserData(player2);
                player1Text.text = user1.user_name;
                player2Text.text = user2.user_name;
                player1avatar.sprite = user1.userIcon;
                player2avatar.sprite = user2.userIcon;
                if (player1 != GameDataManager.Instance.LoadedPlayerData.PlayerWallet &&
                    player2 != GameDataManager.Instance.LoadedPlayerData.PlayerWallet)
                {
                    WatchAction();
                } 
                else
                {
                    countDownObject.gameObject.SetActive(true);
                    readyButton.gameObject.SetActive(true);
                    countDownText.gameObject.SetActive(true);
                    slider.gameObject.SetActive(true);

                    var currentRoom = CustomNetworkManager.Instance.GetRoom();
                    const float countdownDuration = 30f;
                    float currentTime;
                    slider.maxValue = countdownDuration;
                    slider.value = countdownDuration;
                    slider.DOValue(0, countdownDuration).SetEase(Ease.Linear).OnUpdate(() =>
                    {
                        currentTime = slider.value;

                        // Calculate minutes and seconds
                        var minutes = Mathf.FloorToInt(currentTime / 60);
                        var seconds = Mathf.FloorToInt(currentTime % 60);

                        // Update the countdown text (format: Remaining... mm:ss)
                        countDownText.text = $"{minutes:0}:{seconds:00}";
                    }).OnComplete(() =>
                    {
                        countDownText.text = "0:00";
                        if (currentRoom.State.players.items.Values.FirstOrDefault(p =>
                                p.walletId == GameDataManager.Instance.LoadedPlayerData.PlayerWallet)?.state !=
                            PlayerState.NotReady) return;
                        currentRoom.Send("player_ready",
                            new { content = GameDataManager.Instance.LoadedPlayerData.PlayerId }).GetAwaiter();
                        Debug.Log("Player Ready: " + GameDataManager.Instance.LoadedPlayerData.PlayerId);
                        readyButton.interactable = false;
                        PlayerReady(info);
                    });
                    readyButton.onClick.RemoveAllListeners();
                    readyButton.onClick.AddListener(() =>
                    {
                        countDownText.text = "0:00";
                        slider.value = 0;
                        currentRoom.Send("player_ready",
                            new { content = GameDataManager.Instance.LoadedPlayerData.PlayerId }).GetAwaiter();
                        Debug.Log("Player Ready: " + GameDataManager.Instance.LoadedPlayerData.PlayerId);
                        readyButton.interactable = false;
                        PlayerReady(info);
                    });
                    
                }
            }
            catch (Exception e)
            {
                Debug.LogError(e.Message);
                info.CloseMenu();
            }
        }

        private async void WatchMatchProgress()
        {
            if (string.IsNullOrEmpty(_roomId)) return;
            
            var loading = BackendLoadData.Instance.LoadingCanvas("WatchMatchProgress");
            try
            {
                var options = new Dictionary<string, object>
                {
                    { "playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId },
                    { "token", BackendLoadData.Instance.token },
                    { "walletId", GameDataManager.Instance.LoadedPlayerData.PlayerWallet }
                };
                
                await CustomNetworkManager.Instance.ConnectToRoomAsync(_roomId, options);
                _room = CustomNetworkManager.Instance.GetRoom();
                await _room.Send("player_watch");
                _room.OnStateChange += OnStateChange;
                if (_room.State.players[0].state != "ReadyToPlay" || _room.State.players[1].state != "ReadyToPlay" || 
                    _room.State.players[0].state != "Disconnect" || _room.State.players[1].state != "Disconnect")
                {
                    UIPopupPanel.Instance.OpenMenu();
                    UIPopupPanel.Instance.SetupCancel("Waiting...","Party setting in progress.", () =>
                    {
                        _room.OnStateChange -= OnStateChange;
                        _room.Leave().GetAwaiter();
                    });
                }
                Destroy(loading);
            }
            catch (Exception e)
            {
                Destroy(loading);
                Debug.LogError(e);
                watchButton.interactable = false;
            }
        }

        private void OnStateChange(GameState state, bool firstState)
        {
            if (!state.isInBattle) return;
            if (state.players[0].state != "ReadyToPlay" || state.players[1].state != "ReadyToPlay" || 
                state.players[0].state != "Disconnect" || state.players[1].state != "Disconnect")
            {
                return;
            }
            CustomNetworkManager.Instance.previousGameMode = PvpGameMode.TournamentMatch;
            _room.OnStateChange -= OnStateChange;
            SceneHelper.Instance.LoadScene(2, 1, _tournament);
        }

        private void PlayerReady(SponsoredTournamentInfo info)
        {
            readyButton.interactable = false;
            info.CloseMenu();
            var uiPvpBattleMenu = transform.root.GetComponentInChildren<UIPVPBattleMenu>(true);
            uiPvpBattleMenu.Toggle(false);
            uiPvpBattleMenu.CloseMenu();
            partySettingMenu.SetUIBattleRoom(null);
        }

        public void InitEntry(bool watchMode, Action exitAction)
        {
            ActiveFalse();
            waiting.gameObject.SetActive(true);
            exitButton.gameObject.SetActive(watchMode);
            if (watchMode)
                exitButton.onClick.AddListener(() => exitAction?.Invoke());
        }

        public void TournamentEnded(SerializeSponsoredTournament tournament, TournamentState tournamentState,
            SponsorTournamentEl tournamentEl, SponsorTournamentRR tournamentRr, SponsorTournamentResult result,
            SponsoredTournamentInfo info)
        {
            ActiveFalse();
            resultButton.gameObject.SetActive(true);
            resultButton.onClick.RemoveAllListeners();
            resultButton.onClick.AddListener(() =>
            {
                result.gameObject.SetActive(true);
                tournamentEl?.gameObject.SetActive(false);
                tournamentRr?.gameObject.SetActive(false);
                result.AddResultItem(tournament, tournamentState, tournamentEl, tournamentRr, info);
            });
        }

        public void ActiveFalse()
        {
            readyButton.gameObject.SetActive(false);
            exitButton.gameObject.SetActive(false);
            watchButton.gameObject.SetActive(false);
            resultButton.gameObject.SetActive(false);
            progressButton.gameObject.SetActive(false);
            claimButton.gameObject.SetActive(false);
            claimGameObject.gameObject.SetActive(false);
            claimPrize.gameObject.SetActive(false);
            countDownText.gameObject.SetActive(false);
            slider.gameObject.SetActive(false);
            player1Text.gameObject.SetActive(false);
            player1avatar.gameObject.SetActive(false);
            player2Text.gameObject.SetActive(false);
            player2avatar.gameObject.SetActive(false);
            countDownObject.gameObject.SetActive(false);
            waiting.gameObject.SetActive(false);
            vs.gameObject.SetActive(false);
        }
    }
}