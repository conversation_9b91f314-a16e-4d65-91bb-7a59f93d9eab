using UnityEngine;
using Assets.Scripts.SerializeDataStruct;
using Assets.Scripts.Enums;
using Assets.Scripts.Managers;
using Assets.Scripts.Scriptables;
using CustomBattle;

public class BuffSpriteGO : MonoBehaviour
{
    [SerializeField]
    GameObject buff, debuff, mentalBuff, mentalDebuff;

    public void ActiveBuffSprite(ParameterInGame parameterInGame)
    {
        if(parameterInGame.MonsterInGame.Unit.GetTeam() != CustomGameManager.Instance.PlayerSide)
        {
            if (parameterInGame.CheckBuffCount(BuffsEnum.Invisibility) > 0)
            {
                buff.SetActive(false);
                debuff.SetActive(false);
                mentalBuff.SetActive(false);
                mentalDebuff.SetActive(false);

                ILevelCell onCell = parameterInGame.MonsterInGame.Unit.GetCell();

                onCell.SetMaterial(onCell.GetCellState());
                onCell.SetCellState(onCell.GetCellState());
                return;
            }
        }

        int buffCount = 0;

        int debuffCount = 0;

        int mentalBuffCount = 0;

        int mentalDebuffCount = 0;

        for (int i = 0; i < parameterInGame.CurrentBuffs.Count; i++)
        {
            SerializeIngameBuff buff = parameterInGame.CurrentBuffs[i];
            if (BattleAlimentDataManager.Instance.BuffEffectDetailDict.TryGetValue(buff.BuffType, out BuffEffectsScriptable effectDetail))
            {
                if (effectDetail.IsMentalBuff)
                    mentalBuffCount++;
                else
                    buffCount++;
            }
            else
            {
                Debug.LogError($"Does not exist {buff.BuffType} in aliment manager");
            }
        }

        for (int i = 0; i < parameterInGame.CurrentDebuffs.Count; i++)
        {
            SerializeIngameDebuff buff = parameterInGame.CurrentDebuffs[i];
            if (BattleAlimentDataManager.Instance.DebuffEffectDetailDict.TryGetValue(buff.DebuffType, out DebuffEffectsScriptable effectDetail))
            {
                if (effectDetail.IsMentalDebuff)
                    mentalDebuffCount++;
                else
                    debuffCount++;
            }
            else
            {
                Debug.LogError($"Does not exist {buff.DebuffType} in aliment manager");
            }
        }

        if(buffCount > 0 && buff.activeSelf == false)
        {
            buff.SetActive(true);
        }
        else if (buffCount <= 0 && buff.activeSelf)
        {
            buff.SetActive(false);
        }

        if (debuffCount > 0 && debuff.activeSelf == false)
        {
            debuff.SetActive(true);
        }
        else if (debuffCount <= 0 && debuff.activeSelf)
        {
            debuff.SetActive(false);
        }

        if (mentalBuffCount > 0 && mentalBuff.activeSelf == false)
        {
            mentalBuff.SetActive(true);
        }
        else if (mentalBuffCount <= 0 && mentalBuff.activeSelf)
        {
            mentalBuff.SetActive(false);
        }

        if (mentalDebuffCount > 0 && mentalDebuff.activeSelf == false)
        {
            mentalDebuff.SetActive(true);
        }
        else if (mentalDebuffCount <= 0 && mentalDebuff.activeSelf)
        {
            mentalDebuff.SetActive(false);
        }
    }
}
