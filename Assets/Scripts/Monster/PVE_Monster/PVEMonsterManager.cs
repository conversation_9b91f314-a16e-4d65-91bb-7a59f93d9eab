using Assets.Scripts.Managers;
using Assets.Scripts.Scriptables;
using System.Collections.Generic;
using UnityEngine;

public class PVEMonsterManager : MonoBehaviour
{
    public static PVEMonsterManager Instance;

    [SerializeField]
    List<PVEPlayerScriptableObject> rankF = new();

    [SerializeField]
    List<PVEPlayerScriptableObject> rankE = new();

    [SerializeField]
    List<PVEPlayerScriptableObject> rankD = new();

    [SerializeField]
    List<PVEPlayerScriptableObject> rankC = new();

    [SerializeField]
    List<PVEPlayerScriptableObject> rankB = new();

    [SerializeField]
    List<PVEPlayerScriptableObject> rankA = new();

    [SerializeField]
    List<PVEPlayerScriptableObject> rankS = new();

    [SerializeField]
    List<PVEPlayerScriptableObject> rankSp = new();

    [SerializeField]
    List<Sprite> npcImage = new();
    
    private void Awake()
    {
        Instance = this;
    }

    public PVEPlayerScriptableObject GetRandomPVEPlayerByRank(RankTypeEnums rank)
    {
        switch (rank)
        {
            case RankTypeEnums.f:
                {
                    return GetRandomPVEPlayer(rankF);
                }

            case RankTypeEnums.e:
                {
                    return GetRandomPVEPlayer(rankE);
                }

            case RankTypeEnums.d:
                {
                    return GetRandomPVEPlayer(rankD);
                }

            case RankTypeEnums.c:
                {
                    return GetRandomPVEPlayer(rankC);
                }

            case RankTypeEnums.b:
                {
                    return GetRandomPVEPlayer(rankB);
                }

            case RankTypeEnums.a:
                {
                    return GetRandomPVEPlayer(rankA);
                }

            case RankTypeEnums.s:
                {
                    return GetRandomPVEPlayer(rankS);
                }

            case RankTypeEnums.sp:
                {
                    return GetRandomPVEPlayer(rankSp);
                }
        }

        return null;
    }

    public List<PVEMonsterScriptableObject> GetPVEMonsterListByRank(RankTypeEnums rank)
    {
        switch (rank)
        {
            case RankTypeEnums.f:
                {
                    return GetMonsterListByRankPlayer(rankF);
                }

            case RankTypeEnums.e:
                {
                    return GetMonsterListByRankPlayer(rankE);
                }

            case RankTypeEnums.d:
                {
                    return GetMonsterListByRankPlayer(rankD);
                }

            case RankTypeEnums.c:
                {
                    return GetMonsterListByRankPlayer(rankC);
                }

            case RankTypeEnums.b:
                {
                    return GetMonsterListByRankPlayer(rankB);
                }

            case RankTypeEnums.a:
                {
                    return GetMonsterListByRankPlayer(rankA);
                }

            case RankTypeEnums.s:
                {
                    return GetMonsterListByRankPlayer(rankS);
                }

            case RankTypeEnums.sp:
                {
                    return GetMonsterListByRankPlayer(rankSp);
                }
        }

        return null;
    }

    List<PVEMonsterScriptableObject> GetMonsterListByRankPlayer(List<PVEPlayerScriptableObject> playerList)
    {
        List<PVEMonsterScriptableObject> tempList = new();

        foreach (PVEPlayerScriptableObject player in playerList)
        {
            foreach (PVEMonsterScriptableObject pveMonster in player.PVEMonsters)
            {
                if (CheckMonsterDataCanUse(pveMonster.SerializeMonster.MonsterScriptableData))
                {
                    tempList.Add(pveMonster);
                }
            }
        }

        return tempList;
    }

    PVEPlayerScriptableObject GetRandomPVEPlayer(List<PVEPlayerScriptableObject> playerList)
    {
        if(playerList.Count > 0)
        {
            int ranNum = Random.Range(0, playerList.Count);

            return playerList[ranNum];
        }

        return null;
    }

    public PVEMonsterScriptableObject GetRandomPVEMonsterByRank(RankTypeEnums rank)
    {
        List<PVEMonsterScriptableObject> tempList = GetPVEMonsterListByRank(rank);
        int ranNum = Random.Range(0, tempList.Count);
        return tempList[ranNum];
    }

    public bool CheckMonsterDataCanUse(MonsterScriptable monsterData)
    {
        if (MonsterDataManager.Instance.MonsterDictData.ContainsKey(monsterData.MonsterScriptableId))
        {
            return true;
        }

        Debug.Log($"monsterScriptable not exist monster id = {monsterData.MonsterScriptableId}");

        return false;
    }
}
