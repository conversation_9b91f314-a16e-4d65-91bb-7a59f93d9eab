using Assets.Scripts.Scriptables;
using System.Collections.Generic;
using Network;
using UnityEngine;

namespace Assets.Scripts.Monster.Skill.CustomSkillAbility
{
    [CreateAssetMenu(fileName = "SkillStealSkillAbilityParam", menuName = "TurnBasedTools/Custom Ability/Parameters/ Create SkillStealSkillAbilityParam", order = 1)]
    public class SkillStealSkillAbilityParam : AbilityParam
    {
        [System.Serializable]
        public class StealSkillData
        {
            [SerializeField]
            GridUnit stealMonster;
            [SerializeField]
            SkillDataScriptable stealSkillDataScriptable;
            [SerializeField]
            int skillLevel = 1;
            [SerializeField]
            SkillDataScriptable replaceSkillDataScriptable;

            public StealSkillData(GridUnit unit, SkillDataScriptable stealSkill, int skillLvl, SkillDataScriptable replaceSkill)
            {
                stealMonster = unit;

                stealSkillDataScriptable = stealSkill;

                skillLevel = skillLvl;

                replaceSkillDataScriptable = replaceSkill;
            }

            public GridUnit StealMonster => stealMonster;
            public SkillDataScriptable StealSkillDataScriptable => stealSkillDataScriptable;
            public int SkillLevel => skillLevel;
            public SkillDataScriptable ReplaceSkillDataScriptable => replaceSkillDataScriptable;
        }

        [SerializeField] private List<StealAnimationSetting> skillAnimationSettings;
        public List<StealAnimationSetting> SkillAnimationSettings => skillAnimationSettings;

        public override void ApplyTo(GridUnit caster, GridUnit target, ILevelCell selectedCell, AbilityParamInfo abilityParamInfo)
        {
            Debug.Log("Steal ability");

            if(abilityParamInfo.isHit)
            {
                SkillDataScriptable skill = null;

                string stealSkillID = "";

                foreach (SkillDataScriptable skillScriptable in target.GameMonster.MonsterInfo.MonsterScriptableData.SkillRegister)
                {
                    if (SkillAnimationSettings.Exists(x=>x.SkillId == skillScriptable.SkillId))
                    {
                        stealSkillID = skillScriptable.SkillId;

                        skill = target.GameMonster.MonsterInfo.MonsterScriptableData.SkillDataDict[stealSkillID];

                        break;
                    }
                }

                if(skill != null)
                {
                    StealSkillData stealSkillData = new StealSkillData(target, skill, abilityParamInfo.skillLevel, abilityParamInfo.skillScriptable);

                    caster.GameMonster.InGameParameter.AddStealSkill(stealSkillData);
                }
            }
        }

        [System.Serializable]
        public partial class StealAnimationSetting
        {
            [SerializeField] private string skillId;
            [SerializeField] private AnimationClip animation;
            [SerializeField] private bool hideOriginalMonster;

            public StealAnimationSetting(string skillId, AnimationClip animation, bool hideOriginalMonster)
            {
                this.skillId = skillId;
                this.animation = animation;
                this.hideOriginalMonster = hideOriginalMonster;
            }

            public string SkillId => skillId;
            public AnimationClip Animation => animation;
            public bool HideOriginalMonster => hideOriginalMonster;
        }
    }
}
