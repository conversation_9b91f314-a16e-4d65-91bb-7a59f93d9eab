using Assets.Scripts.Helper;
using UnityEngine;

namespace Assets.Scripts.DataStruct.Data
{
    [System.Serializable]
    public class AssistantDialogStruct
    {   
        [SerializeField] private string m_dialogKey;
        [TagSelector("suprise", "smile", "shy", "sad", "normal", "confusion")]
        [SerializeField] private string m_emotionKey;
        [SerializeField] private bool m_isBlockingMenuInteraction = true;
        [SerializeField] private bool m_isConfirmationDialog;
        [SerializeField] private bool m_isdialogPause = false;
        [SerializeField] private bool m_isRequireMonsterName = false;
        [SerializeField] private int m_focusIndex = -1;
        [SerializeField] private int m_waitForMenuOpen = -1;
        [SerializeField] private int m_waitForMenuClose = -1;
        [SerializeField] private int m_waitForButtonClick = -1;
        [SerializeField] private int m_requestMenuOpen = -1;
        [SerializeField] private float m_dialogDelay;

        public string DialogKey => m_dialogKey;
        public string EmotionKey => m_emotionKey;
        public bool IsConfirmationDialog => m_isConfirmationDialog;
        public bool IsBlockMenuInteraction => m_isBlockingMenuInteraction;
        public bool IsDialogPause => m_isdialogPause;
        public bool IsRequireMonsterName => m_isRequireMonsterName;
        public int FocusIndex => m_focusIndex;
        public float DialogDelay => m_dialogDelay;
        public int MenuOpen => m_waitForMenuOpen;
        public int MenuClose => m_waitForMenuClose;
        public int WaitForButtonClick => m_waitForButtonClick;
        public int RequestMenuOpen => m_requestMenuOpen;
    }
}
