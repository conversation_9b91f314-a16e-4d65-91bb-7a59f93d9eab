using UnityEngine;
using Assets.Scripts.Enums;

namespace Assets.Scripts.SerializeDataStruct
{
    [System.Serializable]
    public class SerializeBuffValue
    {
        [SerializeField] private BuffsEnum buffType;
        [SerializeField] private float value;

        public BuffsEnum BuffType => buffType;

        public float Value { get => value; set => this.value = value; }

        public SerializeBuffValue() { }

        public SerializeBuffValue(SerializeBuffValue serializeBuffValue)
        {
            buffType = serializeBuffValue.buffType;
            value = serializeBuffValue.value;
        }
    }
}