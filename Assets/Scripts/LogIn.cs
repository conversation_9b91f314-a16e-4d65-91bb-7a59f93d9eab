using System;
using System.Numerics;
using Assets.Scripts.Helper;
using Assets.Scripts.Managers;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using Nethereum.JsonRpc.Client;
using Nethereum.Web3;
using Thirdweb;
using Thirdweb.Examples;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class LogIn : MonoBehaviour
{
    [SerializeField] private Button SkipLoginButton;
    [SerializeField] private Button SignMsgBtn;
    [SerializeField] private Button TestMonster;
    [SerializeField] private Button TestDucAnh;
    [SerializeField] private Button HuyTest;
    [SerializeField] private bool CanLogin;
    [SerializeField] private TMP_Dropdown dropdown;

    [SerializeField] private GameObject canvas;
    [SerializeField] private GameObject[] backgroundObject;

    [SerializeField] private Prefab_ConnectWallet connectWallet;
    [SerializeField] private SOAllowTest testAddress;
    
    private void Start()
    {
        TestDucAnh.onClick.RemoveAllListeners();
        TestDucAnh.onClick.AddListener(TestNickDucAnh);
        
        HuyTest.onClick.RemoveAllListeners();
        HuyTest.onClick.AddListener(TestNickHuy);
        
        SkipLoginButton.onClick.RemoveAllListeners();
        SkipLoginButton.onClick.AddListener(SkipLogin);
        
        dropdown.onValueChanged.AddListener(SelectTestAddress);

        SignMsgBtn.onClick.RemoveAllListeners();
        SignMsgBtn.onClick.AddListener(() => Sign().Forget());
        
        connectWallet.onConnected.AddListener(address => Sign(address).Forget());
        connectWallet.onConnectionError.AddListener(ConnectionError);
        
        if (BackendLoadData.Instance.ServerInfo.BuildType == ServerInfoSO.BuildTypeEnum.PRODUCTION || BackendLoadData.Instance.ServerInfo.BuildType == ServerInfoSO.BuildTypeEnum.STAGING)
        {
            ThirdwebManager.Instance.activeChain = "MCH Verse Mainnet";
            Debug.unityLogger.logEnabled = false;
        }
        else
        {
            ThirdwebManager.Instance.activeChain = "SAND Verse";
            Debug.unityLogger.logEnabled = true;
        }
        
#if UNITY_EDITOR
        TestDucAnh.gameObject.SetActive(true);
        SkipLoginButton.gameObject.SetActive(true);
        HuyTest.gameObject.SetActive(true);
#else
        TestDucAnh.gameObject.SetActive(false);
        HuyTest.gameObject.SetActive(false);
        SkipLoginButton.gameObject.SetActive(false);
#endif
    }

    private void SelectTestAddress(int arg0)
    {
        TurnOffBackground();
        switch (arg0)
        {
            case 0:
                BackendLoadData.Instance.ApiLogInWithWallet(
                    "0x0da1e1e8953633153e8cae20a2bc9fe2f5ae8e96d0c7aba6ec0db4ecc5a6926b4e39cadbf730c1eb00fbb166e4b89757e112dff691903a3c3249b4482fd3f4c01c", "******************************************", true, LoadScene1);
                break;
            case 1:
                BackendLoadData.Instance.ApiLogInWithWallet(
                    "0xdcf45e37f9f29958d6442e3508ad2ffe4bfd52b6298801cc0846673db94f59147a1e04f71c93642c126d3f08dc357941e152577c8fc183f64b011bf3c9acc8631c", "******************************************", true, LoadScene1);
                break;
            case 2:
                BackendLoadData.Instance.ApiLogInWithWallet(
                    "0x7d896e038fe87e6e7abc7d95f8b7e9a5907a449d4bc89ca2ca571a563d9285ff36d212de6f30a1d2fa66fd01b0e8f37cab9cdfcece95c19f35d03e27599f84641b", "******************************************", true, LoadScene1);
                break;
            case 3:
                BackendLoadData.Instance.ApiLogInWithWallet(
                    "0xb9567f88cc835ca792d33e3787555c6040f847b64e09d8ccf156b18afa43c4242b707f842a2d6d858717b3c486b118d6f91076016d3b5c3214764e88753eee601b", "******************************************", true, LoadScene1);
                break;
            case 4:
                BackendLoadData.Instance.ApiLogInWithWallet(
                    "0x3bee27932d4cc0a068e928ac029d0a0af49235fcb5b024ca9a36c4c6171e79e93dc1ee2ba2cc30fda19dcc048ae55db1d2b6feda883587a6cdfbb99d70aee3bd1c", "******************************************", true, LoadScene1);
                break;
            case 5:
                BackendLoadData.Instance.ApiLogInWithWallet(
                    "0x31e0c83740e7091035a17a4a407b4100d5dcde5bb0899f91df168cc064ffac3a119bae999c664559f1f50084eb00551c4bfab17ff2fdd36739ed3c0037941c011c", "******************************************", true, LoadScene1);
                break;
        }
    }


    private void ConnectionError(Exception e)
    {
        if (e.Message.Contains(BackendLoadData.Instance.ServerInfo.chainID.ToString()))
        {
            Application.OpenURL($"{BackendLoadData.Instance.ServerInfo.MarketUrl}/add-network");
        }
    }

    private async UniTask Sign(string address = "")
    {
        if(!testAddress.UserAllowed.Contains(address) && testAddress.UserAllowed != null)
        {
            BackendLoadData.Instance.ShowErrorCanvas("Error","You cannot access this server!");
            return;
        }
        try
        {
            SignMsgBtn.interactable = false;
            await ThirdwebManager.Instance.SDK.Wallet.SwitchNetwork(BackendLoadData.Instance.ServerInfo.chainID);
            
            var signature = await ThirdwebManager.Instance.SDK.Wallet.Sign(BackendLoadData.Instance.Message);
            var currentAddress = string.IsNullOrEmpty(address)?  await ThirdwebManager.Instance.SDK.Wallet.GetAddress() : address;

            BackendLoadData.Instance.ApiLogInWithWallet(signature, currentAddress, false, () =>
            {
                TurnOffBackground();
                LoadScene1();
            });
        }
        catch (Exception e)
        {
            SignMsgBtn.interactable = true;
            BackendLoadData.Instance.ShowErrorCanvas("Error",e.Message);
        }
    }

    private void TurnOffBackground()
    {
        if (!CanLogin) return;
        //backgroundObject.SetActive(false);
        canvas.SetActive(false);
        backgroundObject[0].GetComponent<SpriteRenderer>().DOColor(new Color(0, 0, 0, 0), 1f).SetEase(Ease.OutBack);
        backgroundObject[1].GetComponent<SpriteRenderer>().DOColor(new Color(0, 0, 0, 0), 1f).SetEase(Ease.OutBack);
        backgroundObject[2].SetActive(false);
    }

    private void LoadScene1()
    {
        GameProgressManager.Instance.HasOpenedBonusLoginMenu = false;
        if (!CanLogin) return;
        SceneHelper.Instance.LoadScene(1, 0);
    }

    #region Test Account
    private void TestNickHuy()
    {
        // BackendLoadData.Instance.ApiLogInWithWallet(
        //     "0xa9f6cfa7e4fdd2ce45e68ec403a15a8644a2b3d9e53692941cd0f94e696e13d5577a3665d6d86029f5780d4eaf2032b8690c4a12c5f6b8ac5aad01ad4f7ecc501c",
        //     "******************************************",
        //     _ => LoadScene1()
        // );
        TurnOffBackground();
        Invoke(nameof(LoginTestHuy), 3f);

    }

    private void LoginTestHuy()
    {
        BackendLoadData.Instance.ApiLogInWithWallet(
            "0xb34e078256bf8291d8af9733e0900d1b066fbdbf6aff477123a6821ff3461b93574a21ea8f36e61f045eed262b8e4152a78c8f32e06ccd1e61b8c7434aac263c1b",
            "******************************************", true,
            LoadScene1
        );    
    }

    private void TestNickDucAnh()
    {
        TurnOffBackground();
        //BackendLoadData.Instance.ApiLogInWithWallet(
        //    "0xc7f2aad11f5129b369da9eea6f36d5ad850fdea82fd64dca3356a40b0da1b3506a417bf1fd2469ce6b42769b7334837c5d096cefc2ebbd8d6b541df25a335e6e1b",
        //    "******************************************", true,
        //     LoadScene1
        //);
        BackendLoadData.Instance.ApiLogInWithWallet(
            "0xc9042cf3a5d36e3e647b198afebacd10166079eea61fbaf3e02185bfc3208e742fed49ec54268e037b929a1b972c25f1f16fa07f83e639f9b05562c0ef10c0601b",
            "******************************************", true,
            LoadScene1
        );
    }
    private void SkipLogin()
    {
        TurnOffBackground();
        BackendLoadData.Instance.ApiLogInWithWallet(
            "0x59a04dc31259833847e184c09b161601f43881bbb5faef062a3325defbcee21546ddba57786a0369c5ba7ba5ebc0a3ed7127159cc4abe77bcbb9542944cbf4521c",
            "******************************************", true,
            LoadScene1
        );
    }
    #endregion
    private async UniTask AddNetworkManual()
    {
        string chainId;
        string chainName;
        var nativeCurrency = new ThirdwebNativeCurrency
        {
            name = null,
            symbol = null,
            decimals = 18
        };
        string rpcUrls;

        switch (BackendLoadData.Instance.ServerInfo.BuildType)
        {
            case ServerInfoSO.BuildTypeEnum.PRODUCTION:
            case ServerInfoSO.BuildTypeEnum.STAGING:
                chainId = ThirdwebManager.Instance.supportedChains[1].chainId;
                chainName =ThirdwebManager.Instance.supportedChains[1].identifier;
                nativeCurrency.name = "OAS";
                nativeCurrency.symbol = "OAS";
                rpcUrls = ThirdwebManager.Instance.supportedChains[1].rpcOverride;
                break;
            case ServerInfoSO.BuildTypeEnum.TEST:
                chainId = ThirdwebManager.Instance.supportedChains[0].chainId;
                chainName =ThirdwebManager.Instance.supportedChains[0].identifier;
                nativeCurrency.name = "OAS";
                nativeCurrency.symbol = "OAS";
                rpcUrls = ThirdwebManager.Instance.supportedChains[0].rpcOverride;
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
        
        var newChainData = new ThirdwebChainData
        {
            chainId = chainId,
            blockExplorerUrls = new string[]
            {
            },
            chainName = chainName,
            iconUrls = new string[]
            {
            },
            nativeCurrency = nativeCurrency,
            rpcUrls = new[]
            {
                rpcUrls
            }
        };
        var nonce = await ThirdwebManager.Instance.SDK.Wallet.GetNonce();
        var method = "wallet_addEthereumChain";
        var parameters = new object[] { newChainData };
        var request = new RpcRequest(nonce, method, parameters);
        await ThirdwebManager.Instance.SDK.Session.Web3.Client.SendRequestAsync<object>(request);
    }
}