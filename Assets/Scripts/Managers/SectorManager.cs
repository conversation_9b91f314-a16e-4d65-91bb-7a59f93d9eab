using Assets.Scripts.Controllers;
using Assets.Scripts.DataStruct.Data.Sector;
using Assets.Scripts.Models;
using Cysharp.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Assets.Scripts.Managers
{
    public class SectorManager : PersistentSingleton<SectorManager>
    {
        [SerializeField] private List<SerializeSectorData> m_sectorList = new();
        [SerializeField] private SerializeSectorData m_currentSector = new();

        public List<SerializeSectorData> SectorList => m_sectorList;
        public SerializeSectorData CurrentSector { get => m_currentSector; set => m_currentSector = value; }

        public void InitSector()
        {
            var previousCycle = CycleController.GetPreviousCycle();
            if (CycleManager.Instance.CurrentCycle.CycleStartTime == default)
            {
                Debug.LogError("No date exist");
                return;
            }
            SectorModel.GetSectorEvent?.Invoke(previousCycle.CycleStartTime, CycleManager.Instance.CurrentCycle.CycleEndTime);
        }

        public async void AssignSector()
        {
            if (CycleManager.Instance.CurrentCycle == null || CycleManager.Instance.CurrentCycle.CycleId < 0)
                return;
            if (SectorList.Count != 0)
            {
                SectorController.AssignSectorEvent?.Invoke(GameDataManager.Instance.LoadedPlayerData.PlayerWallet, CycleManager.Instance.CurrentCycle.CycleId);
            }
            else
            {
                while (string.IsNullOrEmpty(CycleManager.Instance.CurrentCycle.CycleName))
                {
                    await UniTask.WaitForSeconds(5);
                }
                var previousCycle = CycleController.GetPreviousCycle();
                SectorModel.GetSectorEvent?.Invoke(CycleManager.Instance.CurrentCycle.CycleStartTime, CycleManager.Instance.CurrentCycle.CycleEndTime);
            }
        }

        private void Start()
        {
            if (Instance == this)
            {
                if (!SectorController.IsInit)
                    SectorController.InitController();
                if (!SectorModel.IsInit)
                    SectorModel.InitModel();
            }
        }

        public SerializeSectorData GetSectorDataByWallet(string wallet)
        {
            return SectorList.FirstOrDefault(sector => sector.CycleId == CycleManager.Instance.CurrentCycle.CycleId && sector.SectorUsersMember.Contains(wallet));
        }

        internal void ClearCurrentSector()
        {
            CurrentSector = null;
        }
    }
}
