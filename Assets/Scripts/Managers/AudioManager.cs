using System;
using UnityEngine;
using UnityEngine.Audio;

namespace Assets.Scripts.Managers
{
    public class AudioManager : PersistentSingleton<AudioManager>
    {
        #region Declare values
        #region Private values
        [SerializeField] private Audio[] sounds;
        [SerializeField] private AudioMixerGroup bgmMixer;
        [SerializeField] private AudioMixerGroup sfxMixer;
        [SerializeField] private AudioMixerGroup masterMixer;

        private static float sfxVolume;
        private static float bgmVolume;
        private static float masterVolume;
        private const string BGM_KEY = "BGM";
        private const string SFX_KEY = "SFX";
        private const string MASTER_KEY = "Master";
        #endregion

        #region Public values
        public static float BGMVolume => bgmVolume;
        public static float SFXVolume => sfxVolume;
        public static float MasterVolume => masterVolume;

        public AudioMixerGroup BgmMixer => bgmMixer;
        public AudioMixerGroup SfxMixer => sfxMixer;
        public AudioMixerGroup MasterMixer => masterMixer;
        #endregion
        #endregion

        #region Unity Message
        private void Start()
        {
            InitSounds();
            LoadSettings();
        }
        #endregion


        #region Private functions
        private void InitSounds()
        {
            foreach (var s in sounds)
            {
                s.audioSource = gameObject.AddComponent<AudioSource>();
                s.audioSource.clip = s.audioClip;
                s.audioSource.loop = s.isLoop;
                s.audioSource.playOnAwake = s.playOnAwake;
                s.audioSource.volume = s.volume;
                switch (s.audioType)
                {
                    case Enums.AudioType.SFX:
                        s.audioSource.outputAudioMixerGroup = sfxMixer;
                        break;
                    case Enums.AudioType.BGM:
                        s.audioSource.outputAudioMixerGroup = bgmMixer;
                        break;
                }
                if (s.playOnAwake)
                {
                    s.audioSource.Play();
                }
            }
        }

        private void LoadSettings()
        {
            if (PlayerPrefs.HasKey(BGM_KEY))
                bgmVolume = PlayerPrefs.GetFloat(BGM_KEY);
            else
                bgmVolume = -15f;
            UpdateBGMVolume(bgmVolume);
            if (PlayerPrefs.HasKey(SFX_KEY))
                sfxVolume = PlayerPrefs.GetFloat(SFX_KEY);
            else
                sfxVolume = -15f;
            UpdateSFXVolume(sfxVolume);
            if (PlayerPrefs.HasKey(MASTER_KEY))
                masterVolume = PlayerPrefs.GetFloat(MASTER_KEY);
            UpdateMasterVolume(masterVolume);
        }
        #endregion

        #region Public functions
        public void PlayClipByName(string clipname)
        {
            Audio soundToPlay = Array.Find(sounds, x => x.clipName == clipname);
            if (soundToPlay == null)
                return;
            switch (soundToPlay.audioType)
            {
                case Enums.AudioType.SFX:
                    if (!soundToPlay.audioSource.isPlaying)
                        soundToPlay.audioSource.Play();
                    break;
                case Enums.AudioType.BGM:
                    if (!soundToPlay.audioSource.isPlaying)
                        soundToPlay.audioSource.Play();
                    break;
            }
        }

        public void PlayRandomPitchName(string clipname, float randomPercent)
        {
            Audio soundToPlay = Array.Find(sounds, x => x.clipName == clipname);
            if (soundToPlay == null) return;
            switch (soundToPlay.audioType)
            {
                case Enums.AudioType.SFX:
                    soundToPlay.audioSource.pitch *= 1 + UnityEngine.Random.Range(-randomPercent / 100, randomPercent / 100);
                    soundToPlay.audioSource.Play();
                    break;
                case Enums.AudioType.BGM:
                    break;
            }
        }

        public void StopClipByName(string clipname)
        {
            Audio soundToPlay = Array.Find(sounds, x => x.clipName == clipname);
            soundToPlay?.audioSource.Stop();
        }

        public void PauseClipByName(string clipname)
        {
            Audio soundToPlay = Array.Find(sounds, x => x.clipName == clipname);
            soundToPlay?.audioSource.Pause();
        }

        public void ResumeClipByName(string clipname)
        {
            Audio soundToPlay = Array.Find(sounds, x => x.clipName == clipname);
            soundToPlay?.audioSource.Pause();
        }

        public void StopAllClip()
        {
            if (sounds.Length == 0) return;
            foreach (var s in sounds)
            {
                s.audioSource.Stop();
            }
        }

        public void UpdateMasterVolume(float value)
        {
            masterVolume = value <= -29 ? -100 : value;
            masterMixer.audioMixer.SetFloat("Master", masterVolume);
            PlayerPrefs.SetFloat(MASTER_KEY, value);
            PlayerPrefs.Save();
        }

        public void UpdateBGMVolume(float value)
        {
            bgmVolume = value<= -29 ? -100 : value;
            bgmMixer.audioMixer.SetFloat("BGM", bgmVolume);
            PlayerPrefs.SetFloat(BGM_KEY, value);
            PlayerPrefs.Save();
        }

        public void UpdateSFXVolume(float value)
        {
            sfxVolume = value<= -29 ? -100 : value;
            sfxMixer.audioMixer.SetFloat("SFX", sfxVolume);
            PlayerPrefs.SetFloat(SFX_KEY, value);
            PlayerPrefs.Save();
        }
        #endregion
    }

    [Serializable]
    public class Audio
    {
        public Enums.AudioType audioType;

        [HideInInspector]
        public AudioSource audioSource;
        public AudioClip audioClip;
        public string clipName;

        public bool isLoop;
        public bool playOnAwake;
        [Range(0, 1)]
        public float volume = 0.5f;
    }
}