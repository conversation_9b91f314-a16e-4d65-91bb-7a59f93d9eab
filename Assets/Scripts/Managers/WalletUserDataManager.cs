using Assets.Scripts.Controllers;
using Assets.Scripts.DataStruct;
using Assets.Scripts.DataStruct.Data;
using Assets.Scripts.Models;
using Cysharp.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

namespace Assets.Scripts.Managers
{
    public class WalletUserDataManager : PersistentSingleton<WalletUserDataManager>
    {
        private List<ServerUserData> cachedUsersDataList = new();
        public List<ServerUserData> CachedUsersDataList { get => cachedUsersDataList; set => cachedUsersDataList = value; }

        private void Start()
        {
            if (!WalletUserDataModel.IsInit)
                WalletUserDataModel.InitModel();
        }

        public void SetToCached(List<SerializeUserData> list_user_data)
        {
            WalletUserDataController.MergeDataList(list_user_data, cachedUsersDataList);
        }

        public async UniTask<ServerUserData> GetUser(string wallet)
        {
            if (cachedUsersDataList.Exists(x => x.AddressWallet == wallet) && wallet != GameDataManager.Instance.LoadedPlayerData.PlayerWallet)
            {
                return cachedUsersDataList.FirstOrDefault(x => x.AddressWallet == wallet);
            }

            if(wallet == GameDataManager.Instance.LoadedPlayerData.PlayerWallet)
            {
                return null;
            }
            // in case wallet is not cached
            WalletUserDataModel.FetchUserDataEvent?.Invoke(new() { wallet });
            while (WalletUserDataModel.FetchingUsersData)
            {
                await UniTask.WaitForSeconds(3);
            }
            await UniTask.WaitForSeconds(1);
            return cachedUsersDataList.FirstOrDefault(x => x.AddressWallet == wallet);
        }

        public async UniTask<List<ServerUserData>> GetUsers(List<string> wallets)
        {
            var nonExistWallets = wallets
                .Except(cachedUsersDataList
                    .Select(user => user.AddressWallet))
                .Where(user=> user != GameDataManager.Instance.LoadedPlayerData.PlayerWallet)
                .ToList();
            WalletUserDataModel.FetchUserDataEvent?.Invoke(nonExistWallets);
            while (WalletUserDataModel.FetchingUsersData)
            {
                await UniTask.WaitForSeconds(5);
            }
            return cachedUsersDataList.Where(x => wallets.Exists(y => y == x.AddressWallet)).ToList();
        }
    }
}
