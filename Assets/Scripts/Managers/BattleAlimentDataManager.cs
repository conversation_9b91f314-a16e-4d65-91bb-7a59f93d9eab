using Assets.Scripts.Enums;
using Assets.Scripts.Scriptables;
using System.Collections.Generic;
using UnityEngine;

namespace Assets.Scripts.Managers
{
    public class BattleAlimentDataManager : PersistentSingleton<BattleAlimentDataManager>
    {
        #region Private variables
        [SerializeField] private List<BuffEffectsScriptable> buffEffectsDetail = new();
        [SerializeField] private List<DebuffEffectsScriptable> debuffsEffectDetail = new();
        private Dictionary<BuffsEnum, BuffEffectsScriptable> buffEffectDetailDict = new();
        private Dictionary<DebuffsEnum, DebuffEffectsScriptable> debuffEffectDetailDict = new();
        #endregion

        #region Public variables
        public Dictionary<BuffsEnum, BuffEffectsScriptable> BuffEffectDetailDict
        {
            get
            {
                if (buffEffectDetailDict == null || buffEffectDetailDict.Count == 0)
                {
                    foreach (var buff in buffEffectsDetail)
                    {
                        if (!buffEffectDetailDict.TryAdd(buff.BuffType, buff))
                        {
                            Debug.Log($"Can't add {buff.BuffName} detail");
                        }
                    }
                }
                return buffEffectDetailDict;
            }
        }

        public Dictionary<DebuffsEnum, DebuffEffectsScriptable> DebuffEffectDetailDict
        {
            get
            {
                if (debuffEffectDetailDict == null || debuffEffectDetailDict.Count == 0)
                {
                    foreach (var debuff in debuffsEffectDetail)
                    {
                        if (!debuffEffectDetailDict.TryAdd(debuff.DebuffType, debuff))
                        {
                            Debug.Log($"Can't add {debuff.DebuffName} detail");
                        }
                    }
                }
                return debuffEffectDetailDict;
            }
        }
        #endregion

        public static string SkillDescriptionConvert(string summaryString, float value)
        {
            string replaceString = summaryString.Replace("{skill}", Mathf.RoundToInt(value).ToString());
            return replaceString;
        }
    }
}