using System;
using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.Enums;
using Assets.Scripts.Scriptables.Item;
using UnityEngine;

namespace Assets.Scripts.Managers
{
    public class ItemDataManager : PersistentSingleton<ItemDataManager>
    {
        [SerializeField] private List<ScriptableTrainingItem> trainingItemsList = new();
        [SerializeField] private List<ScriptableEnhanceItem> enhanceItemsList = new();
        [SerializeField] private List<ScriptableFushionItem> fushionItemsList = new();
        [SerializeField] private List<ScriptableRegenerationItem> regenerationItemsList = new();
        [SerializeField] private List<ScriptableTrophyItem> trophyItemsList = new();
        [SerializeField] private List<CoachSetting> coachSettingList = new();
        [SerializeField] private List<Sprite> itemTypeSpriteList = new();
        [SerializeField] private List<Sprite> backgroundNft;
        [SerializeField] private List<CrystalSetup> crystalSetup = new();
        [SerializeField] private List<Sprite> genesisBoxSprite;
        [SerializeField] private List<Sprite> generalBoxSprite;
        [SerializeField] private Sprite hashChipSprite;
        [SerializeField] private Sprite trainerSprite;
        [SerializeField] private Sprite[] pocSprite;
        [SerializeField] private Sprite[] trophiesSprite;

        public List<Sprite> GenesisBoxSprite => genesisBoxSprite;
        public List<Sprite> GeneralBoxSprite => generalBoxSprite;
        public Sprite HashChipSprite => hashChipSprite;
        public Sprite TrainerSprite => trainerSprite;
        public Sprite[] PocSprite => pocSprite;
        public Sprite[] TrophiesSprite => trophiesSprite;
        
        private Dictionary<string, ScriptableItem> itemDict = new();
        private Dictionary<int, ScriptableItem> trophiesDict = new();
        private Dictionary<ItemsRarityEnum, Sprite> itemsRarityEnumDict = new();
        private Dictionary<BasicBattleParameterEnum, Sprite> coachSettingDict;

        public Dictionary<ItemsRarityEnum, Sprite> ItemsRarityEnumDict
        {
            get
            {
                itemsRarityEnumDict.Clear();
                itemsRarityEnumDict.Add(ItemsRarityEnum.Shop, itemTypeSpriteList[0]);
                itemsRarityEnumDict.Add(ItemsRarityEnum.B, itemTypeSpriteList[1]);
                itemsRarityEnumDict.Add(ItemsRarityEnum.C, itemTypeSpriteList[2]);
                itemsRarityEnumDict.Add(ItemsRarityEnum.UC, itemTypeSpriteList[3]);
                itemsRarityEnumDict.Add(ItemsRarityEnum.R, itemTypeSpriteList[4]);
                return itemsRarityEnumDict;
            }
        }

        public Dictionary<BasicBattleParameterEnum, Sprite> CoachSettingDict
        {
            get
            {
                coachSettingDict ??= new Dictionary<BasicBattleParameterEnum, Sprite>();
                if (coachSettingDict == null || coachSettingDict.Count == 0)
                {
                    foreach (var item in coachSettingList)
                    {
                        coachSettingDict.Add(item.parameter, item.parameterBackground);
                    }
                }
                return coachSettingDict;
            }
        }

        public Dictionary<string, ScriptableItem> ItemDict
        {
            get
            {
                if (itemDict == null || itemDict.Count <= 0)
                {
                    foreach (var t in trainingItemsList.AsSpan())
                    {
                        if (!itemDict.TryAdd(t.ItemId, t))
                        {
                            Debug.LogWarning("Can't add" + t.ItemId);
                        }
                    }
                    foreach (var t in enhanceItemsList.AsSpan())
                    {
                        if (!itemDict.TryAdd(t.ItemId, t))
                        {
                            Debug.LogWarning($"Can't add {t.ItemId}");
                        }
                    }
                    foreach (var t in fushionItemsList)
                    {
                        if (!itemDict.TryAdd(t.ItemId, t))
                        {
                            Debug.LogWarning($"Can't add {t.ItemId}");
                        }
                    }
                    foreach (var t in regenerationItemsList.AsSpan())
                    {
                        if (!itemDict.TryAdd(t.ItemId, t))
                        {
                            Debug.LogWarning($"Can't add {t.ItemId}");
                        }
                    }
                }
                return itemDict;
            }
        }

        public Dictionary<int, ScriptableItem> TrophiesDict
        {
            get
            {
                if (trophiesDict is { Count: > 0 }) return trophiesDict;
                foreach (var t in trophyItemsList.AsSpan())
                {
                    if (!trophiesDict.TryAdd(t.Rank, t))
                    {
                        Debug.LogWarning("Cannot add trophies:" + t.Rank);
                    }
                }
                return trophiesDict;
            }
        }


        public List<ScriptableTrainingItem> TrainingItemsList => trainingItemsList;
        public List<ScriptableEnhanceItem> EnhanceItemsList => enhanceItemsList;
        public List<ScriptableFushionItem> FusionItemsList => fushionItemsList;
        public List<ScriptableRegenerationItem> RegenerationItemsList => regenerationItemsList;
        public List<ScriptableTrophyItem> TrophyItemsList => trophyItemsList;
        public List<Sprite> BackgroundNft => backgroundNft;

        public Sprite GetCrystal(RankTypeEnums crystalRank, Enums.BasicBattleParameterEnum crystalColor)
        {
            var setting = crystalSetup.FirstOrDefault(x => x.crystalRank.Equals(crystalRank) && x.crystalParameter.Equals(crystalColor));
            return setting?.crystalSprite;
        }

        [Serializable]
        private class CoachSetting
        {
            public BasicBattleParameterEnum parameter;
            public Sprite parameterBackground;
        }

        [System.Serializable]
        private class CrystalSetup
        {
            public RankTypeEnums crystalRank;
            public Enums.BasicBattleParameterEnum crystalParameter;
            public Sprite crystalSprite;
        }
    }
}