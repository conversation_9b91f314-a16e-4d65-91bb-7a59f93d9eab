using Assets.Scripts.Scriptables.Traits;
using System.Collections.Generic;
using UnityEngine;

namespace Assets.Scripts.Managers
{
    public class InnateTraitsDataManager : PersistentSingleton<InnateTraitsDataManager>
    {
        [SerializeField] private List<MonsterInnateTrait> traitsData = new();
        private Dictionary<string, MonsterInnateTrait> traitsDict = new();
        public List<MonsterInnateTrait> TraitsData => traitsData;
        public Dictionary<string, MonsterInnateTrait> TraitsDict
        {
            get
            {
                if (traitsDict == null || traitsDict.Count == 0)
                {
                    traitsDict = new();
                    for (int i = 0; i < traitsData.Count; i++)
                    {
                        if (!traitsDict.TryAdd(traitsData[i].TraitId, traitsData[i]))
                        {
                            Debug.LogWarning("Can't add " + traitsData[i].TraitId);
                        }
                    }
                }
                return traitsDict;
            }
        }
    }
}