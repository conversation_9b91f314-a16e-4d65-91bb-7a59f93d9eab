using Assets.Scripts.DataStruct;
using Assets.Scripts.Managers;
using Best.HTTP;
using Best.HTTP.Request.Authenticators;
using Best.HTTP.Request.Upload;
using System;
using System.Collections.Generic;

namespace Assets.Scripts.Models
{
    public class WalletUserDataModel
    {
        public static bool IsInit { get; private set; } = false;
        public static void InitModel()
        {
            if (IsInit) return;
            IsInit = true;
            FetchUserDataEvent += FetchUserData;
        }

        public class UserRequestResponse
        {
            public bool success { get; set; }
            public string message { get; set; }
            public UserListResponse data { get; set; }
            public int code { get; set; }
        }
        public class UserListResponse
        {
            public List<SerializeUserData> list_user_data;
        }

        public static bool FetchingUsersData { get; private set; } = false;
        public static Action<List<string>> FetchUserDataEvent { get; private set; }
        private static async void FetchUserData(List<string> wallets)
        {
            if (FetchingUsersData) return;
            FetchingUsersData = true;
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.ListUserDataUrl}";
            var request = HTTPRequest.CreatePost(url);
            var requestBody = new Dictionary<string, object> { { "list_wallet_address", wallets } };
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
            UserRequestResponse response = new();
            try
            {
                response = await request.GetFromJsonResultAsync<UserRequestResponse>();
                if (!response.success)
                    UnityEngine.Debug.LogError(response.message);
            }
            catch (Exception e)
            {
                UnityEngine.Debug.LogError(e);
            }
            if (response.success)
            {
                WalletUserDataManager.Instance.SetToCached(response.data.list_user_data);
            }
            FetchingUsersData = false;
        }
    }
}
