using Assets.Scripts.Controllers;
using Assets.Scripts.DataStruct;
using Assets.Scripts.DataStruct.Data;
using Assets.Scripts.DataStruct.ScoreApi;
using Assets.Scripts.Helper;
using Assets.Scripts.Managers;
using Best.HTTP;
using Best.HTTP.Request.Authenticators;
using Best.HTTP.Request.Upload;
using Cysharp.Threading.Tasks;
using NBitcoin;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Assets.Scripts.Models
{
    public class ScoreModel
    {
        private static bool isInit = false;
        public static bool IsGettingScoreFinish { get; private set; } = true;
        // is getting snapshot finish
        public static Dictionary<string, bool> SnapshotStatus { get; private set; } = new()
        {
            {ScoreController.GUILD_SNAPSHOT, true },
            {ScoreController.SECTOR_SNAPSHOT, true },
            {ScoreController.WEEKLY_SNAPSHOT, true },
            {ScoreController.PERSONAL_PVP_SNAPSHOT, true },
            {ScoreController.PERSONAL_EXPLORATION_SNAPSHOT, true }
        };

        public static void InitModel()
        {
            if (isInit) return;
            isInit = true;
            ScoreManager.RequestScores += GetScoreFromDB;
            AddUserScore += AddUserScoreToDb;
            ScoreManager.RequestSnapshots += GetScoreSnapshotFromDB;
            AddScoreSnapshot += AddScoreSnapshotToDB;
            RequestDeleteScoreSnapshot += DeleteScoreSnapshotFromDB;
            RequestDeleteScore += DeleteUserScoreDb;
            UpdateSnapshotClaim += UpdateSnapshotClaimToDB;
        }

        private static async void GetScoreFromDB(List<string> wallets, DateTime fromDate, DateTime toDate)
        {
            while (!IsGettingScoreFinish)
                await UniTask.WaitForSeconds(5);
            IsGettingScoreFinish = false;
            List<UserScoreApi> fetchData = new();
            List<(DateTime, DateTime)> weekRanges = DivideIntoWeeks(fromDate, toDate);
            var stringUrl = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.GetUserScore}";
            foreach (var value in weekRanges)
            {
                var request = HTTPRequest.CreatePost(stringUrl);
                var requestBody = new Dictionary<string, object> {
                    { "user_wallets", wallets },
                    { "from_date", ((DateTimeOffset)value.Item1).ToUnixTimeSeconds()},
                    { "to_date", ((DateTimeOffset)value.Item2).ToUnixTimeSeconds()}
                };
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
                Dictionary<string,object> response = new();
                try
                {
                    response = await request.GetFromJsonResultAsync<Dictionary<string,object>>();
                }
                catch (Exception ex)
                {
                    UnityEngine.Debug.LogException(ex);
                }
                bool success = ObjectConvertionHelper.ConvertTo<bool>(response, "success");
                if (success)
                {
                    Dictionary<string, object> data = (Dictionary<string, object>)response["data"];
                    if (data["user_score"] != null)
                    {
                        var dataArray = data["user_score"] as object[];
                        for (int i = 0; i < dataArray.Length; i++)
                        {
                            var dictData = dataArray[i] as Dictionary<string, object>;
                            float score_value = 0;
                            if (dictData["score_value"] is int)
                            {
                                int getVal = (int)dictData["score_value"];
                                score_value = getVal;
                            }
                            if (dictData["score_value"] is double)
                            {
                                double getVal = (double)dictData["score_value"];
                                score_value = (float)getVal;
                            }
                            fetchData.Add(new()
                            {
                                _id = dictData["_id"] as string,
                                user_wallet = dictData["user_wallet"] as string,
                                score_value = score_value,
                                score_type = (int)dictData["score_type"],
                                share_to_guild = (bool)dictData["share_to_guild"],
                                score_date_time = (int)dictData["score_date_time"],
                                record_version = (int)dictData["record_version"]
                            });
                        }
                    }
                }
            }
            ScoreManager.OnScoreResultReturn?.Invoke(fetchData);
            IsGettingScoreFinish = true;
        }

        public static List<(DateTime, DateTime)> DivideIntoWeeks(DateTime startDate, DateTime endDate)
        {
            List<(DateTime, DateTime)> weeks = new List<(DateTime, DateTime)>();
            DateTime currentStart = startDate;
            while (currentStart < endDate)
            {
                DateTime currentEnd = currentStart.AddDays(7).AddSeconds(-1);
                if (currentEnd > endDate)
                {
                    currentEnd = endDate;
                }
                weeks.Add((currentStart, currentEnd));
                currentStart = currentStart.AddDays(7);
            }
            return weeks;
        }

        public static bool IsAddingScore { get; private set; } = false;
        public static bool IsAddingScoreSuccess { get; private set; } = true;
        public static Action<List<SerializeUserScoreRecord>> AddUserScore { get; set; }
        private async static void AddUserScoreToDb(List<SerializeUserScoreRecord> userScores)
        {
            if (userScores == null || userScores.Count <= 0) return;
            if (IsAddingScore) return;
            IsAddingScore = true;
            var stringUrl = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.CreateUserScore}";
            var request = HTTPRequest.CreatePost(stringUrl);
            List<UserScoreApi> scoreApiList = new();
            for (int i = 0; i < userScores.Count; i++)
            {
                scoreApiList.Add(new()
                {
                    _id = userScores[i].ScoreId,
                    user_wallet = userScores[i].UserWallet,
                    score_date_time = userScores[i].Record_time.ToUnixTimestamp(),
                    score_type = userScores[i].Score_type,
                    share_to_guild = userScores[i].Share_to_guild,
                    score_value = userScores[i].Score,
                    record_version = userScores[i].RecordVersion,
                });
            }
            var requestBody = new Dictionary<string, object> { { "user_score", scoreApiList } };
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
            ServerResponse response = new();
            try
            {
                response = await request.GetFromJsonResultAsync<ServerResponse>();
                if (!response.success)
                    UnityEngine.Debug.Log("Add score failed " + response.message);
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogException(ex);
            }
            var scoreOrder = userScores.OrderBy(x => x.Record_time).ToList();
            var fromDate = scoreOrder.Select(x => x.Record_time).FirstOrDefault();
            var toDate = scoreOrder.Select(x => x.Record_time).LastOrDefault();
            while (!IsAddScoreSnapshotFinish)
                await UniTask.WaitForSeconds(5);
            var weekTime = Helpers.GetCurrentWeekRange(userScores[0].Record_time);
            var cycle = CycleManager.Instance.GameCycles.FirstOrDefault(x => x.CycleStartTime <= userScores[0].Record_time && x.CycleEndTime >= userScores[0].Record_time);
            var firstDayOfTheMonth = Helpers.GetFirstDateOfMonth(userScores[0].Record_time.Year, userScores[0].Record_time.Month);
            var lastDayOfTheMonth = Helpers.GetLastDateOfMonth(userScores[0].Record_time.Year, userScores[0].Record_time.Month);
            ScoreManager.RequestScores?.Invoke(userScores.Select(x => x.UserWallet).Distinct().ToList(), fromDate, toDate.AddDays(1));
            ScoreManager.RequestSnapshots?.Invoke(ScoreController.WEEKLY_SNAPSHOT, weekTime.Item1, weekTime.Item2, new());
            ScoreManager.RequestSnapshots?.Invoke(ScoreController.SECTOR_SNAPSHOT, cycle.CycleStartTime, cycle.CycleEndTime, new());
            ScoreManager.RequestSnapshots?.Invoke(ScoreController.PERSONAL_EXPLORATION_SNAPSHOT, cycle.CycleStartTime, cycle.CycleEndTime, new());
            ScoreManager.RequestSnapshots?.Invoke(ScoreController.PERSONAL_PVP_SNAPSHOT, cycle.CycleStartTime, cycle.CycleEndTime, new());
            ScoreManager.RequestSnapshots?.Invoke(ScoreController.GUILD_SNAPSHOT, firstDayOfTheMonth, lastDayOfTheMonth, new());
            IsAddingScoreSuccess = response.success;
            IsAddingScore = false;
        }

        public static Action<List<string>> RequestDeleteScore { get; set; }
        private async static void DeleteUserScoreDb(List<string> idList)
        {
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.DeleteUserScore}";
            var listCopy = new ListDivider<string>(idList);
            var divideList = listCopy.DivideList(50);
            for (int i = 0; i < divideList.Count; i++)
            {
                var request = HTTPRequest.CreatePost(url);
                var requestBody = new Dictionary<string, object>()
                {
                    {"_ids",divideList[i] }
                };
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
                ServerResponse response = new();
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        UnityEngine.Debug.Log("Delete score failed " + response.message);
                    UnityEngine.Debug.Log("Done request " + i);
                }
                catch (Exception ex)
                {
                    UnityEngine.Debug.LogException(ex);
                }
            }
        }

        public static async System.Threading.Tasks.Task<(int typeReward, int cycle, string account, string amount, int deadline, string signature)> SignClaimReward(string rewardType, string walletAddress, string snapshotId)
        {
            var url = $"{BackendLoadData.Instance.ServerInfo.BlockchainUrl}{BackendLoadData.Instance.ServerInfo.SignClaim}";
            var request = HTTPRequest.CreatePost(url);
            var body = new Dictionary<string, object>()
            {
                {"address", walletAddress },
                {"typeClaim", rewardType},
                {"id_snapshot", snapshotId}
            };
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(body);
            Dictionary<string, object> response = new();
            try
            {
                response = await request.GetFromJsonResultAsync<Dictionary<string, object>>();
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError(ex);
            }
            if (response.TryGetValue("data", out object data))
            {
                int typeReward;
                int cycle;
                string account;
                string amount;
                int deadline;
                string signature;
                Dictionary<string, object> dataConvert = data as Dictionary<string, object>;
                typeReward = (int)dataConvert["typeReward"];
                cycle = (int)dataConvert["cycle"];
                account = (string)dataConvert["account"];
                amount = (string)dataConvert["amount"];
                deadline = (int)dataConvert["deadline"];
                signature = (string)dataConvert["signature"];
                return (typeReward, cycle, account, amount, deadline, signature);
            }
            return new();
        }

        #region Score snapshot
        private static async void GetScoreSnapshotFromDB(string score_type, DateTime from_time, DateTime to_time, List<string> wallets)
        {
            if (!SnapshotStatus.TryGetValue(score_type, out bool isGettingDone))
            {
                return;
            }
            while (!isGettingDone)
                await UniTask.WaitForSeconds(5);
            isGettingDone = false;
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.GetScoreSnapshot}";
            var request = HTTPRequest.CreatePost(url);
            Dictionary<string, object> requestBody = new Dictionary<string, object>()
            {
                {"score_type", score_type },
                {"from_date", ((DateTimeOffset)from_time).ToUnixTimeSeconds()},
                {"to_date", ((DateTimeOffset)to_time ).ToUnixTimeSeconds()},
                {"wallets", wallets }
            };
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
            ServerScoreSnapshotReceive response = new();
            try
            {
                response = await request.GetFromJsonResultAsync<ServerScoreSnapshotReceive>();
                if (!response.success)
                {
                    UnityEngine.Debug.LogError(response.message);
                    return;
                }
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogException(ex);
                return;
            }
            ScoreManager.OnScoreSnapshotResultReturn?.Invoke(response.data.score_snapshot);
            isGettingDone = true;
        }

        public static bool IsAddScoreSnapshotFinish { get; private set; } = false;
        public static Action<List<SerializeScoreSnapshot>> AddScoreSnapshot { get; private set; }
        // Only update for user
        private static async void AddScoreSnapshotToDB(List<SerializeScoreSnapshot> scoreSnapshot)
        {
            if (scoreSnapshot.Count <= 0) return;
            IsAddScoreSnapshotFinish = false;
            var listDivide = new ListDivider<SerializeScoreSnapshot>(scoreSnapshot);
            var divideList = listDivide.DivideList(50);
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.UpdateScoreSnapshot}";
            foreach (var snapshotsData in divideList)
            {
                var request = HTTPRequest.CreatePost(url);
                List<Dictionary<string, object>> uploadValues = new List<Dictionary<string, object>>();
                foreach (var item in snapshotsData)
                {
                    long claimDate = 0;
                    if (item.ClaimDate != default)
                        claimDate = ((DateTimeOffset)item.ClaimDate.ToUniversalTime()).ToUnixTimeSeconds();
                    if (item.ScoreUser == GameDataManager.Instance.LoadedPlayerData.PlayerWallet)
                    {
                        uploadValues.Add(new()
                        {
                            {"_id",item.ScoreId},
                            {"user",item.ScoreUser },
                            {"score",item.ScoreValue },
                            {"explore_score",item.ExploreScore},
                            {"ranked_score",item.RankedScore},
                            {"score_type",item.ScoreType },
                            {"from_date",((DateTimeOffset)item.FromDate.ToUniversalTime()).ToUnixTimeSeconds()},
                            {"to_date",((DateTimeOffset)item.ToDate.ToUniversalTime()).ToUnixTimeSeconds()},
                            {"daily_quest_complete", item.DailyQuestComplete },
                            {"reward_amount",item.RewardAmount },
                            {"claim_date",claimDate },
                            {"rank",item.Ranking },
                            {"sector_rank",item.SectorRank },
                        });
                    }
                }
                var requestBody = new Dictionary<string, object>() {
                    {"score_snapshot", uploadValues}
                };
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
                bool requestSucess = false;
                try
                {
                    var response = await request.GetFromJsonResultAsync<Dictionary<string, object>>();
                    if (response.TryGetValue("success", out object success) && (bool)success)
                    {
                        requestSucess = (bool)success;
                    }
                }
                catch (Exception ex)
                {
                    UnityEngine.Debug.LogException(ex);
                }
            }
            IsAddScoreSnapshotFinish = true;
        }

        public static bool IsUpdateingSnapshotClaim { get; private set; } = false;
        public static Action<List<SerializeScoreSnapshot>> UpdateSnapshotClaim { get; private set; }
        private static async void UpdateSnapshotClaimToDB(List<SerializeScoreSnapshot> scoreSnapshot)
        {
            if (IsUpdateingSnapshotClaim) return;
            IsUpdateingSnapshotClaim = true;
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.UpdateScoreSnapshot}";
            var request = HTTPRequest.CreatePost(url);
            List<Dictionary<string, object>> uploadValues = new List<Dictionary<string, object>>();
            foreach (var item in scoreSnapshot)
            {
                long claimDate = 0;
                if (item.ClaimDate == default)
                    claimDate = 0;
                else
                    claimDate = ((DateTimeOffset)item.ClaimDate.ToUniversalTime()).ToUnixTimeSeconds();
                uploadValues.Add(new()
                {
                    {"_id",item.ScoreId},
                    {"user",item.ScoreUser },
                    {"claim_date",claimDate }
                });
            }
            var requestBody = new Dictionary<string, object>() {
                {"score_snapshot", uploadValues}
            };
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
            bool requestSucess = false;
            try
            {
                var response = await request.GetFromJsonResultAsync<Dictionary<string, object>>();
                if (response.TryGetValue("success", out object success) && (bool)success)
                {
                    requestSucess = (bool)success;
                }
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogException(ex);
            }
            IsUpdateingSnapshotClaim = false;
        }

        public static Action<string> RequestDeleteScoreSnapshot { get; private set; }
        private static async void DeleteScoreSnapshotFromDB(string snapshotId)
        {
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.DeleteScoreSnapshot}";
            var request = HTTPRequest.CreatePost(url);
            var requestBody = new Dictionary<string, object>()
            {
                {"_id",snapshotId }
            };
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
            ServerResponse response = new();
            try
            {
                response = await request.GetFromJsonResultAsync<ServerResponse>();
                if (!response.success)
                {
                    UnityEngine.Debug.LogError(response.message);
                    return;
                }
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogException(ex);
                return;
            }
            if (response.success)
            {
                UnityEngine.Debug.Log($"delete {snapshotId}");
                ScoreManager.Instance.ScoreSnapshot.RemoveAll(x => x.ScoreId == snapshotId);
            }
        }
        #endregion
    }
}
