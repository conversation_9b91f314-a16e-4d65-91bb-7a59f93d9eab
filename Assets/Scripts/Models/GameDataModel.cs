using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Assets.Scripts.Controllers;
using Assets.Scripts.DataStruct;
using Assets.Scripts.DataStruct.Data;
using Assets.Scripts.Enums;
using Assets.Scripts.Helper;
using Assets.Scripts.Managers;
using Assets.Scripts.Scriptables.Item;
using Assets.Scripts.SerializeDataStruct.ItemData;
using Assets.Scripts.UI;
using Assets.Scripts.UI.Utilities;
using Best.HTTP;
using Best.HTTP.Request.Authenticators;
using Best.HTTP.Request.Upload;
using Cysharp.Threading.Tasks;
using I2.Loc;
using Proyecto26;
using UnityEngine;
using Exception = System.Exception;

namespace Assets.Scripts.Models
{
    public abstract class GameDataModel
    {
        public static bool RequestBurnItemSuccess { get; private set; } = true;
        public static bool RequestCoachBurnSucces { get; private set; } = true;
        public static bool RequestCrystalBurnSuccess { get; private set; } = true;
        public static bool IsBurningItem { get; private set; }
        public static bool IsBurningCoach { get; private set; }
        public static bool IsBurningCrystal { get; private set; }

        public static bool IsCreatingCrystal { get; private set; }
        public static bool IsCreateCrystalSuccess { get; private set; } = true;
        public static bool IsFetchingItem { get; private set; }
        public static bool IsFetchingCoach { get; private set; }
        public static bool IsFetchingCrystal { get; private set; }


        public static bool IsCreatingCoach { get; private set; }
        public static bool IsCreateCoachSuccess { get; private set; } = true;

        public static bool IsCreatingItem { get; private set; }
        public static bool IsCreateItemSuccess { get; private set; } = true;

        public static bool IsLoadingFarm { get; private set; }

        #region Monster
        public static async Task<ServerResponse> AddMonsterToFusion(SerializeMonster childMonster)
        {
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.CreateFusionMonster}";
            var requestBody = new Dictionary<string, object>
            {
                {"fusion", new SerializePlayerMonster(
                    _id: childMonster.MonsterId, monster_nft_id: childMonster.MonsterNFTId,
                    monster_name: childMonster.MonsterName,
                    monster_script_id: childMonster.MonsterScriptableData.MonsterScriptableId,
                    monster_personality_id: childMonster.MonsterPersonality.PersonalityId,
                    likes: childMonster.LikeMeal.FoodName, dislikes: childMonster.DislikeMeal.FoodName,
                    monster_age: childMonster.MonsterAge, rank_battle_count: childMonster.RankBattleCount,
                    is_crystal_used: childMonster.IsCrystalUsed, is_life_potion_used: childMonster.IsLifePotionUsed,
                    is_item_used: childMonster.IsItemUsed, is_memory: childMonster.IsMemory,
                    is_alive: childMonster.IsMonsterAlive, monster_rank: (int)childMonster.MonsterRank,
                    alter_basic_p: childMonster.MonsterAlteredBasicP.SaveParameter,
                    alter_training_p: childMonster.MonsterAlteredTrainingP.SaveParameter,
                    innate_trait_id_list: childMonster.InnateTraits.Select(x => new SerializeInnateTrait(x.TraitId)).ToList(),
                    skill_detail: childMonster.SkillSave,
                    monster_acquired_traits: childMonster.MonsterAcquireTraitLevel.Select(x => new SerializeAcquireTraitSaving(x.TraitScriptable.TraitId, x.Level)).ToList(),
                    monster_injury: childMonster.MonsterInjuryCondition,
                    monster_disease: childMonster.MonsterDiseasesCondition, minter: childMonster.Minter,
                    mint_type: childMonster.MintType, cycle: childMonster.Cycle,
                    born_of_1_address: childMonster.BornOf1Address, born_of_1_id: childMonster.BornOf1Id,
                    born_of_1_chain: childMonster.BornOf1Chain, born_of_1_token_id: childMonster.BornOf1TokenId,
                    born_of_2_address: childMonster.BornOf2Address, born_of_2_id: childMonster.BornOf2Id,
                    born_of_2_chain: childMonster.BornOf2Chain, born_of_2_token_id: childMonster.BornOf2TokenId,
                    is_favorite: childMonster.IsFavorite, is_rank_s_offical_clear: childMonster.IsRankSOfficalClear,
                    is_monster_grand_prix_clear: childMonster.IsMonsterGrandPrixClear,
                    is_monster_crown_cup_clear: childMonster.IsMonsterCrownCupClear,
                    is_santuary_cup_clear: childMonster.IsSantuaryCupClear,
                    is_winner_cup_clear: childMonster.IsWinnerCupClear,
                    is_re_master_cup_clear: childMonster.IsReMasterCupClear, is_free_monster: childMonster.IsFree,
                    overall: childMonster.OverallValue, lifespan_fusion: childMonster.MonsterLifespanFusion,
                    main_seed: ExtendedSeedEnumData.GetBySeed(childMonster.MonsterScriptableData.MonsterMainSeed).SeedNumber,
                    sub_seed: ExtendedSeedEnumData.GetBySeed(childMonster.MonsterScriptableData.MonsterSubSeed).SeedNumber,
                    lifespan: childMonster.ComplexLifespan, crystal_rank_use: (int)childMonster.CrystalRankUse,
                    crystal_parameter_use: (int)childMonster.CrystalParameter, nft_image_url: childMonster.NftImageUrl,
                    training_count: childMonster.TrainingCount, current_cycle_name: childMonster.CurrentCycleName,
                    final_hp: childMonster.ComplexHealth, final_int: childMonster.ComplexIntelligent,
                    final_str: childMonster.ComplexStrength, final_agi: childMonster.ComplexAgility,
                    final_dex: childMonster.ComplexDexterity, final_vit: childMonster.ComplexVitality,
                    life_span_end_count: childMonster.LifeSpanEndCount,is_coached: false, is_crystalized: false)
                }
            };
            var request = HTTPRequest.CreatePost(url);
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
            request.AddHeader("Authorization", $"Bearer {BackendLoadData.Instance.token}");
            request.AddHeader("Content-Type", "application/json");
            ServerResponse response = new();
            try
            {
                response = await request.GetFromJsonResultAsync<ServerResponse>();
            }
            catch (Exception ex)
            {
                Debug.LogError(ex);
            }
            return response;
        }

        public static bool IsLoadingMonster { get; private set; }
        public static async void LoadMonsterData()
        {
            try
            {
                while (IsLoadingMonster) await UniTask.WaitForSeconds(5);
                IsLoadingMonster = true;
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.GetMonsterBalance}";
                var request = HTTPRequest.CreatePost(url);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                ServerResponse response = new();
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                    {
                        Debug.LogError(response.message);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogException(e);
                }
                if (response.success)
                {
                    GameDataController.MergeMonsters(response.data.player_monster_list);
                }
                IsLoadingMonster = false;
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        /// <summary>
        /// To sync if data is slow
        /// </summary>
        public static async void LoadMonsterMemory()
        {
            try
            {
                while (IsLoadingMonster) await UniTask.WaitForSeconds(1);

                var url = $"{BackendLoadData.Instance.ServerInfo.BlockchainUrl}{BackendLoadData.Instance.ServerInfo.MonsterMemoryBalance}?address={GameDataManager.Instance.LoadedPlayerData.PlayerWallet}";
                List<int> memoryTokens = new();
                try
                {
                    var request = HTTPRequest.CreateGet(url);
                    var response = await request.GetFromJsonResultAsync<Dictionary<string, object>>();
                    if (response["listToken"] is object[] responseList) memoryTokens.AddRange(responseList.Cast<int>());
                }
                catch (Exception ex)
                {
                    Debug.Log(ex);
                }
                GameDataController.SetMonsterMemory(memoryTokens.ToArray());
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        public static async void UpdateMonsterData(List<SerializePlayerMonster> updateMonsterData)
        {
            try
            {
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.UpdateMonster}";
                var request = HTTPRequest.CreatePost(url);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                var response = new ServerResponse();
                var requestBody = new Dictionary<string, object>
                {
                    { "player_monster_list" , updateMonsterData}
                };
                request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError(response.message);
                }
                catch (Exception ex)
                {
                    Debug.LogError(ex.Message);
                }
                if (response.success)
                    LoadMonsterData();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }
        #endregion

        #region Farm
        public static async void LoadFarmData()
        {
            try
            {
                if (IsLoadingFarm) return;
                IsLoadingFarm = true;
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.GetFarmBalance}";
                var request = HTTPRequest.CreatePost(url);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                ServerResponse response = new();
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError(response.message);
                }
                catch (Exception ex)
                {
                    Debug.LogException(ex);
                }
                GameDataController.MergeFarms(response.data.player_farm_list);
                IsLoadingFarm = false;
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        public static async Task<bool> UpdateFarmData(List<SerializePlayerFarm> saveFarm)
        {
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.UpdateFarm}";
            var request = HTTPRequest.CreatePost(url);
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);

            var requestBody = new Dictionary<string, object>
            {
                {"player_farm_list", saveFarm}
            };

            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
            ServerResponse response;
            try
            {
                response = await request.GetFromJsonResultAsync<ServerResponse>();
                if (!response.success)
                    Debug.LogError(response.message);
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
                return false;
            }
            return response.success;
        }

        public static bool IsUpdatingSetup { get; private set; }
        public static async void LoadFarmSetup()
        {
            try
            {
                while (IsUpdatingSetup)
                    await UniTask.WaitForSeconds(3);
                IsUpdatingSetup = true;
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.Get_FarmSetup_URL}";
                var request = HTTPRequest.CreatePost(url);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                ServerResponse response = new();
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError(response.message);
                }
                catch (Exception ex)
                {
                    Debug.LogException(ex);
                }
                if (response.success)
                    GameDataController.MergeFarmSetup(response.data.farm_setup);
                IsUpdatingSetup = false;
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        public static async void UpdateFarmSetup(List<SerializeFarmSetupSaving> saveSetup)
        {
            try
            {
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.Update_Farm_Setup_URL}";
                var request = HTTPRequest.CreatePost(url);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                var requestBody = new Dictionary<string, object>
                {
                    {"farm_setups", saveSetup }
                };
                request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
                try
                {
                    var response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError($"{response.message}");
                }
                catch (Exception ex)
                {
                    Debug.LogException(ex);
                    return;
                }
                LoadFarmSetup();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        public static async void LoadPartySetup()
        {
            try
            {
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.Get_PartySetup_URL}";
                var request = HTTPRequest.CreatePost(url);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                ServerResponse response = new();
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError(response.message);
                }
                catch (Exception ex)
                {
                    Debug.LogException(ex);
                }
                if (response.success)
                {
                    GameDataController.MergePartySetup(response.data.party_setup);
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        public static async void UpdatePartySetup(List<SerializePartyGroupSaving> saveParty)
        {
            try
            {
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.Update_Party_Setup_URL}";
                var request = HTTPRequest.CreatePost(url);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                var requestBody = new Dictionary<string, object>
                {
                    {"party_setups", saveParty }
                };
                request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
                try
                {
                    var response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError(response.message);
                }
                catch (Exception ex)
                {
                    Debug.LogError(ex.Message);
                }
                LoadPartySetup();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        #endregion

        #region Player Data
        public static async UniTask<bool> LoginWithWallet(string signature, string walletAddress, bool testAccount)
        {
            BackendLoadData.Instance.IsTest = testAccount;
            var request = HTTPRequest.CreatePost($"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.BackendLoginUrl}");
            var requestBody = new Dictionary<string, object>
            {
                {"address_wallet",walletAddress },
                {"signature",signature },
                {"message",BackendLoadData.Instance.Message }
            };
            Dictionary<string, object> response = new();
            try
            {
                response = await request.GetFromJsonResultAsync<Dictionary<string, object>>();
            }
            catch (Exception e)
            {
                if (e is not RequestException error) return false;
                BackendLoadData.Instance.ShowErrorCanvas("Error", $"login_{e.Message}");
                return false;
            }
            return (bool)response["success"];
        }

        public static async void UpdatePlayerFilters(MonsterBagFilterData filterData)
        {
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.UpdateGameProgress}";
            var request = HTTPRequest.CreatePost(url);
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            var requestBody = new Dictionary<string, object>()
            {
                { "player_game_progress", new Dictionary<string, object>{
                    { "monster_bag_filter", new Dictionary<string, object>{
                        {"is_favorite",filterData.m_isFavorite },
                        {"is_memory",filterData.m_isMemory },
                        {"sort_index",filterData.m_sortFilter },
                        {"skill_filter",filterData.m_skillFilter },
                        {"trait_filter",filterData.m_traitFilter },
                        {"main_seed_filter",filterData.m_mainSeedFilter },
                        {"sub_seed_filter",filterData.m_subSeedFilter },
                        {"growth_type_filter",filterData.m_growthTypeFilter },
                        {"rank_filter",filterData.m_rankFilter },
                        {"born_type_filter",filterData.m_bornTypeFilter },
                        {"cycle_filter",filterData.m_cycleFilter },
                        {"terrain_filter",filterData.m_terrainFilter }
                    } }
                } }
            };
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
            Dictionary<string, object> response = new();
            try
            {
                response = await request.GetFromJsonResultAsync<Dictionary<string, object>>();
            }
            catch (Exception e)
            {
                if (e is not RequestException error) return;
                BackendLoadData.Instance.ShowErrorCanvas("Error", $"{e.Message}");
                return;
            }
        }

        public static async void UpdatePlayerFilters(FarmListFilterData filterdata)
        {
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.UpdateGameProgress}";
            var request = HTTPRequest.CreatePost(url);
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            var requestBody = new Dictionary<string, object>()
            {
                { "player_game_progress", new Dictionary<string, object>{
                    {"farm_list_filter",new Dictionary<string, object>
                    {
                        {"sort_index" , filterdata.m_sortIndex },
                        {"terrain_filter" , filterdata.m_terrainFilter },
                        {"boost_hp_rank_filter" , filterdata.m_boostHpRankFilter },
                        {"boost_str_rank_filter" , filterdata.m_boostStrRankFilter },
                        {"boost_int_rank_filter" , filterdata.m_boostIntRankFilter },
                        {"boost_dex_rank_filter" , filterdata.m_boostDexRankFilter },
                        {"boost_agi_rank_filter" , filterdata.m_boostAgiRankFilter },
                        {"boost_vit_rank_filter" , filterdata.m_boostVitRankFilter },
                        {"boost_stress_rank_filter" , filterdata.m_boostStressRankFilter },
                        {"boost_fatigue_rank_filter" , filterdata.m_boostFatigueRankFilter },
                        {"boost_rest_rank_filter" , filterdata.m_boostRestRankFilter },
                        {"potential_hp_rank_filter" , filterdata.m_potencialHpRankFilter },
                        {"potential_str_rank_filter" , filterdata.m_potencialStrRankFilter },
                        {"potential_int_rank_filter" , filterdata.m_potencialIntRankFilter },
                        {"potential_dex_rank_filter" , filterdata.m_potencialDexRankFilter },
                        {"potential_agi_rank_filter" , filterdata.m_potencialAgiRankFilter },
                        {"potential_vit_rank_filter" , filterdata.m_potencialVitRankFilter },
                        {"potential_stress_rank_filter" , filterdata.m_potencialStressRankFilter },
                        {"potential_fatigue_rank_filter" , filterdata.m_potencialFatigueRankFilter },
                        {"potential_rest_rank_filter" , filterdata.m_potencialRestRankFilter },
                        {"special_main_rank_filter" , filterdata.m_specialMainRankFilter },
                        {"special_sub_rank_filter" , filterdata.m_specialSubRankFilter },
                        {"trait_filter" , filterdata.m_traitFilter }
                    } }
                } }
            };
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
            Dictionary<string, object> response = new();
            try
            {
                response = await request.GetFromJsonResultAsync<Dictionary<string, object>>();
            }
            catch (Exception e)
            {
                if (e is not RequestException error) return;
                BackendLoadData.Instance.ShowErrorCanvas("Error", $"{e.Message}");
                return;
            }
        }

        public static async void UpdatePlayerFilters(FarmItemInventoryFilterData filterData)
        {
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.UpdateGameProgress}";
            var request = HTTPRequest.CreatePost(url);
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            var requestBody = new Dictionary<string, object>()
            {
                { "player_game_progress", new Dictionary<string, object>{
                    {"farm_item_filter",new Dictionary<string, object>
                    {
                        {"sort_index",filterData.m_sortIndex },
                        {"item_effect_filter",filterData.m_itemEffect },
                        {"item_quality_filter",filterData.m_itemQuality }
                    } }
                } }
            };
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
            Dictionary<string, object> response = new();
            try
            {
                response = await request.GetFromJsonResultAsync<Dictionary<string, object>>();
            }
            catch (Exception e)
            {
                if (e is not RequestException error) return;
                BackendLoadData.Instance.ShowErrorCanvas("Error", $"{e.Message}");
                return;
            }
        }
        public static async void UpdatePlayerFilters(RaiseItemInventoryFilter filterData)
        {
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.UpdateGameProgress}";
            var request = HTTPRequest.CreatePost(url);
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            var requestBody = new Dictionary<string, object>()
            {
                { "player_game_progress", new Dictionary<string, object>{
                    {"raise_item_filter",new Dictionary<string,object>{
                        {"sort_index",filterData.m_sortIndex },
                        {"item_effect_filter",filterData.m_itemEffect },
                        {"item_quality_filter",filterData.m_itemQuality }
                    } }
                }
            }
            };
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
            Dictionary<string, object> response = new();
            try
            {
                response = await request.GetFromJsonResultAsync<Dictionary<string, object>>();
            }
            catch (Exception e)
            {
                if (e is not RequestException error) return;
                BackendLoadData.Instance.ShowErrorCanvas("Error", $"{e.Message}");
                return;
            }
        }
        public static async void UpdatePlayerFilters(GeneralItemInventoryFilter filterData)
        {
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.UpdateGameProgress}";
            var request = HTTPRequest.CreatePost(url);
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            var requestBody = new Dictionary<string, object>()
            {
                { "player_game_progress", new Dictionary<string, object>{
                    {"general_inventory_filter",new Dictionary<string, object>
                    {
                        {"sort_index",filterData.m_sortIndex },
                        {"item_effect_filter",filterData.m_itemEffect },
                        {"item_quality_filter",filterData.m_itemQuality },
                        {"filter_type",filterData.m_filterType }
                    } }
                } }
            };
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
            Dictionary<string, object> response = new();
            try
            {
                response = await request.GetFromJsonResultAsync<Dictionary<string, object>>();
            }
            catch (Exception e)
            {
                if (e is not RequestException error) return;
                BackendLoadData.Instance.ShowErrorCanvas("Error", $"{e.Message}");
                return;
            }
        }

        public static async void UpdatePlayerData()
        {
            try
            {
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.UpdatePlayerInfo}";
                var request = HTTPRequest.CreatePost(url);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                var requestBody = new Dictionary<string, object>
                {
                    {"player_name" ,GameDataManager.Instance.LoadedPlayerData.PlayerName},
                    {"player_bio", GameDataManager.Instance.LoadedPlayerData.PlayerBio},
                    {"player_language", LocalizationManager.CurrentLanguageCode },
                    {"player_rank", (int)GameProgressManager.Instance.GameProgress.PlayerRank}
                };
                request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
                ServerResponse response = new();
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError(response.message);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"{ex.Message}");
                }
                if (response.success)
                    LoadPlayerData();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        public static async void LoadPlayerData()
        {
            try
            {
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.PlayerInfo}";
                var request = HTTPRequest.CreatePost(url);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                ServerResponse response = new();
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError(response.message);
                }
                catch (Exception ex)
                {
                    Debug.LogError(ex.Message);
                }
                if (response.success)
                {
                    GameDataController.LoadPlayerData(response.data.player_info);
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }
        #endregion

        #region Item
        public static async void CreateCoach(SerializeCoachItemJson coach)
        {
            try
            {
                if (IsCreatingCoach) return;
                IsCreatingCoach = true;
                IsCreateCoachSuccess = true;
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.Create_Coach_Url}";
                var requestBody = new Dictionary<string, object>
                {
                    {"item_coach",coach }
                };
                var request = HTTPRequest.CreatePost(url);
                request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                var response = new ServerResponse();
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError(response.message);
                }
                catch (Exception e)
                {
                    Debug.LogError(e.Message);
                }
                IsCreatingCoach = false;
                IsCreateCoachSuccess = response.success;
                LoadItemCoach();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        public static async void CreateCrystal(SerializeCrystalItemJson crystal)
        {
            try
            {
                if (IsCreatingCrystal) return;
                IsCreatingCrystal = true;
                IsCreateCrystalSuccess = true;
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.Create_Crystal_Url}";
                var requestBody = new Dictionary<string, object>
                {
                    {"item_crystal",crystal }
                };
                var request = HTTPRequest.CreatePost(url);
                request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                var response = new ServerResponse();
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError(response.message);
                }
                catch (Exception e)
                {
                    Debug.LogError(e.Message);
                }
                IsCreatingCrystal = false;
                IsCreateCrystalSuccess = response.success;
                LoadItemCrystal();
            }
            catch (Exception e)
            {
                IsCreatingCrystal = false;
                Debug.LogException(e);
            }
        }

        private static async Task ConsumeCrystal(List<SerializeItem> crystalConsumeList, string source)
        {
            if (crystalConsumeList.Count <= 0)
            {
                return;
            }
            IsBurningCrystal = true;
            List<UniTask<ServerResponse>> burnCrystalBlockchain = new();
            foreach (var item in crystalConsumeList)
            {
                burnCrystalBlockchain.Add(Burn_Crystal(item.CrystalizeItemData, source));
            }
            IsBurningCrystal = false;
            var responses = await UniTask.WhenAll(burnCrystalBlockchain.ToArray());
            RequestCrystalBurnSuccess = responses.FirstOrDefault(x => string.IsNullOrEmpty(x.txid)) == null;
            LoadItemCrystal();
        }

        private static async UniTask<ServerResponse> Burn_Crystal(SerializeCrystalizeItem crystalizeItemData, string source)
        {
            var request = HTTPRequest.CreatePost($"{BackendLoadData.Instance.ServerInfo.BlockchainUrl}{BackendLoadData.Instance.ServerInfo.Burn_Crystal_Blockchain_URL}?tokenId={crystalizeItemData.ItemId}");
            ServerResponse response = new ServerResponse();
            try
            {
                response = await request.GetFromJsonResultAsync<ServerResponse>();
                if(string.IsNullOrEmpty(response.txid) || response.code == 400)
                {
                    BackendLoadData.Instance.LogEvent("burn_crystal_failed", source, $"game 2 {crystalizeItemData.ItemId} 1");
                }
                else
                {
                    BackendLoadData.Instance.LogEvent("burn_crystal_success", source, $"game 2 {crystalizeItemData.ItemId} 1");
                    response.success = true;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"HTTP Request failed: {e.Message}");
                BackendLoadData.Instance.LogEvent("burn_crystal_failed", source, $"game 2 {crystalizeItemData.ItemId} 1");
                return new();
            }
            try
            {
                await BackendLoadData.Instance.Burn_Crystal(new SerializeCrystalItemJson
                {
                    _id = crystalizeItemData.ServerId,
                    item_token_id = crystalizeItemData.ItemId,
                    item_name = crystalizeItemData.CrystalMonsterName,
                    crystal_health = crystalizeItemData.CrystalParameter.Health,
                    crystal_strength = crystalizeItemData.CrystalParameter.Strength,
                    crystal_intelligent = crystalizeItemData.CrystalParameter.Intelligent,
                    crystal_dexterity = crystalizeItemData.CrystalParameter.Dexterity,
                    crystal_agility = crystalizeItemData.CrystalParameter.Agility,
                    crystal_vitality = crystalizeItemData.CrystalParameter.Vitality,
                    crytal_trait_id = crystalizeItemData.CrystalTraitId
                });
            }
            catch (Exception ex)
            {
                Debug.LogWarning("Burn fail from backend server " + ex.Message);
            }
            return response;
        }

        private static async Task ConsumeCoach(List<SerializeItem> coachConsumeList, string source)
        {
            if (coachConsumeList.Count <= 0)
            {
                return;
            }
            IsBurningCoach = true;
            List<UniTask<ServerResponse>> burnCoachBlockchainList = new();
            foreach (var item in coachConsumeList)
            {
                burnCoachBlockchainList.Add(Burn_Coach(item.CoachItemData, source));
            }

            ServerResponse[] responses = await UniTask.WhenAll(burnCoachBlockchainList.ToArray());
            IsBurningCoach = false;
            RequestCoachBurnSucces = responses.FirstOrDefault(x => x.success) != null;
            LoadItemCoach();
        }

        private static async UniTask<ServerResponse> Burn_Coach(SerializeCoachItem coachItemData, string source)
        {
            var request = HTTPRequest.CreatePost($"{BackendLoadData.Instance.ServerInfo.BlockchainUrl}{BackendLoadData.Instance.ServerInfo.Burn_Coach_Blockchain_URL}?tokenId={coachItemData.ItemId}");
            ServerResponse response = new();
            try
            {
                response = await request.GetFromJsonResultAsync<ServerResponse>();
                if (string.IsNullOrEmpty(response.txid) || response.code == 400)
                {
                    BackendLoadData.Instance.LogEvent("burn_coach_failed", source, $"game 2 {coachItemData.ItemId} 1");
                }
                else
                {
                    BackendLoadData.Instance.LogEvent("burn_coach_success", source, $"game 2 {coachItemData.ItemId} 1");
                    response.success = true;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"HTTP Request failed: {e.Message}");
                BackendLoadData.Instance.LogEvent("burn_coach_failed", source, $"game 2 {coachItemData.ItemId} 1");
                return new();
            }
            try
            {
                await BackendLoadData.Instance.Burn_Coach(new SerializeCoachItemJson
                {
                    _id = coachItemData.ServerId,
                    item_token_id = coachItemData.ItemId.ToString(),
                    item_name = coachItemData.ItemName,
                    coach_value = coachItemData.CoachParameterValue,
                    coach_value_type = (int)coachItemData.CoachParameter,
                    coach_monster_id = coachItemData.MonsterCoachFrom,
                    main_seed = (int)coachItemData.MainSeed,
                    sub_seed = (int)coachItemData.SubSeed,
                    monster_tga_id = coachItemData.MonsterPublicId
                });
            }
            catch (Exception e)
            {
                Debug.LogWarning("Burn error from backend server " + e.Message);
            }
            return response;
        }

        private static async Task DeleteItem(List<SerializeItem> deleteItemList, List<int> deleteAmountList, string source)
        {
            while (IsBurningItem)
                await UniTask.WaitForSeconds(1);
            IsBurningItem = true;
            if (deleteItemList.Count <= 0)
            {
                RequestBurnItemSuccess = true;
                IsBurningItem = false;
                return;
            }
            List<SerializeItem> trainingItems = new();
            List<SerializeItem> enhanceItems = new();
            List<SerializeItem> fusionItems = new();
            List<SerializeItem> regenerateItems = new();

            List<int> deleteTrainingItems = new();
            List<int> deleteEnhanceItems = new();
            List<int> deleteFusionItems = new();
            List<int> deleteRegenerateItems = new();
            for (var i = 0; i < deleteItemList.Count; i++)
            {
                var item = deleteItemList[i];
                switch (item.Item)
                {
                    case ScriptableTrainingItem trainingItem:
                        {
                            trainingItems.Add(item);
                            deleteTrainingItems.Add(Mathf.Abs(deleteAmountList[i]));
                            break;
                        }
                    case ScriptableEnhanceItem enhanceItem:
                        {
                            enhanceItems.Add(item);
                            deleteEnhanceItems.Add(Mathf.Abs(deleteAmountList[i]));
                            break;
                        }
                    case ScriptableFushionItem fusionItem:
                        {
                            fusionItems.Add(item);
                            deleteFusionItems.Add(Mathf.Abs(deleteAmountList[i]));
                            break;
                        }
                    case ScriptableRegenerationItem regenerationItem:
                        {
                            regenerateItems.Add(item);
                            deleteRegenerateItems.Add(Mathf.Abs(deleteAmountList[i]));
                            break;
                        }
                }
            }

            if (trainingItems.Count > 0)
            {
                var syncRequest = await BackendLoadData.Instance.DeleteItem(1, trainingItems.Select(x => x.Item.ItemId).ToArray(), deleteTrainingItems.ToArray(), source);
                if (string.IsNullOrEmpty(syncRequest.txid))
                {
                    RequestBurnItemSuccess = false;
                    Debug.LogError(syncRequest.message);
                    throw new Exception(syncRequest.message);
                }
            }

            if (enhanceItems.Count > 0)
            {
                var syncRequest = await BackendLoadData.Instance.DeleteItem(2, enhanceItems.Select(x => x.Item.ItemId).ToArray(), deleteEnhanceItems.ToArray(), source);
                if (string.IsNullOrEmpty(syncRequest.txid))
                {
                    RequestBurnItemSuccess = false;
                    Debug.LogError(syncRequest.message);
                    throw new Exception(syncRequest.message);
                }
            }

            if (regenerateItems.Count > 0)
            {
                var syncRequest = await BackendLoadData.Instance.DeleteItem(4, regenerateItems.Select(x => x.Item.ItemId).ToArray(), deleteRegenerateItems.ToArray(), source);
                if (string.IsNullOrEmpty(syncRequest.txid))
                {
                    RequestBurnItemSuccess = false;
                    Debug.LogError(syncRequest.message);
                    throw new Exception(syncRequest.message);
                }
            }

            if (fusionItems.Count > 0)
            {
                var syncRequest = await BackendLoadData.Instance.DeleteItem(3, fusionItems.Select(x => x.Item.ItemId).ToArray(), deleteFusionItems.ToArray(), source);
                if (string.IsNullOrEmpty(syncRequest.txid))
                {
                    RequestBurnItemSuccess = false;
                    Debug.LogError(syncRequest.message);
                    throw new Exception(syncRequest.message);
                }
            }
            RequestBurnItemSuccess = true;
            IsBurningItem = false;
        }

        private static async Task CreateItem(List<SerializeItem> createItemList, List<int> createAmountList, bool notifyMint, string source)
        {
            if (createItemList.Count <= 0)
            {
                return;
            }
            if (IsCreatingItem) return;
            IsCreatingItem = true;
            IsCreateItemSuccess = false;
            List<SerializeItem> trainingItems = new();
            List<SerializeItem> enhanceItems = new();
            List<SerializeItem> fusionItems = new();
            List<SerializeItem> regenerateItems = new();
            List<SerializeItem> specificMintItems = new();

            List<int> create_trainingItems = new();
            List<int> create_enhanceItems = new();
            List<int> create_fusionItems = new();
            List<int> create_regenerateItems = new();
            for (var i = 0; i < createItemList.Count; i++)
            {
                var item = createItemList[i];
                switch (item.Item)
                {
                    case ScriptableTrainingItem trainingItem:
                        {
                            trainingItems.Add(item);
                            create_trainingItems.Add(createAmountList[i]);
                            break;
                        }
                    case ScriptableEnhanceItem enhanceItem:
                        {
                            enhanceItems.Add(item);
                            create_enhanceItems.Add(createAmountList[i]);
                            if (enhanceItem.FarmBasicParameterEnhance.Health > 0 ||
                                enhanceItem.FarmBasicParameterEnhance.Strength > 0 ||
                                enhanceItem.FarmBasicParameterEnhance.Intelligent > 0 ||
                                enhanceItem.FarmBasicParameterEnhance.Dexterity > 0 ||
                                enhanceItem.FarmBasicParameterEnhance.Vitality > 0 ||
                                enhanceItem.FarmBasicParameterEnhance.Agility > 0 ||
                                enhanceItem.FarmFatigueEnhanceValue > 0 ||
                                enhanceItem.FarmRestEnhanceValue > 0 ||
                                enhanceItem.FarmStressEnhanceValue > 0)
                            {
                                if (GameProgressManager.Instance.GameProgress.FirstEnhancementItem == default)
                                    AssistantManager.Instance.NewEnhanceItemObtained = true;
                            }
                            break;
                        }
                    case ScriptableFushionItem fusionItem:
                        {
                            fusionItems.Add(item);
                            create_fusionItems.Add(createAmountList[i]);
                            break;
                        }
                    case ScriptableRegenerationItem regenerationItem:
                        {
                            if (regenerationItem.LockMonsterData is not null)
                            {
                                specificMintItems.Add(item);
                                break;
                            }
                            regenerateItems.Add(item);
                            create_regenerateItems.Add(createAmountList[i]);
                            break;
                        }
                }
            }

            if (trainingItems.Count > 0)
            {
                var syncRequest = await BackendLoadData.Instance.CreateItem(1,
                    trainingItems.Select(x => x.Item.ItemId).ToArray(), create_trainingItems.ToArray(), source);
                if (string.IsNullOrEmpty(syncRequest.txid))
                {
                    IsCreateItemSuccess = false;
                    IsCreatingItem = false;
                    throw new Exception(syncRequest.message);
                }
            }

            if (enhanceItems.Count > 0)
            {
                var syncRequest = await BackendLoadData.Instance.CreateItem(2,
                    enhanceItems.Select(x => x.Item.ItemId).ToArray(), create_enhanceItems.ToArray(), source);
                if (string.IsNullOrEmpty(syncRequest.txid))
                {
                    IsCreateItemSuccess = false;
                    IsCreatingItem = false;
                    throw new Exception(syncRequest.message);
                }
            }

            if (regenerateItems.Count > 0)
            {
                var syncRequest = await BackendLoadData.Instance.CreateItem(4,
                    regenerateItems.Select(x => x.Item.ItemId).ToArray(), create_regenerateItems.ToArray(), source);
                if (string.IsNullOrEmpty(syncRequest.txid))
                {
                    IsCreateItemSuccess = false;
                    IsCreatingItem = false;
                    throw new Exception(syncRequest.message);
                }
            }

            if (fusionItems.Count > 0)
            {
                var syncRequest = await BackendLoadData.Instance.CreateItem(3,
                    fusionItems.Select(x => x.Item.ItemId).ToArray(), create_fusionItems.ToArray(), source);
                if (string.IsNullOrEmpty(syncRequest.txid))
                {
                    IsCreateItemSuccess = false;
                    IsCreatingItem = false;
                    throw new Exception(syncRequest.message);
                }
            }

            if (specificMintItems.Count > 0)
            {
                var mintType = specificMintItems.Select(t => t.Item as ScriptableRegenerationItem).Select(item => item!.LockMonsterData.MonsterPublicNumber).ToList();
                var grouped = mintType.GroupBy(x => x)
                         .Select(g => new { Number = g.Key, Count = g.Count() });
                var uniqueNumbers = new List<int>();
                var counts = new List<int>();

                foreach (var item in grouped)
                {
                    uniqueNumbers.Add(item.Number);
                    counts.Add(item.Count);
                }
                await BackendLoadData.Instance.CreateSpecifiedItem(uniqueNumbers, counts);
            }
            if (UIPopupNotify.Instance && notifyMint)
            {
                UINotifyManager.RemoveNotify("Mint item notice");
                UINotifyManager.AddNotifyResponse("Mint item notice", () =>
                {
                    string notify = I2.Loc.LocalizationManager.GetTranslation("General/Item Receive Delay");
                    UIPopupNotify.Instance.SetNotify("Notice!", notify, UINotifyManager.ProcessNotify);
                }, UINotifyManager.NotifyType.DefaultNotify, 999);
            }
            IsCreateItemSuccess = true;
            IsCreatingItem = false;
        }

        public static bool IsChangingItemBalance { get; private set; }
        public static async void UpdateItemData(List<SerializeItem> createItemList, List<int> createAmountList, List<SerializeItem> deleteItemList, List<int> deleteAmountList, List<SerializeItem> coachConsumeList, List<SerializeItem> crystalConsumeList, bool notifyMint, string source)
        {
            try
            {
                IsChangingItemBalance = true;
                await CreateItem(createItemList, createAmountList, notifyMint, source);
                await DeleteItem(deleteItemList, deleteAmountList, source);
                await ConsumeCoach(coachConsumeList, source);
                await ConsumeCrystal(crystalConsumeList, source);
                IsChangingItemBalance = false;
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                IsChangingItemBalance = false;
            }
        }

        public static async void LoadItemData(bool haveResultSuccessNotice = false)
        {
            try
            {
                while (IsFetchingItem) await UniTask.WaitForSeconds(1);
                IsFetchingItem = true;
                var url =
                    $"{BackendLoadData.Instance.ServerInfo.BlockchainUrl}{BackendLoadData.Instance.ServerInfo.BlockchainItemsBalanceUrl}?address={GameDataManager.Instance.LoadedPlayerData.PlayerWallet}";
                var request = HTTPRequest.CreateGet(url);
                var response = await request.GetFromJsonResultAsync<ItemsApi>();
                // API nay khong co success
                // if (!response.success)
                //     Debug.LogError(response.message);
                GameDataController.SetItemBalance(response.data);
            }
            catch (Exception e)
            {
                Debug.LogError($"Request finished with error! Error: {e.Message}");
                IsFetchingItem = false;
            }
            finally
            {
                IsFetchingItem = false;
            }
        }

        public static async void LoadItemCoach()
        {
            try
            {
                if (IsFetchingCoach) return;
                IsFetchingCoach = true;
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.Get_Coach_Url}";
                var request = HTTPRequest.CreatePost(url);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                var response = new ServerResponse();
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError(response.message);
                }
                catch (Exception ex)
                {
                    Debug.LogError(ex.Message);
                }
                if (response.success)
                    GameDataController.SetCoachData(response.data.item_coach);
                IsFetchingCoach = false;
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        public static async void LoadItemCrystal()
        {
            try
            {
                if (IsFetchingCrystal) return;
                IsFetchingCrystal = true;
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.Get_Crystal_Url}";
                var request = HTTPRequest.CreatePost(url);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                var response = new ServerResponse();
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError(response.message);
                }
                catch (Exception ex)
                {
                    Debug.LogError(ex);
                }
                if (response.success)
                    GameDataController.SetCrystalData(response.data.item_crystal);
                IsFetchingCrystal = false;
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        public static async UniTask LoadItemHashHash()
        {
            var reqGeneral =
                HTTPRequest.CreateGet(
                    $"{BackendLoadData.Instance.ServerInfo.BlockchainUrl}{BackendLoadData.Instance.ServerInfo.GeneralHashUrl}?address={GameDataManager.Instance.LoadedPlayerData.PlayerWallet}");
            var reqGenesis =
                HTTPRequest.CreateGet(
                    $"{BackendLoadData.Instance.ServerInfo.BlockchainUrl}{BackendLoadData.Instance.ServerInfo.GenesisHashUrl}?address={GameDataManager.Instance.LoadedPlayerData.PlayerWallet}");
            var reqHashChip =
                HTTPRequest.CreateGet(
                    $"{BackendLoadData.Instance.ServerInfo.BlockchainUrl}{BackendLoadData.Instance.ServerInfo.HashChipReedemedUrl}?address={GameDataManager.Instance.LoadedPlayerData.PlayerWallet}");

            try
            {
                IsFetchingItem = true;
                var (resGeneral, resGenesis, resHashChip) = await UniTask.WhenAll(
                    reqGeneral.GetFromJsonResultAsync<HashBoxApi>(),
                    reqGenesis.GetFromJsonResultAsync<HashBoxApi>(),
                    reqHashChip.GetFromJsonResultAsync<ReedemChipResponse>());
                GameDataController.SetHashData(resGeneral, resGenesis, resHashChip);
            }
            catch (Exception ex)
            {
                IsFetchingCoach = false;
                Debug.LogError($"Request finished with error! Error: {ex.Message}");
            }
            finally
            {
                IsFetchingCoach = false;
            }
        }

        public static async UniTask LoadItemOther()
        {
            var reqLicense =
                HTTPRequest.CreateGet(
                    $"{BackendLoadData.Instance.ServerInfo.BlockchainUrl}{BackendLoadData.Instance.ServerInfo.BlockchainItemsTrainerBalanceUrl}?address={GameDataManager.Instance.LoadedPlayerData.PlayerWallet}");
            var reqTrophy =
                HTTPRequest.CreateGet(
                    $"{BackendLoadData.Instance.ServerInfo.BlockchainUrl}{BackendLoadData.Instance.ServerInfo.BlockchainItemsTrophyBalanceUrl}?address={GameDataManager.Instance.LoadedPlayerData.PlayerWallet}");
            var reqPoc =
                HTTPRequest.CreateGet(
                    $"{BackendLoadData.Instance.ServerInfo.BlockchainUrl}{BackendLoadData.Instance.ServerInfo.PocBalance}?address={GameDataManager.Instance.LoadedPlayerData.PlayerWallet}");

            try
            {
                IsFetchingItem = true;
                var (resLicense, resTrophy, resPoc) = await UniTask.WhenAll(
                    reqLicense.GetFromJsonResultAsync<BlockchainResponse>(),
                    reqTrophy.GetFromJsonResultAsync<ItemDetailApiData>(),
                    reqPoc.GetFromJsonResultAsync<BlockchainResponseSbt>());
                GameDataController.SetOtherItems(resLicense, resTrophy, resPoc);
            }
            catch (Exception e)
            {
                IsFetchingCoach = false;
                Debug.LogError($"Request finished with error! Error: {e.Message}");
            }
            finally
            {
                IsFetchingCoach = false;
            }
        }
        #endregion

        #region Tournament PvE
        public static async void LoadTournament()
        {
            try
            {
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.GetTournamentData}";
                var request = HTTPRequest.CreatePost(url);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                var response = new ServerResponse();
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError(response.message);
                }
                catch (Exception ex)
                {
                    Debug.LogError(ex.Message);
                }
                if (response.success)
                    GameDataController.SetTournament(response.data.player_tournaments_data);
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        public static async void UpdateTournamentData(List<SerializeTournamentSaving> savingTournamentList)
        {
            try
            {
                var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.UpdateTournamentData}";
                var request = HTTPRequest.CreatePost(url);
                request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
                var requestBody = new Dictionary<string, object>
                {
                    {"player_tournaments_data", savingTournamentList }
                };
                request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
                var response = new ServerResponse();
                try
                {
                    response = await request.GetFromJsonResultAsync<ServerResponse>();
                    if (!response.success)
                        Debug.LogError(response.message);
                }
                catch (Exception ex)
                {
                    Debug.LogError(ex.Message);
                }
                if (response.success)
                    LoadTournament();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        public static async void SaveRegenHistory(Dictionary<string, object> requestBody)
        {
            var url = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.AddRegenHistory}";
            var request = HTTPRequest.CreatePost(url);
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);
            var response = new ServerResponse();
            try
            {
                response = await request.GetFromJsonResultAsync<ServerResponse>();
                if (!response.success)
                    Debug.LogError(response.message);
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }
        #endregion

        public static async void LoadGameProgress()
        {
            var request = HTTPRequest.CreatePost($"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.GetGameProgress}");
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            var response = new Dictionary<string, object>();
            try
            {
                response = await request.GetFromJsonResultAsync<Dictionary<string, object>>();
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
            bool success = ObjectConvertionHelper.ConvertTo<bool>(response, "success");
            if (!success)
            {
                string message = ObjectConvertionHelper.ConvertTo<string>(response, "message");
                Debug.LogError($"progress_{message}");
                return;
            }
            Dictionary<string, object> data = ObjectConvertionHelper.ConvertTo<Dictionary<string, object>>(response, "data");
            Dictionary<string, object> player_game_progress = ObjectConvertionHelper.ConvertTo<Dictionary<string, object>>(data, "player_game_progress");
            GameDataController.ConvertGameProgress(player_game_progress);
        }
    }
}