using Assets.Scripts.Monster.PVE_Monster;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace CustomBattle.BattleSelectReviveMonsterUI
{
    public class SelectReviveMonsterElement : MonoBehaviour
    {
        private SelectMonsterToReviveUI _selectMonsterToReviveUI;

        private MonsterInGame _monsterInGame;

        public Image monsterAvatar;

        public TextMeshProUGUI nameTxt;
        public TextMeshProUGUI ingameIDTxt;

        public Button button;

        private void Start()
        {
            button.onClick.AddListener(Select);
        }

        public void Setup(MonsterInGame monster, SelectMonsterToReviveUI reviveUI)
        {
            monsterAvatar.sprite = monster.MonsterInfo.MonsterScriptableData.MonsterScreenshot;

            _selectMonsterToReviveUI = reviveUI;

            _monsterInGame = monster;

            nameTxt.text = _monsterInGame.MonsterInfo.MonsterName;

            ingameIDTxt.text = Helpers.ConvertNumberToAlphabet(_monsterInGame.BattleID);

            gameObject.SetActive(true);
        }

        public void Deactive()
        {
            _monsterInGame = null;

            gameObject.SetActive(false);
        }

        private void Select()
        {
            _selectMonsterToReviveUI.SelectToSpawn(_monsterInGame);
        }
    }
}
