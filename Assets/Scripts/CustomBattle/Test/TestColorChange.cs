using UnityEngine;
using UnityEngine.UI;

namespace CustomBattle.Test
{
    public class TestColorChange : MonoBehaviour
    {
        public Image image;
        // Update is called once per frame
        void Start()
        {
        
            image = GetComponent<Image>();
        

        }
        private void Update()
        {
            Color tim = new Color(1f, 0.1f, 0f, 1f);
            image.color = tim;
        }
    }
}
