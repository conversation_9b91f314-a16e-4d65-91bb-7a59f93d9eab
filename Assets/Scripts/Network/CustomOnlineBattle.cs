using System;
using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.Controllers;
using Assets.Scripts.DataStruct;
using Assets.Scripts.Managers;
using Assets.Scripts.SerializeDataStruct;
using Assets.Scripts.Tournament;
using Assets.Scripts.UI;
using Assets.Scripts.UI.BattleUI.MonsterPlacement;
using Assets.Scripts.UI.BattleUI.PvP;
using CustomBattle;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using I2.Loc;
using TMPro;
using TurnBasedToolsAsset.Scripts.Gameplay.GameRules;
using UI.PlayerInfo;
using UI.UIFriend;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Network
{
    public class CustomOnlineBattle : StaticInstance<CustomOnlineBattle>
    {
        [SerializeField] private GameObject countdown;
        [SerializeField] private TextMeshProUGUI countDownText;
        [SerializeField] private Slider countDownSlider;
        [SerializeField] private UIMonsterSpawningControl spawnControl;
        [SerializeField] private UIMonsterPlacementControl placementControl;

        [SerializeField] private UIBattlePlayerInfo playerBlue;
        [SerializeField] private UIBattlePlayerInfo playerRed;

        [SerializeField] private UIPopupNotify oppSurrNotfiy;
        public Action OnStartCountDownEnd;
        public Action OnCountDownEnd;
        public UnityEvent onCompleteAbility;
        public UnityEvent onCompleteMoveAway;
        public UIMonsterPlacementControl UIMonsterPlacementControl => placementControl;

        private void Start()
        {
            countdown.gameObject.SetActive(false);
            if (CustomPvpModeAI.Instance.IsPvpModeAI)
            {
                countdown.gameObject.SetActive(true);
                SetupSliderValue(60);
            }
            else
            {
                if (CustomNetworkManager.Instance.GetRoom() == null) return;
                var listUserScore = new List<string>
                {
                    CustomNetworkManager.Instance.GetRoom().State.players[0].walletId,
                    CustomNetworkManager.Instance.GetRoom().State.players[1].walletId
                };
                ScoreManager.RequestSnapshots?.Invoke(ScoreController.PERSONAL_PVP_SNAPSHOT ,
                    CycleManager.Instance.CurrentCycle.CycleStartTime,
                    CycleManager.Instance.CurrentCycle.CycleEndTime, listUserScore);    
                countdown.gameObject.SetActive(true);
                SetupSliderValue(60);
                AddMessage();
            }
        }
        
        private void OnOnLeave(int code)
        {
            CustomNetworkManager.Instance.SetRoomToNull();
            var message = code is not 4000 and 1001 ? $"Room connection error {code}." : "The game is over.";
            oppSurrNotfiy.SetNotify("Notice!", message, ReTurnToHome);
            oppSurrNotfiy.OpenMenu();
        }

        // write on application quit
        protected override void OnApplicationQuit()
        {
            RemoveMessage();
            base.OnApplicationQuit();
        }

        private void OnDestroy()
        {
            RemoveMessage();
        }

        private void ReTurnToHome()
        {
            UIMonsterPlacementControl.ReturnToHome(true);
        }
        private void AddMessage()
        {
            CustomNetworkManager.StoreReconnectionToken(CustomNetworkManager.Instance.GetRoom().ReconnectionToken);
            CustomNetworkManager.Instance.StoreCurrentGameMode(CustomNetworkManager.Instance.GetRoom().Name);
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("monster_defense", OnMonsterDefense);
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("monster_endTurn", OnMonsterEndTurn);
            CustomNetworkManager.Instance.GetRoom().OnMessage<MoveMessage>("monster_move", OnMonsterMove);
            CustomNetworkManager.Instance.GetRoom().OnMessage<PlayerAction>("monster_ability", OnMonsterAbility);
            CustomNetworkManager.Instance.GetRoom().OnMessage<float>("turnCountDown", OnCountDown);
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("forceEndTurn", ForceEndTurn);
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("placement_ready", StartPlacementPhase);
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("forcePlaceMonster", ForcePlacementMonster);
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("conmemay", OpponentSurrender);
            CustomNetworkManager.Instance.GetRoom().OnLeave += OnOnLeave;
            if (CustomNetworkManager.Instance.IsSpectator())
            {
                CustomNetworkManager.Instance.ClearReconnectionToken();
                return;
            }
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("opponent_dis", OpponentDisconnected);
            CustomNetworkManager.Instance.GetRoom().OnMessage<string>("opponent_reconnect", OpponentReconnected);
        }

        private void RemoveMessage()
        {
            if (CustomNetworkManager.Instance.GetRoom() == null)
            {
                return;
            }
            
            CustomNetworkManager.Instance.GetRoom().RemoveMessage("monster_defense");
            CustomNetworkManager.Instance.GetRoom().RemoveMessage("monster_endTurn");
            CustomNetworkManager.Instance.GetRoom().RemoveMessage("monster_move");
            CustomNetworkManager.Instance.GetRoom().RemoveMessage("monster_ability");
            CustomNetworkManager.Instance.GetRoom().RemoveMessage("turnCountDown");
            CustomNetworkManager.Instance.GetRoom().RemoveMessage("forceEndTurn");
            CustomNetworkManager.Instance.GetRoom().RemoveMessage("placement_ready");
            CustomNetworkManager.Instance.GetRoom().RemoveMessage("forcePlaceMonster");
            CustomNetworkManager.Instance.GetRoom().RemoveMessage("conmemay");
            CustomNetworkManager.Instance.GetRoom().RemoveMessage("opponent_dis");
            CustomNetworkManager.Instance.GetRoom().RemoveMessage("opponent_reconnect");
            CustomNetworkManager.Instance.GetRoom().OnLeave -= OnOnLeave;
        }

        private void OpponentDisconnected(string playerId)
        {
            if (CustomNetworkManager.Instance.IsSpectator())
            {
                return;
            }

            if (playerId != CustomNetworkManager.Instance.GetRoom().State.players[0].id &&
                playerId != CustomNetworkManager.Instance.GetRoom().State.players[1].id) return;
            switch (CustomGameManager.Instance.PlayerSide)
            {
                case GameTeam.Blue:
                    CustomGameManager.Instance.SetAIActiveForBot(GameTeam.Red, true);
                    break;
                case GameTeam.Red:
                    CustomGameManager.Instance.SetAIActiveForBot(GameTeam.Blue, true);
                    break;
            }

        }

        private void OpponentReconnected(string playerId)
        {
            if (CustomNetworkManager.Instance.IsSpectator())
            {
                return;
            }

            if (playerId != CustomNetworkManager.Instance.GetRoom().State.players[0].id &&
                playerId != CustomNetworkManager.Instance.GetRoom().State.players[1].id) return;
            switch (CustomGameManager.Instance.PlayerSide)
            {
                case GameTeam.Blue:
                    CustomGameManager.Instance.SetAIActiveForBot(GameTeam.Red, false);
                    break;
                case GameTeam.Red:
                    CustomGameManager.Instance.SetAIActiveForBot(GameTeam.Blue, false);
                    break;
            }
        }

        private void OpponentSurrender(string message)
        {
            GameManager.Get().OnTeamWon.Invoke(CustomGameManager.Instance.PlayerSide);
            if (CustomNetworkManager.Instance.IsSpectator())
            {
                return;
            }

            oppSurrNotfiy.SetNotify("You Win", message, () => { });
            oppSurrNotfiy.OpenMenu();
        }

        public void SetupSliderValue(int value)
        {
            countDownSlider.maxValue = value;
            countDownSlider.value = value;
        }

        public void OnCountDown(float countdownTime, bool isNextTurn)
        {
            if (_countdownTween != null && _countdownTween.IsActive())
            {
                _countdownTween.Kill();
            }

            countDownSlider.value = countdownTime;
            _countdownTween = countDownSlider.DOValue(0, countdownTime)
                .SetEase(Ease.Linear)
                .SetUpdate(UpdateType.Normal, true) // Đảm bảo tween không bị ảnh hưởng bởi Time.timeScale
                .OnUpdate(() =>
                {
                    var currentTime = countDownSlider.value;

                    // Calculate minutes and seconds
                    var minutes = Mathf.FloorToInt(currentTime / 60);
                    var seconds = Mathf.FloorToInt(currentTime % 60);


                    var timeText = LocalizationManager.CurrentLanguageCode == "ja" ? "残り時間..." : "Remaining...";
                    countDownText.text = timeText + $"{minutes:0}:{seconds:00}";
                }).OnComplete(() =>
                {
                    if (isNextTurn)
                    {
                        OnCountDownEnd?.Invoke();
                    }
                    else
                    {
                        OnStartCountDownEnd?.Invoke();
                    }
                });
        }

        public async void UpdateModeAi()
        {
            try
            {
                var user1 = await FriendDataManager.Instance.GetUserData(GameDataManager.Instance.LoadedPlayerData.PlayerWallet);
                UpdatePlayerInfo(user1, null);
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        public async void UpdateBattlePlayerInfo()
        {
            try
            {
                var user1 = await FriendDataManager.Instance.GetUserData(CustomNetworkManager.Instance.GetRoom().State
                    .players[0].walletId);
                var user2 = await FriendDataManager.Instance.GetUserData(CustomNetworkManager.Instance.GetRoom().State
                    .players[1].walletId);
               
                UpdatePlayerInfo(user1, user2);
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        public async void UpdatePlayerPve()
        {
            try
            {
                var user1 = await FriendDataManager.Instance.GetUserData(GameDataManager.Instance.LoadedPlayerData
                    .PlayerWallet);
                UpdatePlayerInfoPve(user1);
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private void UpdatePlayerInfoPve(SerializeUserData player1)
        {
            var listBlueMonster = CustomGameManager.Instance.FriendlySpawnListInfo;
            var listRedMonster = CustomGameManager.Instance.EnemySpawnListInfos;
            var overallA = CustomGameManager.Instance.GetTotalValueFromFriendlySpawnListInfo();
            var overallB = CustomGameManager.Instance.GetTotalValueFromEnemySpawnListInfo();
            var blueOverall = Mathf.FloorToInt(overallA).ToString();
            var redOverall = Mathf.FloorToInt(overallB).ToString();
            var scoreRed = CustomPvpModeAI.Instance.RedScore;

            var userSnapshot = ScoreManager.Instance.ScoreSnapshot.FirstOrDefault(x => x.ScoreUser == player1.address_wallet && x.ScoreType == ScoreController.PERSONAL_PVP_SNAPSHOT && x.FromDate == CycleManager.Instance.CurrentCycle.CycleStartTime && x.ToDate == CycleManager.Instance.CurrentCycle.CycleEndTime);
            UpdateStartPlayerInfo(player1, listBlueMonster, blueOverall, (int)(userSnapshot?.ScoreValue ?? 0) ,player1.playerRanksEnum.ToString() ,null, listRedMonster, scoreRed,
                redOverall, player1.playerRanksEnum.ToString());
        }

        private void UpdatePlayerInfo(SerializeUserData player1, SerializeUserData player2)
        {
            var blueOverall = "0";
            var redOverall = "0";
            var scoreBlue = 0;
            var scoreRed = 0;
            var blueRank = 0;
            var redRank = 0;
            
            List<SpawnListInfo> listBlueMonster = null;
            List<SpawnListInfo> listRedMonster = null;

            if (CustomNetworkManager.Instance.GetRoom() != null)
            {
                var userSnapshot1 = ScoreManager.Instance.ScoreSnapshot.FirstOrDefault(x =>
                    x.ScoreUser == player1.address_wallet && x.ScoreType == ScoreController.PERSONAL_PVP_SNAPSHOT &&
                    x.FromDate == CycleManager.Instance.CurrentCycle.CycleStartTime &&
                    x.ToDate == CycleManager.Instance.CurrentCycle.CycleEndTime);
                if (userSnapshot1 != null)
                {
                    scoreBlue = (int)userSnapshot1.ScoreValue;
                    blueRank = userSnapshot1.Ranking;
                }
                
                var userSnapshot2 = ScoreManager.Instance.ScoreSnapshot.FirstOrDefault(x =>
                    x.ScoreUser == player2.address_wallet && x.ScoreType == ScoreController.PERSONAL_PVP_SNAPSHOT &&
                    x.FromDate == CycleManager.Instance.CurrentCycle.CycleStartTime &&
                    x.ToDate == CycleManager.Instance.CurrentCycle.CycleEndTime);
                if (userSnapshot2 != null)
                {
                    scoreRed = (int)userSnapshot2.ScoreValue;
                    redRank = userSnapshot2.Ranking;
                }

                switch (CustomGameManager.Instance.PlayerSide)
                {

                    case GameTeam.Blue:
                    {
                        var overallA = CustomGameManager.Instance.GetTotalValueFromFriendlySpawnListInfo();
                        var overallB = CustomGameManager.Instance.GetTotalValueFromEnemySpawnListInfo();
                        listBlueMonster = CustomGameManager.Instance.FriendlySpawnListInfo;
                        listRedMonster = CustomGameManager.Instance.EnemySpawnListInfos;
                        blueOverall = Mathf.FloorToInt(overallA).ToString();
                        redOverall = Mathf.FloorToInt(overallB).ToString();
                        CustomPvpModeAI.Instance.BlueScore = scoreBlue;
                        CustomPvpModeAI.Instance.BlueRank = blueRank;
                        
                        CustomPvpModeAI.Instance.RedScore = scoreRed;
                        CustomPvpModeAI.Instance.RedRank = redRank;
                        break;
                    }
                    case GameTeam.Red:
                    {
                        var overallA = CustomGameManager.Instance.GetTotalValueFromFriendlySpawnListInfo();
                        var overallB = CustomGameManager.Instance.GetTotalValueFromEnemySpawnListInfo();
                        listBlueMonster = CustomGameManager.Instance.EnemySpawnListInfos;
                        listRedMonster = CustomGameManager.Instance.FriendlySpawnListInfo;
                        blueOverall = Mathf.FloorToInt(overallB).ToString();
                        redOverall = Mathf.FloorToInt(overallA).ToString();
                        CustomPvpModeAI.Instance.BlueScore = scoreRed;
                        CustomPvpModeAI.Instance.BlueRank = redRank;
                        
                        CustomPvpModeAI.Instance.RedScore = scoreBlue;
                        CustomPvpModeAI.Instance.RedRank = blueRank;
                        break;
                    }
                }
            }
            else
            {
                var overallA = CustomGameManager.Instance.GetTotalValueFromFriendlySpawnListInfo();
                var overallB = CustomGameManager.Instance.GetTotalValueFromEnemySpawnListInfo();
                listBlueMonster = CustomGameManager.Instance.FriendlySpawnListInfo;
                listRedMonster = CustomGameManager.Instance.EnemySpawnListInfos;
                blueOverall = Mathf.FloorToInt(overallA).ToString();
                redOverall = Mathf.FloorToInt(overallB).ToString();

                var userSnapshot = ScoreManager.Instance.ScoreSnapshot.FirstOrDefault(x =>
                    x.ScoreUser == player1.address_wallet && x.ScoreType == ScoreController.PERSONAL_PVP_SNAPSHOT &&
                    x.FromDate == CycleManager.Instance.CurrentCycle.CycleStartTime &&
                    x.ToDate == CycleManager.Instance.CurrentCycle.CycleEndTime);
                
                if (userSnapshot != null)
                {
                    scoreBlue = (int)userSnapshot.ScoreValue;
                    blueRank = userSnapshot.Ranking;
                    
                    CustomPvpModeAI.Instance.BlueScore = scoreBlue;
                    CustomPvpModeAI.Instance.BlueRank = blueRank;
                }
                 
                scoreRed = CustomPvpModeAI.Instance.RedScore;
            }

            if (player2 != null)
            {
                // Gọi hàm UpdateStartPlayerInfo với dữ liệu đã lấy
                UpdateStartPlayerInfo(player1, listBlueMonster, blueOverall, scoreBlue,
                    player1.playerRanksEnum.ToString(), player2, listRedMonster, scoreRed, redOverall,
                    player2.playerRanksEnum.ToString());
            }
            else
            {
                UpdateStartPlayerInfo(player1, listBlueMonster, blueOverall, scoreBlue,
                    player1.playerRanksEnum.ToString(), null, listRedMonster, scoreRed, redOverall,
                    player1.playerRanksEnum.ToString());
            }

            playerBlue.PlayerName.text = player1.user_name;
            playerBlue.PlayerIcon.sprite = player1.userIcon;
            playerBlue.PlayerRank.text = player1.playerRanksEnum.ToString();
            playerBlue.PlayerRankScore.text = scoreBlue.ToString();
            playerBlue.PlayerOverallRank.text = blueOverall;
            if (player2 == null)
            {
                playerRed.PlayerIcon.sprite = CustomGameManager.PvePlayerScriptableObject.PlayerAvatar;
                playerRed.PlayerName.text = CustomGameManager.PvePlayerScriptableObject.PlayerName;
                playerRed.PlayerRank.text = player1.playerRanksEnum.ToString();
            }
            else
            {
                playerRed.PlayerName.text = player2.user_name;
                playerRed.PlayerIcon.sprite = player2.userIcon;
                playerRed.PlayerRank.text = player2.playerRanksEnum.ToString();
            }

            playerRed.PlayerRankScore.text = scoreRed.ToString();
            playerRed.PlayerOverallRank.text = redOverall;

            playerBlue.GetComponent<Button>().onClick.AddListener(() =>
            {
                PlayerProfileManager.Instance.PlayerProfileMenu.SetDataPlayerInBattle(player1);
            });

            if (player2 == null)
            {
                playerRed.GetComponent<Button>().onClick.RemoveAllListeners();
            }
            else
            {
                playerRed.GetComponent<Button>().onClick.AddListener(() =>
                {
                    PlayerProfileManager.Instance.PlayerProfileMenu.SetDataPlayerInBattle(player2);
                });
            }
        }

        public void OpenUiInfo()
        {
            playerRed.gameObject.SetActive(true);
            playerBlue.gameObject.SetActive(true);
        }
        private void UpdateStartPlayerInfo(SerializeUserData userDataBlue, List<SpawnListInfo> monsterBlue, string blueOverall, int scoreBlue , string blueRank, SerializeUserData userDataRed, List<SpawnListInfo> monsterRed, int scoreRed, string redOverall  ,string redRank)
        {
            placementControl.GameStartUI.BlueInfo.SetupPlayer(userDataBlue, monsterBlue,blueRank ,scoreBlue, blueOverall);
            placementControl.GameStartUI.RedInfo.SetupPlayer(userDataRed, monsterRed,redRank ,scoreRed, redOverall);
        }

        private void ForcePlacementMonster(string message)
        {
            placementControl.ForcePlaceMonster();
            placementControl.SetPlayerReady();
        }

        private void StartPlacementPhase(string message)
        {
            CustomGameManager.Instance.StartPlacementPhase();
        }

        private async void ForceEndTurn(string message)
        {
            try
            {
                SetNextTurn();
                var currentRoom = CustomNetworkManager.Instance.GetRoom();
                if (currentRoom != null)
                {
                    await currentRoom.Send("requestNewClock", new { content = GameDataManager.Instance.LoadedPlayerData.PlayerId });
                }
                else
                {
                    Debug.LogWarning("Not connected to any room!");
                }
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private void SetNextTurn()
        {
            CustomGameManager.Instance.SetNextTurn();
        }
        private void OnMonsterAbility(PlayerAction playerAction)
        {
            var selectedUnit = GameManager.GetRules().GetSelectedUnit();
            selectedUnit.SetCurrentState(UnitState.UsingAbility);
            var targetCell = CustomGameManager.Instance.GetCellByIndex(playerAction.position.x, playerAction.position.y);
            if (playerAction.isBlowSkill)
            {
                selectedUnit.SetupAbility(selectedUnit.GameMonster.MonsterInfo.MonsterScriptableData.GeneralSkill.SkillLogic);
            }
            else
            {
                if (selectedUnit.GameMonster.InGameParameter.StealSkillInfo != null && selectedUnit.GameMonster.InGameParameter.StealSkillInfo.StealSkillDataScriptable != null && selectedUnit.GameMonster.InGameParameter.StealSkillInfo.StealSkillDataScriptable.SkillLogic != null)
                {
                    if (selectedUnit.GameMonster.InGameParameter.StealSkillInfo.StealSkillDataScriptable.SkillLogic.name == playerAction.unitAbility)
                    {
                        selectedUnit.SetupAbility(selectedUnit.GameMonster.InGameParameter.StealSkillInfo.StealSkillDataScriptable.SkillLogic);
                    }
                    else
                    {
                        foreach (var data in selectedUnit.GetUnitData().m_Abilities)
                        {
                            if (data.unitAbility.name != playerAction.unitAbility) continue;
                            selectedUnit.SetupAbility(data.unitAbility);
                            break;
                        }
                    }
                }
                else
                {
                    foreach (var data in selectedUnit.GetUnitData().m_Abilities)
                    {
                        if (data.unitAbility.name != playerAction.unitAbility) continue;
                        selectedUnit.SetupAbility(data.unitAbility);
                        break;
                    }
                }
            }

            GameManager.Get().SetCurrentHoverCell(targetCell);
            GameManager.Get().UpdateHoverCells();
            GameManager.GetRules().HandleCellSelected(targetCell, playerAction);
            
        }

        private void OnMonsterMove(MoveMessage moveMessage)
        {
            var listMonster = CustomGameManager.Instance.GetListUnitCanUsed();
            foreach (var monster in listMonster.Where(monster => monster.GameMonster.MonsterInfo.MonsterId == moveMessage.monsterId))
            {
                monster.SetCurrentState(UnitState.Moving);
                var spawnCell = CustomGameManager.Instance.GetCellByIndex(moveMessage.position.x, moveMessage.position.y);
                GameManager.GetRules().HandleCellSelected(spawnCell);
                onCompleteMoveAway?.Invoke();
                onCompleteMoveAway?.RemoveAllListeners();
                break;
            }
        }
 
        private void OnMonsterEndTurn(string playerId)
        {
           GameManager.RemoveActionBeingPerformed();
           onCompleteAbility?.Invoke();
           onCompleteAbility?.RemoveAllListeners();
           SetNextTurn();
        }

        private const float ActionCost = 10;

        private void OnMonsterDefense(string playerId)
        {
            var selectedUnit = GameManager.GetRules().GetSelectedUnit();

            if (!selectedUnit) return;
            selectedUnit.GameMonster.InGameParameter.BuffCombineValue.BasicParameterAddValue.AddAgility(0.2f);
            selectedUnit.GameMonster.InGameParameter.BuffCombineValue.BasicParameterAddValue.AddVitality(0.2f);

            selectedUnit.GameMonster.InGameParameter.AddStamina(-ActionCost);

            SetNextTurn();

            // UIAccuracyRate.Instance.SetActive(false);
        }

        private Tween _countdownTween;

        public void KillCountDownTween()
        {
            if (_countdownTween != null && _countdownTween.IsActive())
            {
                _countdownTween.Kill();
            }
        }

        private void OnCountDown(float countdownValue)
        {
            if (_countdownTween != null && _countdownTween.IsActive())
            {
                _countdownTween.Kill();
            }

            countDownSlider.value = countdownValue;

            _countdownTween = countDownSlider.DOValue(0, countdownValue)
                .SetEase(Ease.Linear)
                .SetUpdate(UpdateType.Normal, true) // Đảm bảo tween không bị ảnh hưởng bởi Time.timeScale
                .OnUpdate(() =>
                {
                    var currentTime = countDownSlider.value; // Tính lại thời gian hiện tại

                    // Calculate minutes and seconds
                    var minutes = Mathf.FloorToInt(currentTime / 60);
                    var seconds = Mathf.FloorToInt(currentTime % 60);

                    var timeText = LocalizationManager.CurrentLanguageCode == "ja" ? "残り時間..." : "Remaining...";
                    countDownText.text = timeText + $"{minutes:0}:{seconds:00}";
                });
        }

        public async UniTask OnGameEndMessage(Dictionary<string, object> options)
        {
            try
            {
                CustomNetworkManager.Instance.ClearReconnectionToken();
                var currentRoom = CustomNetworkManager.Instance.GetRoom();
                if (currentRoom != null && CustomNetworkManager.Instance.IsSpectator() == false)
                {
                    await currentRoom.Send("game_end", options);
                    Debug.Log(CustomNetworkManager.Instance.previousGameMode);
                }
                else
                {
                    Debug.LogWarning("Not connected to any room!");
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning(e);
            }
        }

        public async UniTask OnSurrenderMessage()
        {
            if (CustomNetworkManager.Instance.GetRoom() == null) return;
            var currentRoom = CustomNetworkManager.Instance.GetRoom();
            if (currentRoom != null && CustomNetworkManager.Instance.IsSpectator() == false)
            {
                await currentRoom.Send("player_sur",
                    new { content = GameDataManager.Instance.LoadedPlayerData.PlayerId });
                Debug.Log("Player Surrender: " + GameDataManager.Instance.LoadedPlayerData.PlayerId);
            }
            else
            {
                Debug.LogWarning("Not connected to any room!");
            }
        }

        private Dictionary<string, object> StoreBuffs(GridUnit unit)
        {
            return unit.GameMonster.InGameParameter.CurrentBuffs.ToDictionary<SerializeIngameBuff, string, object>(
                buff => buff.BuffType.ToString(),
                buff => new Dictionary<string, object>
                    { { "value", buff.EffectValue }, { "duration", buff.BuffTime } });
        }

        private Dictionary<string, object> StoreDeBuffs(GridUnit unit)
        {
            return unit.GameMonster.InGameParameter.CurrentDebuffs.ToDictionary<SerializeIngameDebuff, string, object>(
                deBuff => deBuff.DebuffType.ToString(),
                deBuff => new Dictionary<string, object>
                    { { "value", deBuff.EffectValue }, { "duration", deBuff.DebuffTime } });
        }

        private List<Dictionary<string, object>> GetMonsterData()
        {
            return CustomGameManager.Instance.CurrentTurnList.Select(monster => new Dictionary<string, object>
                {
                    { "monsterId", monster.GameMonster.MonsterInfo.MonsterId },
                    { "isDead", monster.IsDead },
                    { "currentStaminaRecover", monster.GetCurrentSt() },
                    { "currentSpeed", monster.MoveSpeed },
                    { "currentMoveLimit", monster.GameMonster.InGameParameter.CurrentMoveLimit },
                    {
                        "position",
                        new Dictionary<string, float>
                            { { "x", monster.GetCell().GetIndex().x }, { "y", monster.GetCell().GetIndex().y } }
                    },
                    { "currentHealthPercent", monster.GameMonster.InGameParameter.HealthPercentageLeft },
                    {
                        "basicParameter", new Dictionary<string, object>
                        {
                            { "health", monster.GameMonster.BattleParameter.Health },
                            { "strength", monster.GameMonster.BattleParameter.Strength },
                            { "intelligent", monster.GameMonster.BattleParameter.Intelligent },
                            { "dexterity", monster.GameMonster.BattleParameter.Dexterity },
                            { "agility", monster.GameMonster.BattleParameter.Agility },
                            { "vitality", monster.GameMonster.BattleParameter.Vitality }
                        }
                    },
                    { "buffs", StoreBuffs(monster) },
                    { "debuffs", StoreDeBuffs(monster) }
                })
                .ToList();
        }

        public async UniTask OnUpdateTurnMessage()
        {
            if (CustomNetworkManager.Instance.GetRoom() != null && CustomNetworkManager.Instance.IsSpectator() == false)
            {
                var customGameRules = GameManager.GetRules() as CustomGameRules;
                var currentRoom = CustomNetworkManager.Instance.GetRoom();
                var currentMonsterTurnList = CustomGameManager.Instance.CurrentTurnList
                    .Select(unit => unit.GameMonster.MonsterInfo.MonsterId).ToList();
                var nextMonsterTurnList = CustomGameManager.Instance.NextTurnList
                    .Select(unit => unit.GameMonster.MonsterInfo.MonsterId).ToList();

                if (currentRoom != null && customGameRules)
                {
                    await currentRoom.Send("sync_turnData", new
                    {
                        turnCount = customGameRules.GetCurrentTurn(),
                        currentSelectedMonster = GameManager.GetRules().GetSelectedUnit().GameMonster.MonsterInfo.MonsterId,
                        currentMonsterTurn = currentMonsterTurnList,
                        nextMonsterTurn = nextMonsterTurnList,
                        monsterData = GetMonsterData()
                    });
                }
                else
                {
                    Debug.LogWarning("Not connected to any room!");
                }
            }
        }


    }

    [Serializable]
    public class MoveMessage
    {
        public string monsterId;
        public string playerId;
        public Position position;
        public MoveMessage() { }
        public MoveMessage(string monsterId, string playerId, Position position)
        {
            this.playerId = playerId;
            this.monsterId = monsterId;
            this.position = position;
        }
    }

    [Serializable]
    public class Position
    {
        public float x;
        public float y;
        public Position() { }
        public Position(float x, float y)
        {
            this.x = x;
            this.y = y;
        }
    }

    [Serializable]
    public class PlayerAction
    {
        public string unitAbility;
        public string playerId;
        public Position position;
        public List<MonsterHit> monsterHits = new();
        public List<BuffMessage> buffMessages = new();
        public List<DeBuffMessage> deBuffMessages = new();
        public float skillDelay;
        public float skillStamina;
        public bool isBlowSkill;
        public bool isTrapSkill;
        public string monsterReviveId;
    }

    [Serializable]
    public class MonsterHit
    {
        public string monsterId;
        public bool isHit;
        public bool isCritical;
        public float skillDelayDamage;
        public float hpDamage;
        public float stDamage;
    }

    [Serializable]
    public class BuffMessage
    {
        public string monsterId;
        public List<string> listBuff = new();
    }
    [Serializable]
    public class DeBuffMessage
    {
        public string monsterId;
        public List<DeBuffHit> listDeBuff = new();
    }

    [Serializable]
    public class DeBuffHit
    {
        public string deBuffId;
        public bool isHit;
    }
}