using System;
using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.Controllers;
using Assets.Scripts.DataStruct;
using Assets.Scripts.Helper;
using Assets.Scripts.Managers;
using Assets.Scripts.UI;
using Assets.Scripts.UI.BattleUI.PvP;
using Best.HTTP;
using Best.HTTP.Request.Authenticators;
using Best.HTTP.Request.Upload;
using Colyseus;
using Cysharp.Threading.Tasks;
using I2.Loc;
using Network.RoomState;
using UI.Utilities;
using UnityEngine;

namespace Network
{
    public class CustomNetworkManager : ColyseusManager<CustomNetworkManager>
    {
        private static ColyseusRoom<GameState> _room;
        private static ColyseusClient _client;

        // Dictionary to manage multiple tournament rooms concurrently
        private static readonly Dictionary<string, ColyseusRoom<GameState>> _tournamentRooms = new();
        private static readonly object _roomLock = new object();

        public bool isLocal;
        public bool IsReconnected { get; private set; }

        public delegate void OnRoomsReceived(RoomAvailable[] rooms);

        public static OnRoomsReceived onRoomsReceived;

        public PvpGameMode previousGameMode = PvpGameMode.None;
        private List<RankedMatchHistory> _rankedMatchHistories = new();

        public int MonsterNum { get; set; }

        public string MonsterRank { get; set; }

        public delegate void OnGameStateChange();

        public static OnGameStateChange onGameStateChange;

        public ColyseusRoom<GameState> GetRoom()
        {
            return _room;
        }

        public void SetRoomToNull()
        {
            _room = null;
        }

        public GameState GetGameState()
        {
            return _room.State;
        }

        // Tournament room management methods
        public ColyseusRoom<GameState> GetTournamentRoom(string roomId)
        {
            lock (_roomLock)
            {
                return _tournamentRooms.TryGetValue(roomId, out var room) ? room : null;
            }
        }

        public void SetTournamentRoom(string roomId, ColyseusRoom<GameState> room)
        {
            lock (_roomLock)
            {
                _tournamentRooms[roomId] = room;
                Debug.Log($"[Tournament Room] Set room {roomId} in dictionary. Total rooms: {_tournamentRooms.Count}");
            }
        }

        public void RemoveTournamentRoom(string roomId)
        {
            lock (_roomLock)
            {
                if (_tournamentRooms.Remove(roomId))
                {
                    Debug.Log($"[Tournament Room] Removed room {roomId} from dictionary. Remaining rooms: {_tournamentRooms.Count}");
                }
            }
        }

        public int partyReferenceId;

        private void Initialize()
        {
            _client = isLocal
                ? new ColyseusClient("ws://localhost:2567")
                : new ColyseusClient(BackendLoadData.Instance.ServerInfo.LobbyUrl);
            _client.Auth.Token = BackendLoadData.Instance.token;
        }

        // Start is called before the first frame update
        protected override void Start()
        {
            base.Start();
            Initialize();
            _room = null;
            // GetAvailableRooms();
            // await JoinOrCreateGame();
        }

        protected override async void OnDestroy()
        {
            try
            {
                await CleanupAllTournamentRooms();
                await LeaveRoom(false);
                base.OnDestroy();
            }
            catch (Exception e)
            {
                Debug.LogError(e.Message);
            }
        }

        protected override async void OnApplicationQuit()
        {
            try
            {
                await CleanupAllTournamentRooms();
                await LeaveRoom(false);
                base.OnApplicationQuit();
            }
            catch (Exception e)
            {
                Debug.Log(e);
            }
        }

        public async UniTask<bool> CheckIfMatch()
        {
            if (PlayerPrefs.GetString("wallet") != GameDataManager.Instance.LoadedPlayerData.PlayerWallet) return false;

            var reconnectionToken = GetReconnectionToken();
            if (reconnectionToken == null)
            {
                return false;
            }

            var rooms = await GetAvailableRoomsAsync();

            return rooms.Select(room => room.roomId).Contains(GetReconnectionToken().RoomId);
        }

        private void SetReconnected(bool isReconnected)
        {
            IsReconnected = isReconnected;
        }

        private async UniTask<List<RoomAvailable>> GetAvailableRoomsAsync()
        {
            try
            {
                List<RoomAvailable> rooms = new();
                rooms.AddRange(await _client.GetAvailableRooms<RoomAvailable>("free_battle"));
                rooms.AddRange(await _client.GetAvailableRooms<RoomAvailable>("ranked_battle"));
                rooms.AddRange(await _client.GetAvailableRooms<RoomAvailable>("prized_battle"));
                rooms.AddRange(await _client.GetAvailableRooms<RoomAvailable>("tournament_match"));
                return rooms;
            }
            catch (Exception e)
            {
                Debug.LogError(e.Message);
                return null;
            }
        }

        public async void GetAvailableRooms()
        {
            try
            {
                var rooms = await _client.GetAvailableRooms<RoomAvailable>("free_battle");

                var filteredRooms = rooms.Where(room => room.metadata.hostId != GameDataManager.Instance.LoadedPlayerData.PlayerId).ToArray();

                onRoomsReceived?.Invoke(filteredRooms);
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }
        public async UniTask<List<RoomAvailable>> GetAvailableRankedRoom()
        {
            try
            {
                var rooms = await _client.GetAvailableRooms<RoomAvailable>("ranked_battle");
                return rooms.ToList();
            }
            catch (Exception e)
            {
               Debug.LogException(e);
                throw;
            }
        }

        public async UniTask<List<RoomAvailable>> GetAvailablePrizedRoom()
        {
            try
            {
                var rooms = await _client.GetAvailableRooms<RoomAvailable>("prized_battle");
                return rooms.ToList();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                throw;
            }
        }

        public async UniTask CreateFreeBattleRoom(Dictionary<string, object> options, Action<bool> onComplete = null)
        {
            var loading = BackendLoadData.Instance.LoadingCanvas("CreateFreeBattleRoom");
            try
            {
                Debug.Log("[Creating Room]");
                if (_room == null)
                {
                    options.Add("playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId);
                    options.Add("playerName", GameDataManager.Instance.LoadedPlayerData.PlayerName);
                    options.Add("token", BackendLoadData.Instance.token);
                    options.Add("walletId", GameDataManager.Instance.LoadedPlayerData.PlayerWallet);
                    _room = await _client.Create<GameState>("free_battle", options);
                    _room.OnJoin += OnRoomJoined;
                    _room.OnStateChange += HandleStateChange;
                }

                Destroy(loading);
            }
            catch (Exception e)
            {
                onComplete?.Invoke(false);
                Debug.LogError($"Error creating room: {e.Message}");
                Destroy(loading);
            }

            onComplete?.Invoke(true);
        }
        
        public async UniTask CreateRankedBattleRoom(Dictionary<string, object> options, Action<bool> onComplete = null)
        {
            try
            {
                Debug.Log("[Creating Ranked Battle Room]");
                if (_room == null)
                {
                    options.Add("playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId);
                    options.Add("playerName", GameDataManager.Instance.LoadedPlayerData.PlayerName);
                    options.Add("token", BackendLoadData.Instance.token);
                    options.Add("walletId", GameDataManager.Instance.LoadedPlayerData.PlayerWallet);
                    _room = await _client.Create<GameState>("ranked_battle", options);
                    _room.OnJoin += OnRoomJoined;
                    _room.OnStateChange += HandleStateChange;
                }
            }
            catch (Exception e)
            {
                onComplete?.Invoke(false);
                Debug.LogError($"Error creating room: {e.Message}");
            }

            onComplete?.Invoke(true);
        }

        public async UniTask<ColyseusRoom<GameState>> CreateTournamentRoom(Dictionary<string, object> options)
        {
            try
            {
                string roomName = options.TryGetValue("roomName", out var roomNameObj) ? roomNameObj.ToString() : "";
                Debug.Log($"[Create Tournament Room] Creating room: {roomName}");

                // Check if this specific tournament room already exists
                var existingRoom = GetTournamentRoom(roomName);
                if (existingRoom != null)
                {
                    Debug.Log($"[Create Tournament Room] Room {roomName} already exists, returning existing room");
                    return existingRoom;
                }

                options.Add("playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId);
                options.Add("playerName", GameDataManager.Instance.LoadedPlayerData.PlayerName);
                options.Add("token", BackendLoadData.Instance.token);
                options.Add("walletId", GameDataManager.Instance.LoadedPlayerData.PlayerWallet);

                var newRoom = await _client.Create<GameState>("tournament_match", options);
                newRoom.OnJoin += OnRoomJoined;
                newRoom.OnStateChange += HandleStateChange;

                // Store the tournament room with its specific ID
                SetTournamentRoom(roomName, newRoom);

                Debug.Log($"[Create Tournament Room] Successfully created room: {roomName}");
                return newRoom;
            }
            catch (Exception e)
            {
                Debug.LogError($"[Create Tournament Room] Error creating room\n{e.Message}");
                return null;
            }
        }

        public async UniTask<ColyseusRoom<GameState>> JoinTournamentRoom(string id)
        {
            try
            {
                Debug.Log($"[Join Tournament Room] Attempting to join room: {id}");

                // Check if we already have this tournament room
                var existingRoom = GetTournamentRoom(id);
                if (existingRoom != null)
                {
                    Debug.Log($"[Join Tournament Room] Room {id} already exists locally, returning existing room");
                    return existingRoom;
                }

                Dictionary<string, object> options = new()
                {
                    { "playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId },
                    { "playerName", GameDataManager.Instance.LoadedPlayerData.PlayerName },
                    { "token", BackendLoadData.Instance.token },
                    { "walletId", GameDataManager.Instance.LoadedPlayerData.PlayerWallet }
                };

                var joinedRoom = await _client.JoinById<GameState>(id, options);
                joinedRoom.OnJoin += OnRoomJoined;
                joinedRoom.OnStateChange += HandleStateChange;

                // Store the joined tournament room
                SetTournamentRoom(id, joinedRoom);

                Debug.Log($"[Join Tournament Room] Successfully joined room: {id}");
                return joinedRoom;
            }
            catch (Exception e)
            {
                Debug.LogError($"[Join Tournament Room] Error joining room {id}: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// Leave and clean up a specific tournament room
        /// </summary>
        /// <param name="roomId">The room ID to clean up</param>
        public async UniTask LeaveTournamentRoom(string roomId)
        {
            try
            {
                var room = GetTournamentRoom(roomId);
                if (room != null)
                {
                    Debug.Log($"[Leave Tournament Room] Leaving room: {roomId}");
                    await room.Leave();
                    RemoveTournamentRoom(roomId);
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[Leave Tournament Room] Error leaving room {roomId}: {e.Message}");
                // Still remove from dictionary even if leave fails
                RemoveTournamentRoom(roomId);
            }
        }

        /// <summary>
        /// Clean up all tournament rooms
        /// </summary>
        public async UniTask CleanupAllTournamentRooms()
        {
            try
            {
                List<string> roomIds;
                lock (_roomLock)
                {
                    roomIds = new List<string>(_tournamentRooms.Keys);
                }

                foreach (var roomId in roomIds)
                {
                    await LeaveTournamentRoom(roomId);
                }

                Debug.Log("[Cleanup Tournament Rooms] All tournament rooms cleaned up");
            }
            catch (Exception e)
            {
                Debug.LogError($"[Cleanup Tournament Rooms] Error during cleanup: {e.Message}");
            }
        }

        /// <summary>
        /// Get the count of active tournament rooms
        /// </summary>
        public int GetTournamentRoomCount()
        {
            lock (_roomLock)
            {
                return _tournamentRooms.Count;
            }
        }

        /// <summary>
        /// Check if a specific tournament room exists
        /// </summary>
        public bool HasTournamentRoom(string roomId)
        {
            lock (_roomLock)
            {
                return _tournamentRooms.ContainsKey(roomId);
            }
        }

        /// <summary>
        /// ConnectToRoomAsync
        /// </summary>
        /// <param name="roomId"></param>
        /// <param name="options"></param>
        /// <returns>success, errorMessage, roomId</returns>
        public async UniTask<(bool, string, string)> ConnectToRoomAsync(string roomId, Dictionary<string, object> options)
        {
            try
            {
                // Check if this is a tournament room first
                var existingTournamentRoom = GetTournamentRoom(roomId);
                if (existingTournamentRoom != null)
                {
                    Debug.Log($"[ConnectToRoomAsync] Tournament room {roomId} already exists");
                    return (true, "Successfully connected to existing tournament room.", roomId);
                }

                // Join the room by ID
                var joinedRoom = await _client.JoinById<GameState>(roomId, options);
                Debug.Log($"[ConnectToRoomAsync] Successfully connected to room: {roomId}");
                joinedRoom.OnJoin += OnRoomJoined;
                joinedRoom.OnStateChange += HandleStateChange;

                // Check if this looks like a tournament room (contains underscore pattern)
                if (roomId.Contains("_"))
                {
                    // Store as tournament room
                    SetTournamentRoom(roomId, joinedRoom);
                    Debug.Log($"[ConnectToRoomAsync] Stored as tournament room: {roomId}");
                }
                else
                {
                    // Store as regular room
                    _room = joinedRoom;
                }

                return (true, "Successfully connected to room.", roomId);
            }
            catch (Exception ex)
            {
                var errorMessage = "";

                if (ex.Message.Contains("Invalid password"))
                {
                    errorMessage = "The password you entered is incorrect.";
                }
                else if (ex.Message.Contains("Enter password field"))
                {
                    errorMessage = "Please Enter Room Password";
                }
                else if (ex.Message.Contains("not found"))
                {
                    errorMessage = LocalizationManager.CurrentLanguageCode switch
                    {
                        "ja" => "検索しているルームは見つかりませんでした。",
                        _ => "The Room you are searching for could not be found."
                    }; 
                    UIPopupNotify.Instance.SetNotify("Room Found", errorMessage, () => UIPopupPanel.Instance.CloseMenu());
                    UIPopupNotify.Instance.OpenMenu();
                }

                Debug.LogWarning("Failed to connect to room: " + roomId + " | Error: " + ex.Message);
                return (false, errorMessage, roomId);
            }
        }

        public bool IsSpectator()
        {
            return _room.State.playerRoles.firstPlayerId != GameDataManager.Instance.LoadedPlayerData.PlayerId &&
                   _room.State.playerRoles.secondPlayerId != GameDataManager.Instance.LoadedPlayerData.PlayerId;
        }
        public bool CheckInBattleAndReady()
        {
            return _room.State.isInBattle &&
                   _room.State.players[0].state is "ReadyToPlay" or "Disconnected" &&
                   _room.State.players[1].state is "ReadyToPlay" or "Disconnected";
        }

        private void HandleStateChange(GameState state, bool isFirstState)
        {
            if (state.metadata.hostId != null)
            {
                onGameStateChange?.Invoke();
            }
        }

        public async UniTask LeaveRoom(bool consented = true)
        {
            if (_room != null)
            {
                try
                {
                    SetReconnected(false);
                    await _room.Leave(consented);
                    _room.OnStateChange -= HandleStateChange;
                    _room = null;
                    Debug.Log("Left the room successfully.");
                }
                catch (Exception e)
                {
                    Debug.LogError("Error leaving the room: " + e.Message);
                }
            }
            else
            {
                Debug.Log("No room to leave.");
            }
        }
    
        private void OnRoomJoined()
        {
            // Now you can safely interact with the room and its state
            Debug.Log($"Successfully joined the room! Host ID: {_room.State.metadata.hostId}. Room ID: {_room.RoomId}");
        }
        
        public async UniTask GetRankedMatchHistory()
        {
            var stringUrl =
                $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.GetRankedMatchHistoryByWalletId}";
            var request = HTTPRequest.CreatePost(stringUrl);
            var requestBody = new Dictionary<string, object>
                { { "wallet", GameDataManager.Instance.LoadedPlayerData.PlayerWallet } };
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);

            try
            {
                var response = await request.GetFromJsonResultAsync<UserHistory>();
                if (response.success)
                {
                    _rankedMatchHistories.Clear();
                    _rankedMatchHistories = response.data.ranked_match_history;
                }
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        public async UniTask PostRankedMatchHistory(string walletA, float overallA,
            List<string> monstersA, float scoreA, int streakA, string walletB, float overallB, List<string> monstersB,
            float scoreB, int streakB, int battleType, string battleRank, string winnerUser)
        {
            var stringUrl = $"{BackendLoadData.Instance.ServerInfo.BackendUrl}{BackendLoadData.Instance.ServerInfo.CreateRankedMatchHistory}";
            var request = HTTPRequest.CreatePost(stringUrl);

            var matchTime = ((DateTimeOffset)DateTime.UtcNow).ToUnixTimeSeconds();

            // Prepare the request body
            var requestBody = new Dictionary<string, object>
            {
                {
                    "ranked_match_history", new Dictionary<string, object>
                    {
                        { "match_time", matchTime },
                        { "user_wallet_a", walletA },
                        { "user_wallet_a_overall", overallA },
                        { "user_a_monsters", monstersA },
                        { "user_a_score", scoreA },
                        { "user_a_streak", streakA },
                        { "user_wallet_b", walletB },
                        { "user_wallet_b_overall", overallB },
                        { "user_b_monsters", monstersB },
                        { "user_b_score", scoreB },
                        { "user_b_streak", streakB },
                        { "battle_type", battleType }, // Fixing typo from "batte_type"
                        { "battle_rank", battleRank },
                        { "winner_user", winnerUser }
                    }
                }
            };

            // Set the authorization header and upload JSON data
            request.Authenticator = new BearerTokenAuthenticator(BackendLoadData.Instance.token);
            request.UploadSettings.UploadStream = new JSonDataStream<Dictionary<string, object>>(requestBody);

            try
            {
                // Await response and handle result
                var response = await request.GetFromJsonResultAsync<ServerResponse>();
                if (response.success)
                {
                    Debug.Log("Success: " + response);
                }
                else
                {
                    Debug.Log("Add score failed " + response.message);
                }
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        // search the RankedMatchHistorries for matches with battle_type = 1
        public List<RankedMatchHistory> GetRankedMatchHistoryByBattleType(int battleType)
        {
            return _rankedMatchHistories.FindAll(x => x.battle_type == battleType);
        }

        // calulate the win streak of the player from GetRankedMatchHistoryByBattleType(1)
        public int GetWinStreakRank()
        {
            var winStreak = 0;
            for (var i = GetRankedMatchHistoryByBattleType(1).Count - 1; i >= 0; i--)
            {
                if (GetRankedMatchHistoryByBattleType(1)[i].winner_user == GameDataManager.Instance.LoadedPlayerData.PlayerWallet)
                {
                    winStreak++;
                }
                else
                {
                    break;
                }
            }

            return winStreak;
        }

        public int GetPrizeBattleWin()
        {
            var winStreak = 0;
            for (var i = GetRankedMatchHistoryByBattleType(2).Count - 1; i >= 0; i--)
            {
                if (GetRankedMatchHistoryByBattleType(2)[i].winner_user ==
                    GameDataManager.Instance.LoadedPlayerData.PlayerWallet)
                {
                    winStreak++;
                }
                else
                {
                    break;
                }
            }

            return winStreak;
        }

        public int GetPrizePreviousWinCount()
        {
            var previewWinStreak = 0;
            var matchHistory = GetRankedMatchHistoryByBattleType(2);

            for (var i = matchHistory.Count - 1; i >= 0; i--)
            {
                var match = matchHistory[i];
                
                if (match.winner_user == GameDataManager.Instance.LoadedPlayerData.PlayerWallet)
                {
                    previewWinStreak++;
                }
                else if (match.winner_user == "used_ticket")
                {
                    break;
                }
            }

            return previewWinStreak;
        }

        // calculte total win of the player
        public int GetTotalWin()
        {
            return GetRankedMatchHistoryByBattleType(1)
                .FindAll(x => x.winner_user == GameDataManager.Instance.LoadedPlayerData.PlayerWallet
                && (x.user_wallet_a == GameDataManager.Instance.LoadedPlayerData.PlayerWallet
                || x.user_wallet_b == GameDataManager.Instance.LoadedPlayerData.PlayerWallet)).Count;
        }

        // get total lose
        public int GetTotalLose()
        {
            return GetRankedMatchHistoryByBattleType(1)
                .FindAll(x => x.winner_user != GameDataManager.Instance.LoadedPlayerData.PlayerWallet
                && (x.user_wallet_a == GameDataManager.Instance.LoadedPlayerData.PlayerWallet
                || x.user_wallet_b == GameDataManager.Instance.LoadedPlayerData.PlayerWallet)).Count;
        }

        public object GetTotalWinRate()
        {
            var totalWins = GetTotalWin();
            var totalLosses = GetTotalLose();

            if (totalWins == 0 && totalLosses == 0)
            {
                return 0.00;
            }

            var winRate = (float)totalWins / (totalWins + totalLosses) * 100;

            return Math.Round(winRate, 1);
        }

        public int GetTotalWinBetweenDates(DateTime startDate, DateTime endDate)
        {
            return GetRankedMatchHistoryByBattleType(1).FindAll(x =>
                x.winner_user == GameDataManager.Instance.LoadedPlayerData.PlayerWallet && x.crt_dt >= startDate &&
                x.crt_dt <= endDate).Count;
        }

        public int GetTotalLossBetweenDates(DateTime startDate, DateTime endDate)
        {
            return GetRankedMatchHistoryByBattleType(1).FindAll(x =>
                x.winner_user != GameDataManager.Instance.LoadedPlayerData.PlayerWallet && x.crt_dt >= startDate &&
                x.crt_dt <= endDate).Count;
        }

        public object GetWinRate(int wins, int losses)
        {
            if (wins == 0 && losses == 0)
            {
                return 0.00;
            }

            var winRate = (float)wins / (wins + losses) * 100;

            return Math.Round(winRate, 1);
        }

        public async UniTask CreatePrizedBattleRoom(Dictionary<string, object> options, Action<bool> onComplete = null)
        {
            try
            {
                Debug.Log("[Creating Prized Battle Room]");
                if (_room == null)
                {
                    options.Add("playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId);
                    options.Add("playerName", GameDataManager.Instance.LoadedPlayerData.PlayerName);
                    options.Add("token", BackendLoadData.Instance.token);
                    options.Add("walletId", GameDataManager.Instance.LoadedPlayerData.PlayerWallet);
                    _room = await _client.Create<GameState>("prized_battle", options);
                    _room.OnJoin += OnRoomJoined;
                    _room.OnStateChange += HandleStateChange;
                }
            }
            catch (Exception e)
            {
                onComplete?.Invoke(false);
                Debug.LogError($"Error creating room: {e.Message}");
            }

            onComplete?.Invoke(true);
        }

        public string GetGuildId() => GuildDataController.GetGuildByUserWallet(GameDataManager.Instance.LoadedPlayerData.PlayerWallet)._guildId;

        public string GetSectorId() => SectorManager.Instance.GetSectorDataByWallet(GameDataManager.Instance.LoadedPlayerData.PlayerWallet)?.SectorId ?? "";

        public void ClearReconnectionToken()
        {
            // Debug.LogWarning($"Clear reconnectionToken");
            PlayerPrefs.DeleteKey("reconnectionToken");
            PlayerPrefs.DeleteKey("roomId");
            PlayerPrefs.DeleteKey("wallet");
            PlayerPrefs.Save();
        }
    
        public static void StoreReconnectionToken(ReconnectionToken reconnectionToken)
        {
            // Debug.LogWarning($"Reconnection {reconnectionToken.RoomId} {reconnectionToken.Token}");
            PlayerPrefs.SetString("reconnectionToken", reconnectionToken.Token);
            PlayerPrefs.SetString("wallet", GameDataManager.Instance.LoadedPlayerData.PlayerWallet);
            PlayerPrefs.SetString("roomId", reconnectionToken.RoomId);
            PlayerPrefs.Save();
        }

        public void StoreCurrentGameMode(string gameMode)
        {
            PlayerPrefs.SetString("gameMode", gameMode);
        }

        public string GetCurrentGameMode()
        {
            return PlayerPrefs.GetString("gameMode");
        }

        private static ReconnectionToken GetReconnectionToken()
        {
            var reconnectionToken = PlayerPrefs.GetString("reconnectionToken");
            var roomID = PlayerPrefs.GetString("roomId");
            if (reconnectionToken == null || roomID == null)
            {
                return null;
            }
            return new ReconnectionToken
            {
                RoomId = roomID,
                Token = reconnectionToken
            };
        }


        public async UniTask ReconnectNormal()
        {
            var options = new Dictionary<string, object>
            {
                { "playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId },
                { "token", BackendLoadData.Instance.token },
                { "password", PlayerPrefs.GetString("roomPassword") },
                { "walletId", GameDataManager.Instance.LoadedPlayerData.PlayerWallet }
            };
            try
            {
                await ConnectToRoomAsync(GetReconnectionToken().RoomId, options);
                if (_room != null)
                {
                    SetReconnected(true);
                    CustomPvpModeAI.Instance.BattleRank = _room.State.metadata.monsterRank;
                    SceneHelper.Instance.LoadScene(2, 1);
                }

                ClearReconnectionToken();
            }
            catch (Exception ex)
            {
                UIPopupNotify.Instance.SetNotify("Reconnect", $"Unknown Error.\n{ex.Message}", () => { });
                ClearReconnectionToken();
            }
        }

        public async UniTask OnDenyReconnect()
        {
            var options = new Dictionary<string, object>
            {
                { "playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId },
                { "token", BackendLoadData.Instance.token },
                { "password", PlayerPrefs.GetString("roomPassword") },
                { "walletId", GameDataManager.Instance.LoadedPlayerData.PlayerWallet }
            };
            try
            {
                await ConnectToRoomAsync(GetReconnectionToken().RoomId, options);
                if (_room != null)
                {
                    Debug.Log("Successfully connected to room: " + _room.RoomId);
                    await _room.Send("player_sur", new { content = GameDataManager.Instance.LoadedPlayerData.PlayerId });
                    await LeaveRoom();
                }
                ClearReconnectionToken();
            }
            catch (Exception ex)
            {
                UIPopupNotify.Instance.SetNotify("Reconnect", $"Unknown Error.\n{ex.Message}", () => { });
                ClearReconnectionToken();
            }
        }

    //     public async UniTask TryToReconnect()
    //     {
    //         try
    //         {
    //             // Assuming your client has a method like JoinRoom that accepts a roomId and options
    //             Debug.Log($"Reconnection {GetReconnectionToken().RoomId} {GetReconnectionToken().Token}");
    //             var options = new Dictionary<string, string>
    //             {
    //                 { "playerId", GameDataManager.Instance.LoadedPlayerData.PlayerId },
    //                 { "token", BackendLoadData.Instance.token },
    //                 { "walletId", GameDataManager.Instance.LoadedPlayerData.PlayerWallet }
    //             };
    //             _room = await _client.Reconnect<GameState>(GetReconnectionToken(), options);
    //             // await _room.Connect();
    //
    //             if (_room != null)
    //             {
    //                 Debug.Log("Successfully connected to room: " + _room.RoomId);
    //                 _room.OnJoin += OnRoomJoined;
    //                 _room.OnStateChange += HandleStateChange;
    //             }
    //         }
    //         catch (Exception ex)
    //         {
    //             string errorMessage = "";
    //
    //             Debug.LogError(ex);
    //             if (ex.Message.Contains("dispose"))
    //             {
    //                 errorMessage = "The Room you are trying to connect to is no longer available";
    //             }
    //             else if (ex.Message.Contains("destination host"))
    //             {
    //                 errorMessage = "The Room you are trying to connect to is no longer available";
    //             }
    //             else
    //             {
    //                 errorMessage = $"Unknown Error.\n{ex.Message}";
    //             }
    //
    //             UIPopupNotify.Instance.SetNotify("Reconnect", errorMessage, () => { });
    //         }
    //
    //         // StoreReconnectionToken("", "");
    //     }
    }

    public class UserHistory
    {
        public bool success { get; set; }
        public string message { get; set; }
        public Data data { get; set; }
        public int code { get; set; }
    }

    public class Data
    {
        public List<RankedMatchHistory> ranked_match_history { get; set; }
    }
}