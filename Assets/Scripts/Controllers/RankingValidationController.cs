using Assets.Scripts.DataStruct.Data;
using Assets.Scripts.DataStruct.Data.Cycle;
using Assets.Scripts.DataStruct.Data.Guild;
using Assets.Scripts.DataStruct.Data.Sector;
using Assets.Scripts.Managers;
using System;
using System.Collections.Generic;
using System.Linq;
using Thirdweb;

namespace Assets.Scripts.Controllers
{
    public class RankingValidationController
    {
        private static readonly int WEEKLY_REWARD_MILESTONE = 15;
        private static readonly int GUILD_REWARD_MILESTONE = 60;
        private static readonly int SECTOR_REWARD_MILESTONE = 180;
        private static readonly float MA_SCORE_REWARD_CONVERT = 10;//percent
        private static readonly float SCORE_REWARD_THRESHHOLD = 30;//percent
        private static readonly float FIRST_PLACE_SECTOR_REWARD = 45;//percent
        private static readonly float SECCOND_PLACE_SECTOR_REWARD = 33;//percent
        private static readonly float THIRD_PLACE_SECTOR_REWARD = 22;//percent

        private static bool IsPreviousWeek(int targetWeek, DateTime currentTime)
        {
            var currentWeek = Helpers.GetWeekOfTheYearCustom(currentTime);
            if (currentWeek == 1 && targetWeek == 48)
                return true;
            return targetWeek == currentWeek - 1;
        }

        public static bool IsPreviousCycle(int cycleId)
        {
            var cycleList = CycleManager.Instance.GameCycles.OrderBy(x => x.CycleStartTime).ToList();
            var currentCycleIndex = cycleList.FindIndex(x => x.CycleId == CycleManager.Instance.CurrentCycle.CycleId);
            var identifyCycle = cycleList.FindIndex(x => x.CycleId == cycleId);
            if (identifyCycle == -1)
            {
                return false;
            }
            return identifyCycle == currentCycleIndex - 1;
        }

        /// <summary>
        /// <para> Target time is utc </para>
        /// </summary>
        /// <param name="wallet"></param>
        /// <param name="score_type"></param>
        /// <param name="targetTime"></param>
        /// <returns></returns>
        public static bool ValidateScoreReward(string wallet, string score_type, DateTime targetTime, DateTime currentTime, List<SerializeScoreSnapshot> targetSnapshotList = default)
        {
            if (score_type == ScoreController.WEEKLY_SNAPSHOT)
                return ValidateWeekly(wallet, targetTime, currentTime, targetSnapshotList.FirstOrDefault(x => x.ScoreUser == wallet));
            if (score_type == ScoreController.SECTOR_SNAPSHOT)
                return ValidateSector(wallet, targetTime, targetSnapshotList);
            if (score_type == ScoreController.GUILD_SNAPSHOT)
                return ValidateGuild(wallet, targetTime, targetSnapshotList);
            if (score_type == ScoreController.PERSONAL_PVP_SNAPSHOT)
                return ValidatePersonalPvP(wallet, targetTime, targetSnapshotList);
            if (score_type == ScoreController.PERSONAL_EXPLORATION_SNAPSHOT)
                return ValidatePersonalExploration(wallet, targetTime, targetSnapshotList);
            return false;
        }

        private static bool ValidatePersonalExploration(string wallet, DateTime targetTime, List<SerializeScoreSnapshot> targetSnapshotList)
        {
            var cycle = CycleManager.Instance.GameCycles.FirstOrDefault(x => x.CycleStartTime <= targetTime && x.CycleEndTime >= targetTime);
            var isPreviousCycle = IsPreviousCycle(cycle.CycleId);
            var orderSnapshotList = targetSnapshotList.OrderByDescending(x => x.ScoreValue).ToList();
            var userScoreSnapshot = orderSnapshotList.FirstOrDefault(x => x.ScoreUser == wallet);
            if (userScoreSnapshot == null) return false;
            var sumScore = orderSnapshotList.Sum(x => x.ScoreValue);
            float accumulateScore = 0;
            for (int i = 0; i < orderSnapshotList.Count; i++)
            {
                accumulateScore += orderSnapshotList[i].ScoreValue;
                if (orderSnapshotList[i].ScoreUser == wallet)
                    break;
            }
            return isPreviousCycle && accumulateScore <= sumScore * SCORE_REWARD_THRESHHOLD / 100;
        }

        private static bool ValidatePersonalPvP(string wallet, DateTime targetTime, List<SerializeScoreSnapshot> targetSnapshotList)
        {
            var cycle = CycleManager.Instance.GameCycles.FirstOrDefault(x => x.CycleStartTime <= targetTime && x.CycleEndTime >= targetTime);
            var isPreviousCycle = IsPreviousCycle(cycle.CycleId);
            var orderSnapshotList = targetSnapshotList.OrderByDescending(x => x.ScoreValue).ToList();
            var userScoreSnapshot = orderSnapshotList.FirstOrDefault(x => x.ScoreUser == wallet);
            if (userScoreSnapshot == null) return false;
            var sumScore = orderSnapshotList.Sum(x => x.ScoreValue);
            float accumulateScore = 0;
            for (int i = 0; i < orderSnapshotList.Count; i++)
            {
                accumulateScore += orderSnapshotList[i].ScoreValue;
                if (orderSnapshotList[i].ScoreUser == wallet)
                    break;
            }
            return isPreviousCycle && accumulateScore <= sumScore * SCORE_REWARD_THRESHHOLD / 100;
        }

        private static bool ValidateGuild(string wallet, DateTime targetTime, List<SerializeScoreSnapshot> targetSnapshotList)
        {
            var startOfTheMonth = Helpers.GetFirstDateOfMonth(targetTime.Year, targetTime.Month);
            var endOfTheMonth = Helpers.GetLastDateOfMonth(targetTime.Year, targetTime.Month);
            var isPreviousMonth = IsPreviousMonth(startOfTheMonth);
            var orderSnapshotList = targetSnapshotList.OrderByDescending(x => x.ScoreValue).ToList();
            var userScoreSnapshot = orderSnapshotList.FirstOrDefault(x => x.ScoreUser == wallet);
            if (userScoreSnapshot == null) return false;
            var passDailyRequirement = userScoreSnapshot.DailyQuestComplete >= GUILD_REWARD_MILESTONE;
            var sumScore = orderSnapshotList.Sum(x => x.ScoreValue);
            float accumulateScore = 0;
            for (int i = 0; i < orderSnapshotList.Count; i++)
            {
                var scoreSnapshot = orderSnapshotList[i];
                accumulateScore += scoreSnapshot.ScoreValue;
                if (scoreSnapshot.ScoreUser == wallet)
                    break;
            }
            return isPreviousMonth && (passDailyRequirement || accumulateScore <= sumScore * SCORE_REWARD_THRESHHOLD / 100);
        }

        public static bool IsPreviousMonth(DateTime monthToCompare)
        {
            var previousMonth = DateTime.UtcNow.Date.AddMonths(-1);
            return monthToCompare.Year == previousMonth.Year && monthToCompare.Month == previousMonth.Month;
        }

        private static bool ValidateSector(string wallet, DateTime targetTime, List<SerializeScoreSnapshot> targetSnapshotList)
        {
            var targetCycle = CycleManager.Instance.GameCycles.FirstOrDefault(x => x.CycleStartTime <= targetTime && x.CycleEndTime >= targetTime);
            if (targetCycle == null) return false;
            var isPreviousCycle = IsPreviousCycle(targetCycle.CycleId);
            var orderSnapshotList = targetSnapshotList.OrderByDescending(x => x.ScoreValue).ToList();
            var userScoreSnapshot = orderSnapshotList.FirstOrDefault(x => x.ScoreUser == wallet);
            if (userScoreSnapshot == null) return false;
            var dailyQuestScore = userScoreSnapshot.DailyQuestComplete;
            var passDailyRequirement = dailyQuestScore >= SECTOR_REWARD_MILESTONE;
            var sumScore = orderSnapshotList.Sum(x => x.ScoreValue);
            float accumulateScore = 0;
            for (int i = 0; i < orderSnapshotList.Count; i++)
            {
                var scoreSnapshot = orderSnapshotList[i];
                accumulateScore += scoreSnapshot.ScoreValue;
                if (scoreSnapshot.ScoreUser == wallet)
                    break;
            }
            return isPreviousCycle && (passDailyRequirement || accumulateScore <= sumScore * SCORE_REWARD_THRESHHOLD / 100);
        }

        private static bool ValidateWeekly(string wallet, DateTime targetTime, DateTime currentTime, SerializeScoreSnapshot userSnapshot)
        {
            var isPreviousWeek = IsPreviousWeek(Helpers.GetWeekOfTheYearCustom(targetTime), currentTime);
            if (userSnapshot == null) return false;
            var dailyQuestScore = userSnapshot.DailyQuestComplete;
            var passDailyRequirement = dailyQuestScore >= WEEKLY_REWARD_MILESTONE;
            return isPreviousWeek && passDailyRequirement;
        }

        public static (float oasReward, float maReward) WeeklyRewardPredict(bool isRewardConditionMeet, DateTime targetTime, string wallet, List<SerializeScoreSnapshot> targetSnapshotList, SerializeCycleData cycleData)
        {
            if (!isRewardConditionMeet) return new(0, 0);
            if (targetSnapshotList.Count == 0) return new(0, 0);
            var userSnapshot = targetSnapshotList.FirstOrDefault(snapshot => snapshot.ScoreUser == wallet);
            if (userSnapshot == null) return new(0, 0);
            float etherValue = float.Parse(cycleData.SubPoolData.WeeklyPool.TotalPool.ToEth(8));
            return WeeklyRewardCalculate(userSnapshot.ScoreValue, etherValue, targetSnapshotList.Sum(x => x.ScoreValue));
        }

        public static (float oasReward, float maReward) WeeklyRewardCalculate(float userScore, float ethValue, float totalScore)
        {
            var maReward = userScore * MA_SCORE_REWARD_CONVERT / 100;
            var oasReward = (ethValue / 12f) * userScore / totalScore;
            if (oasReward == float.NaN)
                oasReward = 0;
            return new(oasReward, maReward);
        }

        public static float SectorRewardPredict(bool isRewardConditionMet, DateTime targetTime, string wallet, List<SerializeScoreSnapshot> targetSnapshot, SerializeCycleData cycleData, List<SerializeSectorData> sectorDataList)
        {
            if (!isRewardConditionMet) return 0;
            var userSector = sectorDataList.FirstOrDefault(sector => sector.SectorUsersMember.Contains(wallet));
            int sectorRank = sectorDataList.FindIndex(x => x == userSector);
            if (sectorRank <= 0) return 0;
            return CalculateSectorOASReward(wallet, cycleData, targetSnapshot, userSector, sectorRank);
        }

        public static float GuildRewardPredict(bool isRewardConditionMet, string wallet, DateTime targetTime, List<SerializeScoreSnapshot> targetSnapshotList, SerializeCycleData cycleData, List<SerializeGuildData> guildData)
        {
            if (!isRewardConditionMet) return 0;
            var firstDayOfTheMonth = Helpers.GetFirstDateOfMonth(targetTime.Year, targetTime.Month);
            var lastDayOfTheMonth = Helpers.GetLastDateOfMonth(targetTime.Year, targetTime.Month);
            float etherValue = float.Parse(cycleData.SubPoolData.GuildPool.TotalPool.ToEth(8));
            float totalReward = etherValue / 3;
            SerializeGuildData guild = GuildDataController.GetGuildByUserWallet(wallet);
            if (string.IsNullOrEmpty(guild._guildId)) return 0;
            var guildScoreRecord = GuildDataManager.Instance.GuildCycleScore;
            SerializeGuildCycleScore guildCycle = GuildDataController.GetGuildCycleData(ref guild, in guildScoreRecord, firstDayOfTheMonth, lastDayOfTheMonth, cycleData.CycleId);
            if (string.IsNullOrEmpty(guildCycle._id)) return 0;
            float totalGuildsScore = guildScoreRecord.Where(x => x._guildId == guild._guildId).Sum(x => x._exploreScore + x._rankedScore);
            float guildRewardAmount = GetGuildRewardAmount(totalReward, guildCycle._rankedScore + guildCycle._exploreScore, totalGuildsScore);
            float totalUserScore = 0;
            float currentUserScore = 0;
            for (int i = 0; i < targetSnapshotList.Count(); i++)
            {
                if (!guild._guildMemberList.Exists(x => x._memberWallet == targetSnapshotList[i].ScoreUser)) continue;
                var userSnapshot = targetSnapshotList[i];
                var individualScore = MathF.Sqrt(userSnapshot.ScoreValue);
                totalUserScore += individualScore;
                if (userSnapshot.ScoreUser == wallet)
                    currentUserScore = individualScore;
            }
            return GetPersonalGuildReward(guildRewardAmount, totalUserScore, currentUserScore);
        }

        public static float GetPersonalGuildReward(float guildRewardAmount, float totalUserScore, float currentUserScore)
        {
            var oasReward = guildRewardAmount * (float)currentUserScore / (float)totalUserScore;
            if (oasReward == float.NaN)
                oasReward = 0;
            return oasReward;
        }

        public static float GetGuildRewardAmount(float totalReward, float guildScore, float totalGuildScore)
        {
            return guildScore / totalGuildScore * totalReward;
        }

        public static float PersonalPvpRewardPredict(bool isRewardConditionMet, DateTime targetTime, string wallet, List<SerializeScoreSnapshot> targetSnapshotList, SerializeCycleData cycleData)
        {
            if (!isRewardConditionMet) return 0;
            var snapshotList = targetSnapshotList.OrderByDescending(x => x.ScoreValue).ToList();
            var userScoreSnapshot = snapshotList.FirstOrDefault(x => x.ScoreUser == wallet);
            if (userScoreSnapshot == null) return 0;
            var totalScore = snapshotList.Sum(x => x.ScoreValue);
            int rewardRank = GetPersonalRewardRank(snapshotList.Select(x => x.ScoreValue).ToList(), totalScore);
            var alphaValue = CalculateAlphaValue(rewardRank);
            float totalWeight = 0;
            float userWeight = 0;
            for (int i = 0; i < rewardRank; i++)
            {
                if (i >= rewardRank) break;
                SerializeScoreSnapshot snapshot = snapshotList[i];
                float personalWeight = CalculateWeight(rewardRank, alphaValue, i + 1);
                totalWeight += personalWeight;
                if (snapshot.ScoreUser == wallet)
                    userWeight = personalWeight;
            }
            float totalReward = float.Parse(cycleData.SubPoolData.PersonalPvpPool.TotalPool.ToEth(8));
            return GetPersonalReward(totalReward, totalWeight, userWeight);
        }

        public static float GetPersonalReward(float totalReward, float totalWeight, float userWeight)
        {
            var oasReward = totalReward * userWeight / totalWeight;
            if (oasReward == float.NaN)
                oasReward = 0;
            return oasReward;
        }

        // Get reward rank milestone
        public static int GetPersonalRewardRank(List<float> scoreList, float totalScore)
        {
            float accumulateScore = 0;
            int rewardRank = 0;
            for (int i = 0; i < scoreList.Count; i++)
            {
                accumulateScore += scoreList[i];
                if (accumulateScore > totalScore * SCORE_REWARD_THRESHHOLD / 100)
                {
                    rewardRank = i;
                    break;
                }
            }
            return rewardRank;
        }

        public static float CalculateWeight(int rewardRank, float alphaValue, int rank) => MathF.Sqrt(MathF.Pow(alphaValue, rewardRank + 1 - rank));

        public static float CalculateAlphaValue(int rewardRank) => 1 + (MathF.Sqrt(rewardRank) / rewardRank);

        public static float PersonalExploreRewardPredict(bool isRewardConditionMet, DateTime targetTime, string wallet, List<SerializeScoreSnapshot> targetSnapshot, SerializeCycleData cycle)
        {
            if (!isRewardConditionMet) return 0;
            var snapshotList = targetSnapshot.OrderByDescending(x => x.ScoreValue).ToList();
            var userScoreSnapshot = snapshotList.FirstOrDefault(x => x.ScoreUser == wallet);
            if (userScoreSnapshot == null) return 0;
            var totalScore = snapshotList.Sum(x => x.ScoreValue);
            int rewardRank = GetPersonalRewardRank(snapshotList.Select(x => x.ScoreValue).ToList(), totalScore);
            var alphaValue = CalculateAlphaValue(rewardRank);
            float totalWeight = 0;
            float userWeight = 0;
            for (int i = 0; i < rewardRank; i++)
            {
                if (i >= rewardRank) break;
                SerializeScoreSnapshot snapshot = snapshotList[i];
                var personalWeight = CalculateWeight(rewardRank, alphaValue, i + 1);
                totalWeight += personalWeight;
                if (snapshot.ScoreUser == wallet)
                    userWeight = personalWeight;
            }
            float totalReward = float.Parse(cycle.SubPoolData.PersonalPvpPool.TotalPool.ToEth(8));
            return GetPersonalReward(totalReward, totalWeight, userWeight);
        }

        private static float CalculateSectorOASReward(string wallet, SerializeCycleData cycle, List<SerializeScoreSnapshot> snapshotList, SerializeSectorData userSector, int sectorRank)
        {
            float totalReward = float.Parse(cycle.SubPoolData.SectorPool.TotalPool.ToEth(8));
            float individualTotalScore = 0;
            float currentUserScore = 0;
            for (int i = 0; i < snapshotList.Count(); i++)
            {
                var userSnapshot = snapshotList[i];
                var totalUserScore = (float)MathF.Sqrt(userSnapshot.ScoreValue);
                individualTotalScore += totalUserScore;
                if (userSnapshot.ScoreUser == wallet)
                    currentUserScore = totalUserScore;
            }
            return CalculateSectorReward(totalReward, sectorRank, currentUserScore, individualTotalScore);
        }

        public static float CalculateSectorReward(float totalReward, int sectorRank, float userScore, float otherUsersScore)
        {
            float sectorReward = 0;
            if (sectorRank == 0)
                sectorReward = totalReward * FIRST_PLACE_SECTOR_REWARD / 100;
            if (sectorRank == 1)
                sectorReward = totalReward * SECCOND_PLACE_SECTOR_REWARD / 100;
            if (sectorRank == 2)
                sectorReward = totalReward * THIRD_PLACE_SECTOR_REWARD / 100;
            var oasReward = sectorReward * userScore / otherUsersScore;
            if (oasReward == float.NaN)
                oasReward = 0;
            return oasReward;
        }
    }
}
