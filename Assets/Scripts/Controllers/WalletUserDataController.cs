using Assets.Scripts.DataStruct;
using Assets.Scripts.DataStruct.Data;
using Assets.Scripts.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Assets.Scripts.Controllers
{
    public class WalletUserDataController
    {
        public static void MergeDataList(List<SerializeUserData> list_user_data, List<ServerUserData> cachedUsersDataList)
        {
            for (int i = 0; i < list_user_data.Count; i++)
            {
                SerializeUserData newData = list_user_data[i];
                var cachedData = cachedUsersDataList.FirstOrDefault(user => user.AddressWallet.Equals(newData.address_wallet));

                if (cachedData != null)
                {
                    cachedData.LoginDay = newData.login_day;
                    cachedData.SetUserAvatar(newData.avatar);
                    cachedData.PlayerRank = (PlayerRanksEnum)newData.player_rank;
                    cachedData.PlayerBio = newData.player_bio;
                    cachedData.PlayerLanguage = newData.player_language;
                    cachedData.UserId = newData.user_id;
                    cachedData.UserName = newData.user_name;
                    cachedData.AddressWallet = newData.address_wallet;
                    cachedData.BitBalance = newData.bit_balance;
                    cachedData.PhoneVerified = newData.phone_verified;
                    cachedData.EmailVerified = newData.email_verified;
                    cachedData.LastActive = DateTime.Parse(newData.last_active, null, System.Globalization.DateTimeStyles.RoundtripKind);
                    cachedData.LastLogin = DateTime.Parse(newData.last_login, null, System.Globalization.DateTimeStyles.RoundtripKind);
                    cachedData.TotalLoginDay = newData.total_login_day;
                    cachedData.PlayerTime = newData.player_time;
                    cachedData.MapointAmount = newData.mapoint_amount;
                    cachedData.PlayerPhone = newData.player_phone;
                    cachedData.Email = newData.email;
                    cachedData.PlayerTwitter = newData.player_twitter;
                }
                else
                {
                    ServerUserData userData = new()
                    {
                        Id = newData._id,
                        LoginDay = newData.login_day,
                        PlayerRank = (PlayerRanksEnum)newData.player_rank,
                        PlayerBio = newData.player_bio,
                        PlayerLanguage = newData.player_language,
                        UserId = newData.user_id,
                        UserName = newData.user_name,
                        AddressWallet = newData.address_wallet,
                        BitBalance = newData.bit_balance,
                        PhoneVerified = newData.phone_verified,
                        EmailVerified = newData.email_verified,
                        TotalLoginDay = newData.total_login_day,
                        PlayerTime = newData.player_time,
                        MapointAmount = newData.mapoint_amount,
                        PlayerPhone = newData.player_phone,
                        Email = newData.email,
                        PlayerTwitter = newData.player_twitter,
                    };
                    if (newData.last_active != null)
                        userData.LastActive = DateTime.Parse(newData.last_active, null, System.Globalization.DateTimeStyles.RoundtripKind);
                    if (newData.last_login != null)
                        userData.LastLogin = DateTime.Parse(newData.last_login, null, System.Globalization.DateTimeStyles.RoundtripKind);

                    userData.SetUserAvatar(newData.avatar);
                    cachedUsersDataList.Add(userData);
                }
            }
        }
    }
}
