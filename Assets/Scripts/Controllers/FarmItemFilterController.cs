using Assets.Scripts.DataStruct.Data;
using Assets.Scripts.Managers;
using Assets.Scripts.SerializeDataStruct.ItemData;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

namespace Assets.Scripts.Controllers
{
    [System.Serializable]
    public class FarmItemFilterController
    {
        [SerializeField] private List<Toggle> sortToggleList;
        [SerializeField] private Toggle hpToggle;
        [SerializeField] private Toggle strToggle;
        [SerializeField] private Toggle intToggle;
        [SerializeField] private Toggle dexToggle;
        [SerializeField] private Toggle agiToggle;
        [SerializeField] private Toggle vitToggle;
        [SerializeField] private Toggle fatigueToggle;
        [SerializeField] private Toggle stressToggle;
        [SerializeField] private Toggle restToggle;
        [Header("Quality filter")]
        [SerializeField] private Toggle shopToggle;
        [SerializeField] private Toggle bonusToggle;
        [SerializeField] private Toggle commonToggle;
        [SerializeField] private Toggle uncommonToggle;
        [SerializeField] private Toggle rareToggle;
        private int m_sortIndex;
        private FarmItemInventoryFilterData m_filterData;
        public System.Action ItemCatChange { get; set; }

        public void Init()
        {
            for (int i = 0; i < sortToggleList.Count; i++)
            {
                Toggle sortToggle = sortToggleList[i];
                int sortIndex = i;
                sortToggle.onValueChanged.AddListener((bool value) =>
                {
                    if (value)
                    {
                        ItemCatChange?.Invoke();
                    }
                });
            }
        }

        public void LoadSortAndFilterOption()
        {
            m_filterData = GameDataManager.Instance.LoadedPlayerData.FarmItemListFilterTags;
            m_sortIndex = m_filterData.m_sortIndex;

            hpToggle.SetIsOnWithoutNotify(m_filterData.m_itemEffect.Contains("HP"));
            strToggle.SetIsOnWithoutNotify(m_filterData.m_itemEffect.Contains("STR"));
            intToggle.SetIsOnWithoutNotify(m_filterData.m_itemEffect.Contains("INT"));
            dexToggle.SetIsOnWithoutNotify(m_filterData.m_itemEffect.Contains("DEX"));
            agiToggle.SetIsOnWithoutNotify(m_filterData.m_itemEffect.Contains("AGI"));
            vitToggle.SetIsOnWithoutNotify(m_filterData.m_itemEffect.Contains("VIT"));
            fatigueToggle.SetIsOnWithoutNotify(m_filterData.m_itemEffect.Contains("Fatigue"));
            stressToggle.SetIsOnWithoutNotify(m_filterData.m_itemEffect.Contains("Stress"));
            restToggle.SetIsOnWithoutNotify(m_filterData.m_itemEffect.Contains("Rest"));

            shopToggle.SetIsOnWithoutNotify(m_filterData.m_itemQuality.Contains((int)Enums.ItemsRarityEnum.Shop));
            bonusToggle.SetIsOnWithoutNotify(m_filterData.m_itemQuality.Contains((int)Enums.ItemsRarityEnum.B));
            commonToggle.SetIsOnWithoutNotify(m_filterData.m_itemQuality.Contains((int)Enums.ItemsRarityEnum.C));
            uncommonToggle.SetIsOnWithoutNotify(m_filterData.m_itemQuality.Contains((int)Enums.ItemsRarityEnum.UC));
            rareToggle.SetIsOnWithoutNotify(m_filterData.m_itemQuality.Contains((int)Enums.ItemsRarityEnum.R));
        }

        internal void ClearFilterSortItem()
        {
            sortToggleList[0].SetIsOnWithoutNotify(true);
            hpToggle.SetIsOnWithoutNotify(false);
            strToggle.SetIsOnWithoutNotify(false);
            intToggle.SetIsOnWithoutNotify(false);
            dexToggle.SetIsOnWithoutNotify(false);
            agiToggle.SetIsOnWithoutNotify(false);
            vitToggle.SetIsOnWithoutNotify(false);
            fatigueToggle.SetIsOnWithoutNotify(false);
            stressToggle.SetIsOnWithoutNotify(false);
            restToggle.SetIsOnWithoutNotify(false);

            shopToggle.SetIsOnWithoutNotify(false);
            bonusToggle.SetIsOnWithoutNotify(false);
            commonToggle.SetIsOnWithoutNotify(false);
            uncommonToggle.SetIsOnWithoutNotify(false);
            rareToggle.SetIsOnWithoutNotify(false);
        }

        public List<SerializeItem> PerformSortAndFilter()
        {
            List<SerializeItem> sortItemList = GetFarmEnhanceItem(GameDataManager.Instance.LoadedPlayerData.PlayerItemList);
            sortItemList = GetItem(sortItemList, hpToggle.isOn, strToggle.isOn, intToggle.isOn, dexToggle.isOn, agiToggle.isOn, vitToggle.isOn, fatigueToggle.isOn, stressToggle.isOn, restToggle.isOn);
            sortItemList = GetItemQualityFilter(sortItemList, shopToggle.isOn, bonusToggle.isOn, commonToggle.isOn, uncommonToggle.isOn, rareToggle.isOn);
            sortItemList = GetItemSort(sortItemList, m_sortIndex);
            m_filterData.m_sortIndex = m_sortIndex;
            GameDataManager.UpdateFarmItemInvFilter?.Invoke(m_filterData);
            return sortItemList;
        }

        public bool ValidateItemFilter()
        {
            if (hpToggle.isOn || strToggle.isOn || intToggle.isOn || dexToggle.isOn || agiToggle.isOn || vitToggle.isOn || fatigueToggle.isOn || stressToggle.isOn || restToggle.isOn)
                return true;
            if (shopToggle.isOn || bonusToggle.isOn || commonToggle.isOn || uncommonToggle.isOn || rareToggle.isOn)
                return true;
            return false;
        }

        private List<SerializeItem> GetFarmEnhanceItem(List<SerializeItem> inputList)
        {
            List<SerializeItem> outputList = new();
            for (int i = 0; i < inputList.Count; i++)
            {
                SerializeItem itemData = inputList[i];
                Scriptables.Item.ScriptableEnhanceItem enhanceItem = itemData.Item as Scriptables.Item.ScriptableEnhanceItem;
                if (enhanceItem == null) continue;
                if (enhanceItem.FarmBasicParameterEnhance.Health != 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmBasicParameterEnhance.Strength != 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmBasicParameterEnhance.Intelligent != 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmBasicParameterEnhance.Dexterity != 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmBasicParameterEnhance.Agility != 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmBasicParameterEnhance.Vitality != 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmStressEnhanceValue != 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmFatigueEnhanceValue != 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmRestEnhanceValue != 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
            }

            foreach (var item in GameDataManager.Instance.LoadedPlayerData.PlayerCoachItemList.AsSpan())
            {
                outputList.Add(new SerializeItem(item));
            }
            return outputList;
        }

        private List<SerializeItem> GetItem(List<SerializeItem> inputList, bool isHpOn, bool isStrOn, bool isIntOn, bool isDexOn, bool isAgiOn, bool isVitOn, bool isFatigueOn, bool isStressOn, bool isRestOn)
        {
            List<string> itemEffect = new();
            if (!isHpOn && !isStrOn && !isIntOn && !isDexOn && !isAgiOn && !isVitOn && !isFatigueOn && !isStressOn && !isRestOn)
            {
                m_filterData.m_itemEffect = itemEffect.ToArray();
                return inputList;
            }

            List<SerializeItem> outputList = new();
            if (isHpOn) itemEffect.Add("HP");
            if (isStrOn) itemEffect.Add("STR");
            if (isIntOn) itemEffect.Add("INT");
            if (isDexOn) itemEffect.Add("DEX");
            if (isAgiOn) itemEffect.Add("AGI");
            if (isVitOn) itemEffect.Add("VIT");
            if (isFatigueOn) itemEffect.Add("Fatigue");
            if (isStressOn) itemEffect.Add("Stress");
            if (isRestOn) itemEffect.Add("Rest");
            m_filterData.m_itemEffect = itemEffect.ToArray();

            for (int i = 0; i < inputList.Count; i++)
            {
                SerializeItem itemData = inputList[i];
                Scriptables.Item.ScriptableEnhanceItem enhanceItem = itemData.Item as Scriptables.Item.ScriptableEnhanceItem;
                if (enhanceItem == null) continue;
                if (enhanceItem.FarmBasicParameterEnhance.Health != 0 && isHpOn || itemData.CoachItemData.CoachParameter == Enums.BasicBattleParameterEnum.HP && isHpOn && itemData.CoachItemData.CoachParameterValue > 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmBasicParameterEnhance.Strength != 0 && isStrOn || itemData.CoachItemData.CoachParameter == Enums.BasicBattleParameterEnum.STR && isStrOn && itemData.CoachItemData.CoachParameterValue > 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmBasicParameterEnhance.Intelligent != 0 && isIntOn || itemData.CoachItemData.CoachParameter == Enums.BasicBattleParameterEnum.INT && isIntOn && itemData.CoachItemData.CoachParameterValue > 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmBasicParameterEnhance.Dexterity != 0 && isDexOn || itemData.CoachItemData.CoachParameter == Enums.BasicBattleParameterEnum.DEX && isDexOn && itemData.CoachItemData.CoachParameterValue > 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmBasicParameterEnhance.Agility != 0 && isAgiOn || itemData.CoachItemData.CoachParameter == Enums.BasicBattleParameterEnum.AGI && isAgiOn && itemData.CoachItemData.CoachParameterValue > 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmBasicParameterEnhance.Vitality != 0 && isVitOn || itemData.CoachItemData.CoachParameter == Enums.BasicBattleParameterEnum.VIT && isVitOn && itemData.CoachItemData.CoachParameterValue > 0)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmStressEnhanceValue != 0 && isStressOn)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmFatigueEnhanceValue != 0 && isFatigueOn)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (enhanceItem.FarmRestEnhanceValue != 0 && isRestOn)
                {
                    outputList.Add(itemData);
                    continue;
                }
            }

            return outputList;
        }

        private List<SerializeItem> GetItemSort(List<SerializeItem> inputList, int sortIndex)
        {
            List<SerializeItem> outputList = new();
            if (inputList == null || inputList.Count == 0)
            {
                return outputList;
            }
            outputList = sortIndex switch
            {
                0 => inputList.OrderBy(_ => _.Item.ItemName).ToList(),
                1 => inputList.OrderByDescending(_ => _.Item.ItemName).ToList(),
                2 => inputList.OrderBy(_ => _.Quantity).ToList(),
                3 => inputList.OrderByDescending(_ => _.Quantity).ToList(),
                4 => inputList.OrderBy(_ => _.Item.ItemRarity).ToList(),
                5 => inputList.OrderByDescending(_ => _.Item.ItemRarity).ToList(),
                _ => inputList
            };
            return outputList;
        }

        private List<SerializeItem> GetItemQualityFilter(List<SerializeItem> inputList, bool isShop, bool isBonus, bool isCommon, bool isUncommon, bool isRare)
        {
            List<int> itemQuality = new();
            List<SerializeItem> outputList = new();
            if (!isShop && !isBonus && !isCommon && !isUncommon && !isRare)
            {
                m_filterData.m_itemQuality = itemQuality.ToArray();
                return inputList;
            }
            if (isShop) itemQuality.Add((int)Enums.ItemsRarityEnum.Shop);
            if (isBonus) itemQuality.Add((int)Enums.ItemsRarityEnum.B);
            if (isCommon) itemQuality.Add((int)Enums.ItemsRarityEnum.C);
            if (isUncommon) itemQuality.Add((int)Enums.ItemsRarityEnum.UC);
            if (isRare) itemQuality.Add((int)Enums.ItemsRarityEnum.R);
            m_filterData.m_itemQuality = itemQuality.ToArray();

            for (int i = 0; i < inputList.Count; i++)
            {
                SerializeItem itemData = inputList[i];
                if (isShop && itemData.Item.ItemRarity == Enums.ItemsRarityEnum.Shop)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (isBonus && itemData.Item.ItemRarity == Enums.ItemsRarityEnum.B)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (isCommon && itemData.Item.ItemRarity == Enums.ItemsRarityEnum.C)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (isUncommon && itemData.Item.ItemRarity == Enums.ItemsRarityEnum.UC)
                {
                    outputList.Add(itemData);
                    continue;
                }
                if (isRare && itemData.Item.ItemRarity == Enums.ItemsRarityEnum.R)
                {
                    outputList.Add(itemData);
                    continue;
                }
            }
            return outputList;
        }
    }
}
