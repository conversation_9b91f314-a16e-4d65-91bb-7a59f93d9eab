using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;

public class TatuUtils : EditorWindow
{
    [MenuItem("Tatu/ToScene/To Battle Scene")]
    // Start is called before the first frame update
    public static void ToBattleScene()
    {
        EditorSceneManager.OpenScene("Assets/Scenes/BattlePVE_NewUI.unity");

    }
    
    [MenuItem("Tatu/ToScene/To Login Scene")]
    public static void ToLoginScene()
    {
        EditorSceneManager.OpenScene("Assets/Scenes/LoginScene.unity");

    }
    [MenuItem("Tatu/ToScene/To Menu Scene")]
    public static void ToMenuScene()
    {
        EditorSceneManager.OpenScene("Assets/Scenes/UIMainmenu.unity");

    }   
    
}
