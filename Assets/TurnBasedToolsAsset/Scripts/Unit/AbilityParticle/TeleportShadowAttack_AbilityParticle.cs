using Assets.Scripts.UI.BattleUI.BattleHeader;
using TurnBasedToolsAsset.Scripts.Gameplay.GameRules;
using UnityEngine;

public class TeleportShadowAttack_AbilityParticle : AbilityParticle
{
    private readonly Vector3 _lookOffset = new(0f, -90f, 0f); //Look by X axis

    //GridUnit caster;
    private ILevelCell _target;

    public Transform startPos;
    public Vector3 startHideOffset;
    public float timeToHide = 0.25f;
    public ParticleSystem startShadowFX;
    private bool _startToHide;

    public Transform showPos;
    public Vector3 showOffset;
    public float timeToShow = 0.25f;
    public ParticleSystem showShadowFX;
    private bool _hideToShow;

    private float _timePass;
    // public bool attacked = false;
    // public AnimationClip attackAnimation;

    public override void Setup(UnitAbility inAbility, GridUnit inCaster, ILevelCell inTarget, ILevelCell selectCell)
    {
        base.Setup(inAbility, inCaster, inTarget, selectCell);

        var casterCell = inCaster.GetCell();

        _target = inTarget;

        var stealer = inCaster.GameMonster.InGameParameter.Stealer;

        inCaster.transform.LookAt(inTarget.transform.position);

        if (stealer != null)
        {
            stealer.transform.LookAt(inTarget.transform.position);
        }

        transform.position = casterCell.transform.position;
        transform.LookAt(inTarget.transform.position);
        transform.rotation *= Quaternion.Euler(_lookOffset);

        //caster = InCaster;
        startPos.position = casterCell.transform.position;

        showPos.position = _target.transform.position;

        if(inCaster.GetTeam() == CustomBattle.CustomGameManager.Instance.PlayerSide || !inCaster.GameMonster.InGameParameter.BuffCombineValue.IsInvisible)
        {
            startShadowFX.gameObject.SetActive(true);
            startShadowFX.Play();
        }
        else
        {
            startShadowFX.gameObject.SetActive(false);
        }

        if (inCaster.GetTeam() == CustomBattle.CustomGameManager.Instance.PlayerSide)
        {
            showShadowFX.gameObject.SetActive(true);
            showShadowFX.Play();
        }else
            showShadowFX.gameObject.SetActive(false);

        _timePass = 0;

        _startToHide = false;

        _hideToShow = false;

        //attacked = false;
    }

    private void Update()
    {
        _timePass += Time.deltaTime;

        var stealer = Caster.GameMonster.InGameParameter.Stealer;

        if (_startToHide == false)
        {
            var lerpVal = Mathf.Clamp(_timePass / timeToHide, 0f, 1f);

            Caster.transform.position = Vector3.Lerp(startPos.position, startPos.position + startHideOffset, lerpVal);

            if (stealer)
            {
                stealer.transform.position = Vector3.Lerp(startPos.position, startPos.position + startHideOffset, lerpVal);
            }

            if (lerpVal >= 1f)
            {
                _startToHide = true;
                _timePass = 0;
            }
            else
            {
                UIBattleMonsterQuickDetailControl.OnActionPerformed?.Invoke(Caster.GameMonster.BattleID);
                (GameManager.GetRules() as CustomGameRules)?.SpawnedSelectedGo.Hide();
            }
        }
        else if (_hideToShow == false)
        {
            var lerpVal = Mathf.Clamp(_timePass / timeToShow, 0f, 1f);

            Caster.transform.position = Vector3.Lerp(showPos.position + showOffset, showPos.position, lerpVal);

            if (stealer != null)
            {
                stealer.transform.position = Vector3.Lerp(showPos.position + showOffset, showPos.position, lerpVal);
            }

            if (!(lerpVal >= 1f)) return;
            _hideToShow = true;
            _timePass = 0;
        }
    }
}
