using UnityEngine;

public class AbilityParticleChangeCasterEmissionMat : MonoBehaviour
{
    AbilityParticle abilityParticle;

    [SerializeField]
    Material changeMat;

    [SerializeField]
    Color toColor;

    [SerializeField]
    float activeDelay = 0f;
    [SerializeField]
    float timeToColor = 0f;
    [SerializeField]
    float timeBackToOrigin = 0f;

    private void Start()
    {
        abilityParticle = GetComponent<AbilityParticle>();

        MonsterIngameChangeMaterial changeMatMonster = abilityParticle.Caster.GetComponent<MonsterIngameChangeMaterial>();

        if(changeMatMonster != null)
        {
            changeMatMonster.StartHightLightColor(activeDelay, timeToColor, timeBackToOrigin, changeMat.name, toColor);
        }
    }
}
