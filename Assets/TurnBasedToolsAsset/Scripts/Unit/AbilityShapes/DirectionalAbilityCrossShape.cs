using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(fileName = "DirectionalAbilityCrossShape", menuName = "TurnBasedTools/Ability/Shapes/Create DirectionalAbilityCrossShape", order = 1)]
public class DirectionalAbilityCrossShape : AbilityShape
{
    [SerializeField]
    bool m_bOnlyMyEnemies;

    public override List<ILevelCell> GetCellList(GridUnit InCaster, ILevelCell InCell, int InRange, bool bAllowBlocked, GameTeam m_EffectedTeam)
    {
        List<ILevelCell> cells = new List<ILevelCell>();

        CompassDir compassDir = GetCompassDirLookToCaster(InCaster.transform.position, InCell.transform.position);

        if (InRange == 0)
        {
            cells.Add(InCell);
        }

        if (InCell)
        {
            switch (compassDir)
            {
                case CompassDir.NW:
                    {
                        cells.AddRange(GetCellsInDirection(InCell, InRange, CompassDir.SE, bAllowBlocked, m_EffectedTeam));

                        if (cells.Count > 0)
                        {
                            ILevelCell lastCell = cells[cells.Count - 1];
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.E));
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.SE));
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.SW));
                        }

                        break;
                    }

                case CompassDir.NE:
                    {
                        cells.AddRange(GetCellsInDirection(InCell, InRange, CompassDir.SW, bAllowBlocked, m_EffectedTeam));

                        if (cells.Count > 0)
                        {
                            ILevelCell lastCell = cells[cells.Count - 1];
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.SE));
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.SW));
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.W));
                        }

                        break;
                    }

                case CompassDir.E:
                    {
                        cells.AddRange(GetCellsInDirection(InCell, InRange, CompassDir.W, bAllowBlocked, m_EffectedTeam));

                        if (cells.Count > 0)
                        {
                            ILevelCell lastCell = cells[cells.Count - 1];
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.SW));
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.W));
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.NW));
                        }

                        break;
                    }

                case CompassDir.SE:
                    {
                        cells.AddRange(GetCellsInDirection(InCell, InRange, CompassDir.NW, bAllowBlocked, m_EffectedTeam));

                        if(cells.Count > 0)
                        {
                            ILevelCell lastCell = cells[cells.Count - 1];
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.W));
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.NW));
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.NE));
                        }

                        break;
                    }

                case CompassDir.SW:
                    {
                        cells.AddRange(GetCellsInDirection(InCell, InRange, CompassDir.NE, bAllowBlocked, m_EffectedTeam));

                        if (cells.Count > 0)
                        {
                            ILevelCell lastCell = cells[cells.Count - 1];
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.NW));
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.NE));
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.E));
                        }

                        break;
                    }

                case CompassDir.W:
                    {
                        cells.AddRange(GetCellsInDirection(InCell, InRange, CompassDir.E, bAllowBlocked, m_EffectedTeam));

                        if (cells.Count > 0)
                        {
                            ILevelCell lastCell = cells[cells.Count - 1];
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.NE));
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.E));
                            cells.Add(lastCell.GetAdjacentCell(CompassDir.SE));
                        }

                        break;
                    }
            }
        }

        if (InRange == 0)
        {
            cells.Remove(InCell);
        }

        if (m_bOnlyMyEnemies)
        {
            List<ILevelCell> enemyCells = new List<ILevelCell>();
            foreach (var currCell in cells)
            {
                GridUnit unitOnCell = currCell.GetUnitOnCell();
                if (unitOnCell)
                {
                    GameTeam AffinityToCaster = GameManager.GetTeamAffinity(InCaster.GetTeam(), unitOnCell.GetTeam());
                    if (AffinityToCaster == GameTeam.Red)
                    {
                        enemyCells.Add(currCell);
                    }
                }
            }

            return enemyCells;
        }
        else
        {
            return cells;
        }
    }
}
