using System.Collections;
using System.Collections.Generic;
using Network;
using UnityEngine;
using UnityEngine.Events;

[CreateAssetMenu(fileName = "UnitAbilitySwitchTargetCellToAttack", menuName = "TurnBasedTools/Ability/Create New UnitAbilitySwitchTargetCellToAttack", order = 1)]
public class UnitAbilitySwitchTargetCellToAttack : UnitAbility
{
    Vector3 offset = new Vector3(0f, -90f, 0f); //Look by X axis

    [SerializeField]
    float activeDelay = 0f;

    GridUnit targetUnit = null;
    ILevelCell targetUnitCell = null;

    ILevelCell nearestCell = null;

    [SerializeField]
    float suctionTime = 0.5f;

    [SerializeField]
    AnimationClip startAnim;

    [SerializeField]
    AbilityParticle[] mPre_SpawnOnCaster;

    CompassDir compassDir;

    List<ILevelCell> EffectCellList;

    void Setup(GridUnit InCaster, ILevelCell InTarget)
    {
        
        compassDir = GetShape().GetCompassDirLookToCaster(InCaster.transform.position, InTarget.transform.position);

        targetUnit = InTarget.GetUnitOnCell();

        targetUnitCell = InTarget;

        EffectCellList = GetDircells(this, InCaster, InTarget);
        foreach (ILevelCell cell in EffectCellList)
        {
            if (nearestCell == null)
            {
                nearestCell = cell;
            }
            else
            {
                float distToNearestCell = Vector3.Distance(InCaster.transform.position, nearestCell.transform.position);
                float distToCurCell = Vector3.Distance(InCaster.transform.position, cell.transform.position);
                
                if (distToCurCell < distToNearestCell)
                {
                    //Debug.Log($"Near cell = {cell.name}");
                    //Debug.Log($"distToNearestCell = {distToNearestCell}");
                    //Debug.Log($"distToCurCell = {distToCurCell}");
                
                    nearestCell = cell;
                    
                }
            }
        }
        InCaster.LookAtCell(nearestCell);
    }

    public List<ILevelCell> GetDircells(UnitAbility InAbility, GridUnit InCaster, ILevelCell InTarget)
    {
        List<ILevelCell> cells = InAbility.GetAbilityCells(InCaster);

        List<ILevelCell> dirCells = new();

        compassDir = InAbility.GetShape().GetCompassDirLookToCaster(InCaster.transform.position, InTarget.transform.position);

        foreach (ILevelCell c in cells)
        {
            CompassDir cellDir = InAbility.GetShape().GetCompassDirLookToCaster(InCaster.transform.position, c.transform.position);

            if (cellDir == compassDir)
            {
                dirCells.Add(c);
            }
        }

        return dirCells;
    }

    public override IEnumerator Execute(GridUnit InCasterUnit, ILevelCell InTarget, UnityEvent OnComplete = null,PlayerAction networkMessage = null)
    {
        GameManager.AddActionBeingPerformed();

        Setup(InCasterUnit, InTarget);

        if (startAnim != null)
        {
            InCasterUnit.PlayAnimation(startAnim, true);
        }

        SpawnParticleSelectedCell(InCasterUnit, InTarget, null, mPre_SpawnOnCaster);

        yield return new WaitForSeconds(activeDelay);

        float duration = suctionTime; // 3 seconds you can change this 
                                      //to whatever you want
        float normalizedTime = 0f;
        while (normalizedTime <= 1f)
        {
            normalizedTime += Time.deltaTime / duration;

            targetUnit.transform.position = Vector3.Lerp(targetUnitCell.transform.position, nearestCell.transform.position, normalizedTime);

            yield return null;
        }

        ILevelCell onCell = targetUnit.GetCell();

        targetUnit.SetCurrentCell(nearestCell);

        if (nearestCell.GetUnitOnCell() != null)
        {
            nearestCell.SetMaterial(onCell.GetCellState());
            nearestCell.SetCellState(nearestCell.GetCellState());

            onCell.SetMaterial(nearestCell.GetCellState());
            onCell.SetCellState(onCell.GetCellState());

            AilmentHandler.HandleUnitOnCell(targetUnit, nearestCell);
        }

        yield return base.Execute(InCasterUnit, nearestCell, OnComplete,networkMessage);

        GameManager.RemoveActionBeingPerformed();
        nearestCell = null;
        yield break;
    }
}
