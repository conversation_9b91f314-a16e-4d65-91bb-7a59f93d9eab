using CustomBattle;
using System.Collections;
using System.Collections.Generic;
using Network;
using UnityEngine;
using UnityEngine.Events;

[CreateAssetMenu(fileName = "UnitAbilityTeleportToTarget", menuName = "TurnBasedTools/Ability/Create New UnitAbilityTeleportToTarget", order = 1)]
public class UnitAbilityTeleportToTarget : UnitAbility
{
    public int KeepDistance = 1;
    public bool randomToCellAround = false;
    [SerializeField]
    AbilityParticle[] mPre_SpawnOnCaster;

    [SerializeField]
    AbilityParticle[] mPre_SpawnOnTarget;

    [SerializeField]
    AbilityParticle[] mPre_SpawnOnSelect;

    [SerializeField]
    AbilityParticle[] mPre_SpawnOnTeleport;

    public float teleportDelay = 0f;

    public AnimationClip startAnim;
    public float moveSpeedMultiply;
    public AnimationClip moveAnim;
    public AnimationClip endAnim;

    public override List<ILevelCell> Setup(GridUnit InCasterUnit)
    {
        List<ILevelCell> abilityCells = base.Setup(InCasterUnit);

        return abilityCells;
    }

    public override bool CustomExecuteCondition(GridUnit InCasterUnit, GridUnit targetUnit)
    {
        if (targetUnit == null) return false;
        ILevelCell onCell = InCasterUnit.GetCell();

        ILevelCell targetCell = targetUnit.GetCell();

        ILevelCell teleportCell = null;

        GridUnit teleportCellUnit = null;

        if (randomToCellAround)
        {
            List<ILevelCell> randomCellList = targetUnit.GetAllowedMovementCells(KeepDistance + 1);

            if (randomCellList.Count > 0)
            {
                int ranIndex = Random.Range(0, randomCellList.Count);

                targetCell = randomCellList[ranIndex];

                //Debug.Log($"randomToCellAround selected {targetCell.name}");
            }
            else
            {
                targetCell = InCasterUnit.GetCell();
            }

            teleportCell = targetCell;
            teleportCellUnit = teleportCell.GetUnitOnCell();
        }
        else
        {
            List<ILevelCell> cellPath = InCasterUnit.GetPathTo(targetCell, GetSetupCells(InCasterUnit));

            if (targetUnit != null)
            {
                int cellOffset = cellPath.Count - (KeepDistance + 1);

                cellPath = cellPath.GetRange(0, Mathf.Clamp(cellOffset, 0, cellOffset));
            }

            if (cellPath.Count > 1 && cellPath.Contains(onCell))
            {
                cellPath.RemoveAt(0);
            }

            if (cellPath.Count > 0)
            {
                teleportCell = cellPath[cellPath.Count - 1];

                teleportCellUnit = teleportCell.GetUnitOnCell();
            }
            else
            {
                teleportCell = onCell;
            }
        }

        if (teleportCell != null && teleportCellUnit == null || teleportCell != null && teleportCellUnit != null && teleportCellUnit == InCasterUnit)
        {
            return true;
        }

        return false;
    }

    public override List<ILevelCell> GetSetupCells(GridUnit InCasterUnit)
    {
        List<ILevelCell> abilityCells = base.GetSetupCells(InCasterUnit);

        return abilityCells;
    }

    public override IEnumerator Execute(GridUnit InCasterUnit, ILevelCell InTargetCell, UnityEvent OnComplete = null,PlayerAction networkMessage = null)
    {
        Debug.Log("CustomAbility Teleport Execute");

        ILevelCell onCell = InCasterUnit.GetCell();
        ILevelCell targetCell = InTargetCell;
        GridUnit targetUnit = InTargetCell.GetUnitOnCell();

        //Start Check teleport cell conditions
        ILevelCell teleportCell = null;
        GridUnit teleportCellUnit = null;

        if (randomToCellAround)
        {
            List<ILevelCell> randomCellList = targetUnit.GetAllowedMovementCells(KeepDistance + 1);

            //Debug.Log($"randomToCellAround count {randomCellList.Count}");

            if(randomCellList.Count > 0)
            {
                int ranIndex = Random.Range(0, randomCellList.Count);

                targetCell = randomCellList[ranIndex];

                //Debug.Log($"randomToCellAround selected {targetCell.name}");
            }

            teleportCell = targetCell;
            teleportCellUnit = teleportCell.GetUnitOnCell();
        }
        else
        {
            List<ILevelCell> cellPath = InCasterUnit.GetPathTo(targetCell, GetSetupCells(InCasterUnit));

            if (targetUnit != null)
            {
                int cellOffset = cellPath.Count - (KeepDistance + 1);

                cellPath = cellPath.GetRange(0, Mathf.Clamp(cellOffset, 0, cellOffset));
            }
            if (cellPath.Count > 1 && cellPath.Contains(onCell))
            {
                cellPath.RemoveAt(0);
            }

            if (cellPath.Count > 0)
            {
                teleportCell = cellPath[cellPath.Count - 1];

                teleportCellUnit = teleportCell.GetUnitOnCell();
            }
            else
            {
                teleportCell = onCell;
            }
        }
        
        if (teleportCell == null || teleportCellUnit != null && teleportCellUnit != InCasterUnit)
        {
            yield break;
        }

        //End Check teleport cell conditions

        GameManager.AddActionBeingPerformed();

        List<ILevelCell> EffectCellList = new();
        EffectCellList.AddRange(GetEffectedCells(InCasterUnit, InTargetCell));

        if (!EffectCellList.Contains(InTargetCell))
        {
            EffectCellList.Add(InTargetCell);
        }

        foreach (ILevelCell EffectCell in EffectCellList)
            SpawnParticleEffectedCell(InCasterUnit, EffectCell, InTargetCell, mPre_SpawnOnTarget);
        SpawnParticleEffectedCell(InCasterUnit, teleportCell, InTargetCell, mPre_SpawnOnTeleport);
        SpawnParticleSelectedCell(InCasterUnit, InTargetCell, mPre_SpawnOnSelect, mPre_SpawnOnCaster);

        if (startAnim != null)
        {
            InCasterUnit.PlayAnimation(startAnim);

            yield return new WaitForSeconds(startAnim.length);

        }

        if (moveAnim != null)
        {
            InCasterUnit.PlayAnimation(moveSpeedMultiply, moveAnim);

            yield return new WaitForSeconds(moveAnim.length);
        }

        if (teleportCell != onCell)
        {
            yield return new WaitForSeconds(teleportDelay / 4f);
            if(InCasterUnit.gameObject.activeSelf)
                InCasterUnit.GameMonster.ActiveMesh(InCasterUnit.GameMonster.NormalMesh, InCasterUnit.GetTeam() == CustomGameManager.Instance.PlayerSide);
            yield return new WaitForSeconds(teleportDelay * 3 / 4f);

            InCasterUnit.SetCurrentCell(teleportCell);
            teleportCell.SetMaterial(onCell.GetCellState());
            teleportCell.SetCellState(teleportCell.GetCellState());

            onCell.SetMaterial(teleportCell.GetCellState());
            onCell.SetCellState(onCell.GetCellState());

            AilmentHandler.HandleUnitOnCell(InCasterUnit, teleportCell);
        }

        if(InCasterUnit.IsDead == false)
        {
            if (endAnim != null)
            {
                InCasterUnit.PlayAnimation(endAnim);

                yield return new WaitForSeconds(endAnim.length);
            }

            yield return base.Execute(InCasterUnit, InTargetCell, OnComplete,networkMessage);
        }

        GameManager.RemoveActionBeingPerformed();

        Debug.Log("Execute RemoveActionBeingPerformed");
    }
}
