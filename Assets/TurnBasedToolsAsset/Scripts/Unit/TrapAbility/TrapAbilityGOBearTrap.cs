using System.Collections;
using Network;
using UnityEngine;

public class TrapAbilityGOBearTrap : TrapAbilityGO
{
    [SerializeField]
    float executeTime;

    [SerializeField]
    float movementPauseTime;

    [SerializeField]
    AnimationClip executeAnimClip;

    public GameObject bearTrap;

    private bool _active;
    public override void Setup(UnitAbility inUnitAbility, GridUnit inCaster, ILevelCell inTrapCell)
    {
        bearTrap.GetComponent<Animation>()["BearTrapCloseAnim"].speed = 2.0f;
        base.Setup(inUnitAbility, inCaster, inTrapCell);
    }

    protected override void Active(GridObject gridObject)
    {
        if (_active == false && gridObject && AbilityParam.IsEffectTeam(Caster, gridObject.GetCell().GetUnitOnCell(), UnitAbility)) 
        {
            _active = true;

            var gridUnit = gridObject.GetComponent<GridUnit>();

            if (gridUnit)
            {
                gridUnit.SetMovementPauseTime(movementPauseTime);
                
                StartCoroutine(Execute(UnitAbility, Caster, TrapCell));

                Debug.Log($"TrapAbilityGOBearTrap active unit on trap = {gridUnit.name}");
                bearTrap.GetComponent<Animation>().Play("BearTrapCloseAnim");
            }
        }

        base.Active(gridObject);
    }


    protected virtual IEnumerator Execute(UnitAbility unitAbility, GridUnit inCasterUnit, ILevelCell selectedCell)
    {
        GameManager.AddActionBeingPerformed();

        yield return new WaitForSeconds(0.25f);
        SpawnParticleEffectedCell(unitAbility, inCasterUnit, selectedCell);

        if (executeAnimClip != null)
        {
            inCasterUnit.PlayAnimation(executeAnimClip, executeTime, true);
        }

        yield return new WaitForSeconds(executeTime);

        Debug.Log("TrapAbilityGOBearTrap Execute");

        InternalHandleEffectedCell(unitAbility, inCasterUnit, selectedCell, selectedCell);
        GameManager.RemoveActionBeingPerformed();
        
        yield return new WaitForSeconds(0.25f);

        Destroy();
    }
}
