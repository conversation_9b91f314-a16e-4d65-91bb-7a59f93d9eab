using System.Collections.Generic;
using System.Linq;
using Assets.Scripts.Enums;
using Assets.Scripts.Monster;
using Assets.Scripts.Scriptables;
using Assets.Scripts.SerializeDataStruct;
using CustomBattle;
using CustomBattle.BattleMonsterSelect;
using CustomBattle.BattleUIAccuracyRate;
using Network;
using TurnBasedToolsAsset.Scripts.AI;
using UnityEngine;

namespace TurnBasedToolsAsset.Scripts.Gameplay.GameRules
{
    [CreateAssetMenu(fileName = "CustomGameRules", menuName = "TurnBasedTools/GameRules/Create Custom GameRules",
        order = 1)]
    public class CustomGameRules : global::GameRules
    {

        private GridUnit _selectedUnit;

        [SerializeField] private int turnCount = 1;

        public MonsterSelectedGO monsterSelectedGO;
        public MonsterSelectedGO SpawnedSelectedGo { get; private set; }
        
        public void CustomInit()
        {
            var currentRoom = CustomNetworkManager.Instance.GetCurrentRoom();
            if (currentRoom?.State != null)
            {
                if (CustomNetworkManager.Instance.IsReconnected || CustomNetworkManager.Instance.IsSpectator())
                {
                    var turn = (int)currentRoom.State.turnCount;
                    CustomGridManager.Instance.UpdateDeadBound(turn);
                }
                else
                {
                    turnCount = 0;
                    CustomGridManager.Instance.UpdateDeadBound(turnCount);
                }
            }
            else
            {
                turnCount = 0;
                CustomGridManager.Instance.UpdateDeadBound(turnCount);
            }
        }
      
        public void UpdateTurnCount(int turn)
        {
            turnCount = turn;
            CustomGridManager.Instance.UpdateDeadBound(turnCount);
        }

        public override void CustomEndTurnTeam(GameTeam nextTeam)
        {
            //Debug.Log("Custom end turn");

            CustomGridManager.Instance.UpdateDeadBound(turnCount);
        }

        public void CustomCountTurn()
        {
            //Debug.Log("Custom count turn");
            turnCount++;
            if (CustomNetworkManager.Instance.GetRoom() == null) return;
            if (CustomNetworkManager.Instance.IsSpectator()) return;
            CustomNetworkManager.Instance.GetRoom().Send("update_turn", turnCount).GetAwaiter();
        }

        public int GetCurrentTurn()
        {
            return turnCount;
        }

        public void PlaceOneUnit(GameTeam gameTeam, SpawnListInfo spawnInfo, UnitAI unitAI, TerrainsTypeEnum terrainsTypeEnum)
        {
            var unitData = spawnInfo.MonsterInfo.MonsterScriptableData.UnitScriptableData;
            var compassDir = gameTeam switch
            {
                GameTeam.Blue => CompassDir.E,
                GameTeam.Red => CompassDir.W,
                _ => CompassDir.S
            };
            var spawnedUnit = GameManager.SpawnUnit(unitData, spawnInfo.SpawnedModel, gameTeam, spawnInfo.SpawnCell, compassDir);
            spawnedUnit.SetAsTarget(false);
            spawnedUnit.AddAI(unitAI);
            spawnedUnit.SetAIActive(false);

            spawnedUnit.GameMonster.SetMonsterInfo(spawnInfo.MonsterInfo, spawnInfo.FarmInfo,terrainsTypeEnum);
            spawnedUnit.GameMonster.InGameParameter.SetParameters();
        }

        public void PlaceUnits(GameTeam gameTeam, List<SpawnListInfo> spawnListInfos, UnitAI unitAI, TerrainsTypeEnum terrainsTypeEnum)
        {
            foreach (var spawnInfo in spawnListInfos)
            {
                var unitData = spawnInfo.MonsterInfo.MonsterScriptableData.UnitScriptableData;
                var compassDir = gameTeam switch
                {
                    GameTeam.Blue => CompassDir.E,
                    GameTeam.Red => CompassDir.W,
                    _ => CompassDir.S
                };

                var spawnedUnit = GameManager.SpawnUnit(unitData, spawnInfo.SpawnedModel, gameTeam, spawnInfo.SpawnCell, compassDir);
                spawnedUnit.SetAsTarget(false);
                spawnedUnit.AddAI(unitAI);
                spawnedUnit.SetAIActive(false);

                spawnedUnit.GameMonster.SetMonsterInfo(spawnInfo.MonsterInfo, spawnInfo.FarmInfo, terrainsTypeEnum);
                spawnedUnit.GameMonster.InGameParameter.SetParameters();
            }

            CustomGameManager.OnTeamReady?.Invoke(gameTeam);
        }

        public void PlaceNoTurnUnits(GameTeam gameTeam, List<SpawnListInfo> spawnListInfos, UnitAI unitAI, TerrainsTypeEnum terrainsTypeEnum)
        {
            foreach (var spawnInfo in spawnListInfos)
            {
                var unitData = spawnInfo.MonsterInfo.MonsterScriptableData.UnitScriptableData;
                var compassDir = gameTeam switch
                {
                    GameTeam.Blue => CompassDir.E,
                    GameTeam.Red => CompassDir.W,
                    _ => CompassDir.S
                };

                var spawnedUnit = GameManager.SpawnNoTurnUnit(unitData, spawnInfo.SpawnedModel, gameTeam, spawnInfo.SpawnCell, compassDir);
                spawnedUnit.SetAsTarget(false);
                spawnedUnit.AddAI(unitAI);
                spawnedUnit.SetAIActive(false);

                spawnedUnit.GameMonster.SetMonsterInfo(spawnInfo.MonsterInfo, spawnInfo.FarmInfo, terrainsTypeEnum);
                spawnedUnit.GameMonster.InGameParameter.SetParameters();
            }
        }

        private void CleanUpSelectedUnit()
        {
            if (!_selectedUnit) return;
            _selectedUnit.UnBindFromOnMovementComplete(UpdateSelectedHoverObject);
            UnselectUnit();
        }


        private void UpdateSelectedHoverObject()
        {
            if (_selectedUnit == null || _selectedUnit.IsDead)
            {
                if (SpawnedSelectedGo != null)
                {
                    SpawnedSelectedGo.SetUnit(null);
                }

                return;
            }

            if (_selectedUnit != null && _selectedUnit.IsDead == false && SpawnedSelectedGo != null)
            {
                SpawnedSelectedGo.SetUnit(_selectedUnit);
            }

            if (SpawnedSelectedGo == null)
            {
                SpawnedSelectedGo = Instantiate(monsterSelectedGO, _selectedUnit.GetCell().GetAllignPos(_selectedUnit),
                    monsterSelectedGO.transform.rotation);
            }
        }

        public override void Update()
        {
            base.Update();

            if (GameManager.IsActionBeingPerformed())
            {
                return;
            }

            UpdateSelectedHoverObject();

        }

        private void UnselectUnit()
        {
            if (_selectedUnit)
            {
                _selectedUnit.CleanUp();
                _selectedUnit = null;

                GameManager.Get().OnUnitSelected.Invoke(null);

                UpdateSelectedHoverObject();
            }

            GameManager.Get().UpdateHoverCells();
        }

        public override GridUnit GetSelectedUnit()
        {
            return _selectedUnit;
        }

        public override void HandleNumPressed(int inNumPressed)
        {
            if (_selectedUnit)
            {
                _selectedUnit.SetupAbility(inNumPressed - 1);
            }
        }

        public void CustomUnitTurn(GridUnit gridUnit)
        {

            var gameTeam = gridUnit.GetTeam();

            CleanUpSelectedUnit();

            AilmentHandler.HandleTurnStart(gameTeam);

            var parameterInGame = gridUnit.GetComponent<ParameterInGame>();
            parameterInGame.SeftTurn();

            if (gridUnit.GetTeam() == CustomGameManager.Instance.PlayerSide)
            {
                CustomHandlePlayerSelected(gridUnit);
            }
            else
            {
                SetSelectedUnit(gridUnit);
            }

            List<GridUnit> newTurnSetList = new();
            newTurnSetList.AddRange(GameManager.GetUnitsOnTeam(GameTeam.Red));
            newTurnSetList.AddRange(GameManager.GetUnitsOnTeam(GameTeam.Blue));
            foreach (var unit in newTurnSetList)
            {
                unit.GameMonster.InGameParameter.SetTurn();
            }
        }

        public override void EndTeamTurn(GameTeam inTeam)
        {

            AilmentHandler.HandleTurnEnd(inTeam);
        }

        public override void BeginHover(ILevelCell inCell)
        {
            if (CustomGameManager.Instance.IsPlaying == false)
            {
                return;
            }

            if (_selectedUnit != null)
            {
                var unitAbility = _selectedUnit.GetCurrentAbility();

                if (unitAbility != null)
                {

                    SkillDataScriptable skillData;
                    var skillLevel = 1;

                    if (_selectedUnit.GameMonster.InGameParameter.StealSkillInfo != null &&
                        _selectedUnit.GameMonster.InGameParameter.StealSkillInfo.StealSkillDataScriptable != null &&
                        unitAbility == _selectedUnit.GameMonster.InGameParameter.StealSkillInfo.StealSkillDataScriptable
                            .SkillLogic)
                    {
                        skillData = _selectedUnit.GameMonster.InGameParameter.StealSkillInfo.StealSkillDataScriptable;

                        unitAbility = skillData.SkillLogic;

                        skillLevel = _selectedUnit.GameMonster.InGameParameter.StealSkillInfo.SkillLevel;
                    }
                    else
                    {
                        skillData = _selectedUnit.GameMonster.GetSKillScriptable(unitAbility);

                        if (_selectedUnit.GameMonster.MonsterInfo.MonsterSkillLevelDict.TryGetValue(skillData.SkillId,
                                out var skillValue))
                        {
                            skillLevel = skillValue.SkillLevel;
                        }
                    }

                    if (skillData != null)
                    {
                        var abilityCellsInRange = unitAbility.GetAbilityCells(_selectedUnit);

                        if (abilityCellsInRange.Contains(inCell))
                        {
                            UIAccuracyRate.Instance.SetTargetsAccury(skillData, skillLevel, _selectedUnit,
                                GameManager.Get().GetTargetsHover(), inCell);
                            return;
                        }
                    }
                }
            }

            UIAccuracyRate.Instance.SetTargetsAccury(null, 0, null, null, null);
        }

        public override void EndHover(ILevelCell inCell)
        {
        }
        
        private void CustomHandlePlayerSelected(GridUnit inPlayerUnit)
        {
            SetSelectedUnit(inPlayerUnit);

            if (_selectedUnit != null)
            {
                _selectedUnit.BindToOnMovementComplete(UpdateSelectedHoverObject);

                GameManager.Get().OnUnitSelected.Invoke(_selectedUnit);
            }
            else
            {
                SetNextTurn();
            }

            UpdateSelectedHoverObject();
        }


        private static void SetNextTurn()
        {
            CustomGameManager.Instance.SetNextTurn();
        }

        private void SetSelectedUnit(GridUnit inUnit)
        {
            _selectedUnit = inUnit;
        }

        public override void HandleCellSelected(ILevelCell selectedCell, PlayerAction networkMessage = null)
        {
            if (!_selectedUnit)
            {
                return;
            }
            
            if (GameManager.IsActionBeingPerformed() || !selectedCell)
            {
                return;
            }

            var unitOnSelectedCell = selectedCell.GetUnitOnCell();

            var currentState = _selectedUnit.GetCurrentState();

            var allowedMovementCells = _selectedUnit.GetAllowedMovementCells();

            switch (currentState)
            {
                case UnitState.Moving when !unitOnSelectedCell && allowedMovementCells.Contains(selectedCell):
                {
                    var targetMovementCell = GameManager.CalculateMoveToCell(_selectedUnit, selectedCell);

                    _selectedUnit.ExecuteMovement(targetMovementCell);
                    GameManager.Get().CustomEndHover();
                    if (CustomNetworkManager.Instance.GetRoom() == null) return;
                    if (CustomNetworkManager.Instance.IsSpectator())
                    {
                        return;
                    }

                    CustomGameManager.Instance.OnMoveMessage(_selectedUnit, targetMovementCell).GetAwaiter();
                    return;
                }
                case UnitState.UsingAbility:
                {
                    var unitAbility = _selectedUnit.GetCurrentAbility();
                    if (!unitAbility)
                    {
                        return;
                    }

                    if (unitAbility.CustomExecuteCondition(_selectedUnit, unitOnSelectedCell) == false)
                    {
                        GameManager.Get().CustomEndHover();
                        return;
                    }

                    if (unitOnSelectedCell == _selectedUnit && unitAbility.GetRadius() == 0)
                    {
                        _selectedUnit.ExecuteAbility(selectedCell, networkMessage);
                        GameManager.Get().CustomEndHover();

                        return;
                    }

                    var abilityCellsInRange = unitAbility.GetSetupCells(_selectedUnit);
                    
                    var abilityDesignationCells = unitAbility.GetDesignationCells(_selectedUnit);

                    List<ILevelCell> enemyProvokeCells = new();

                    var hoverCells = GameManager.Get().GetTargetsHover();

                    GetEnemyProvokeCells(ref enemyProvokeCells, abilityCellsInRange);

                    var skillData = _selectedUnit.GameMonster.GetSKillScriptable(unitAbility);
                    
                    if (skillData && skillData.ExecutionType != SkillExecutionTypesEnum.Designation)
                    {
                        if (!abilityCellsInRange.Contains(selectedCell))
                        {
                            return;
                        }
                    }

                    if (skillData && skillData.ExecutionType == SkillExecutionTypesEnum.Designation)
                    {
                        if (!abilityDesignationCells.Contains(selectedCell))
                        {
                            return;
                        }
                    }

                    var targetCount = 0;

                    foreach (var unit in from cell in hoverCells
                             select cell.GetUnitOnCell()
                             into unit
                             let unitAbilityDelayTurnUseSkill = unitAbility as UnitAbilityDelayTurnUseSkill
                             where (unit && AbilityParam.IsEffectTeam(_selectedUnit, unit, unitAbility))
                                   || (unit && unitAbilityDelayTurnUseSkill &&
                                       unitAbilityDelayTurnUseSkill.IsDelayOrdered(_selectedUnit) == null &&
                                       unit == _selectedUnit)
                             select unit)
                    {
                        switch (enemyProvokeCells.Count)
                        {
                            case > 0 when
                                unit.GameMonster.InGameParameter.CheckBuffCount(BuffsEnum.Provocation) > 0:
                            case 0:
                                targetCount++;
                                break;
                        }
                    }

                    if (_selectedUnit && hoverCells.Count == 1 && _selectedUnit.GameMonster.InGameParameter.CheckBuffCount(BuffsEnum.Invisibility) > 0)
                    {
                        return;
                    }

                    switch (enemyProvokeCells.Count)
                    {
                        case > 0 when targetCount > 0 && enemyProvokeCells.Contains(selectedCell):
                        {
                            _selectedUnit.ExecuteAbility(selectedCell, networkMessage);
                            
                            break;
                        }
                        case 0 when targetCount > 0:
                        {
                            var unitAbilityDelayTurnUseSkill = unitAbility as UnitAbilityDelayTurnUseSkill;

                            if ((unitAbility.m_selectCellMode == AbilitySelectMode.GridUnitCell && selectedCell.GetUnitOnCell() ) || unitAbility.m_selectCellMode == AbilitySelectMode.Both || unitAbilityDelayTurnUseSkill && unitAbilityDelayTurnUseSkill.IsDelayOrdered(_selectedUnit) == null)
                            {
                                _selectedUnit.ExecuteAbility(selectedCell, networkMessage);
                            }

                            break;
                        }
                        case 0 when targetCount == 0:
                        {
                            if (unitAbility.m_selectCellMode == AbilitySelectMode.EmptyCell && !selectedCell.GetUnitOnCell() || unitAbility.m_selectCellMode == AbilitySelectMode.Both)
                            {
                                _selectedUnit.ExecuteAbility(selectedCell, networkMessage);
                            }

                            break;
                        }
                    }

                    break;
                }
            }

            GameManager.Get().CustomEndHover();
        }

        private void GetEnemyProvokeCells(ref List<ILevelCell> enemyProvokeCells, List<ILevelCell> hoverCellList)
        {
            enemyProvokeCells.AddRange(from cell in hoverCellList
                let unit = cell.GetUnitOnCell()
                where unit != null && unit.GetTeam() != _selectedUnit.GetTeam() &&
                      unit.GameMonster.InGameParameter.CheckBuffCount(BuffsEnum.Provocation) > 0
                select cell);
        }

        public override void OnMovePosition(GridUnit gridUnit, int cellDistance)
        {

            var parameterInGame = gridUnit.GetComponent<ParameterInGame>();

            var staminaCost = MonsterBattle.Get_Move_Stamina_Cost(cellDistance);

            parameterInGame.AddStamina(-staminaCost);
        }

        public override void HandleTeamWon(GameTeam inTeam)
        {
            UnselectUnit();
            GameManager.Get().HandleGameComplete();
        }

        public override void HandleSTChanged(int staminaPoint)
        {
        }

    }
}